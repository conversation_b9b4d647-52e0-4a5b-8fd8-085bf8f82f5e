import request from '@/router/axios'


// 人效--项目情况
export function queryProgramAuditor(data) {
    return request({
        url: '/rbi/deviceFact/queryDeviceCustomerByDate',
        method: 'post',
        data,
    })
}
// 人效--项目情况--导出
export function exportProgramAuditor(data) {
    return request({
        url: '/rbi/deviceFact/exportDeviceCustomerByDate',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}

// 人效--总体情况
export function queryTotalyAuditor(data) {
    return request({
        url: '/rbi/auditor/queryTotalyAuditor',
        method: 'post',
        data,
    })
}
// 人效--总体情况--导出
export function exportTotalyAuditor(data) {
    return request({
        url: '/rbi/auditor/exportTotalyAuditor',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}

// 人效--每人当天处理情况
export function queryDayAuditor(data) {
    return request({
        url: '/rbi/auditor/queryDayAuditor',
        method: 'post',
        data,
    })
}
// 人效--每人当天处理情况--导出
export function exportDayAuditor(data) {
    return request({
        url: '/rbi/auditor/exportDayAuditor',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}

// 人效--每小时工作负荷
export function queryHourAuditor(data) {
    return request({
        url: '/rbi/auditor/queryHourAuditor',
        method: 'post',
        data,
    })
}
// 人效--每小时工作负荷--导出
export function exportHourAuditor(data) {
    return request({
        url: '/rbi/auditor/exportHourAuditor',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}