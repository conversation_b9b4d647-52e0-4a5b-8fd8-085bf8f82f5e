import axiosApi from '@/router/axios'
import fileDownload from 'js-file-download'

export default {
  // 查询全部工单（整合erp）
  orderAll(params) {
    return axiosApi({
      url: `/erp/order/all`,
      method: 'get',
      params,
    })
  },

  // 查询工单编号（整合erp）
  number() {
    return axiosApi({
      url: `/erp/order/getBizKey`,
      method: 'get',
    })
  },

  // 查询所有项目
  projectAll() {
    return axiosApi({
      url: `/dmerp-base/project/all`,
      method: 'get',
    })
  },
  // 查询所有设备类型
  deviceAllType() {
    return axiosApi({
      url: `/dmerp-base/device/allType`,
      method: 'get',
    })
  },
  // 查询所有IMEI
  deviceAllImei() {
    return axiosApi({
      url: `/dmerp-base/device/allImei`,
      method: 'get',
    })
  },
  // 查询所有指派人
  orderAllUser() {
    return axiosApi({
      url: `/dmerp-base/order/allUser`,
      method: 'get',
    })
  },
  // 处理角色(权限的角色)
  orderAllList() {
    return axiosApi({
      url: `/admin/role/list`,
      method: 'get',
    })
  },

  //获取全部操作人信息
  allUser() {
    return axiosApi({
      url: `/admin/user/getAll`,
      method: 'get',
    })
  },

  // 根据imei-车牌号获取工单基本信息(整合erp)
  getBaseInfo(params) {
    return axiosApi({
      url: `/erp/order/getBaseInfo`,
      method: 'get',
      params,
    })
  },

  // 新建运维工单（整合erp）
  orderCreate(data) {
    return axiosApi({
      url: `/erp/order/create`,
      method: 'post',
      data,
    })
  },

  // 编辑工单（整合erp）
  orderUpdate(data) {
    return axiosApi({
      url: `/erp/order/update`,
      method: 'post',
      data,
    })
  },

  // 处理运维工单（整合erp）
  orderDeal(data) {
    return axiosApi({
      url: `/erp/order/deal`,
      method: 'post',
      data,
    })
  },

  // // 删除工单 （整合erp）运维工单
  orderDelete(id) {
    return axiosApi({
      url: `/erp/order/${id}`,
      method: 'delete',
    })
  },

  // 批量删除工单(整合erp)
  orderBatchDelete(data) {
    return axiosApi({
      url: `/erp/order/batchDelete`,
      method: 'post',
      data,
    })
  },

  // 工单导入模板下载
  // orderExceltemplate(name) {
  //   return axiosApi({
  //     url: `/dmerp-base/order/excel/template`,
  //     method: 'get',
  //     responseType: 'blob',
  //   }).then(res => {
  //     fileDownload(res.data, `${name}.xlsx`)
  //   })
  // },

  //运维工单导入模板下载 模版下载（整合erp）
  async orderExceltemplate(name) {
    return axiosApi({
      url: `fleet/attachment/downloadTemplateFile?fileName=order.xlsx`,
      method: 'get',
      responseType: 'blob',
    }).then(res => {
      fileDownload(res.data, `${name}.xlsx`)
    })
  },

  // 运维工单查询审核记录
  queryOperationReviewRecord(data) {
    return axiosApi({
      url: `/dmerp-base/orderReportInfo/getAuditHistory`,
      method: 'post',
      data,
    })
  },
  // 运维工单查询审核记录
  operationReview(data) {
    return axiosApi({
      url: `/dmerp-base/orderReportInfo/modify`,
      method: 'post',
      data,
    })
  },

  // 运维工单导入
  // upIntroduction(data) {
  //   return axiosApi({
  //     url: `/dmerp-base/order/import`,
  //     method: 'post',
  //     data,
  //   })
  // },

  // 运维工单导入(整合erp)
  upIntroduction(data) {
    return axiosApi({
      url: `/erp/order/import`,
      method: 'post',
      data,
    })
  },

  // 导入工单校验
  // upValid(data) {
  //   return axiosApi({
  //     url: `/dmerp-base/order/valid`,
  //     method: 'post',
  //     data,
  //   })
  // },

  // 导入工单校验
  upValid(data) {
    return axiosApi({
      url: `/erp/order/valid`,
      method: 'post',
      data,
    })
  },

  // 导入进度查询
  upStatus(params) {
    return axiosApi({
      url: `/dmerp-base/order/importStatus/${params}`,
      method: 'get',
    })
  },

  // 通过id查询工单详情
  // orderDetail(id) {
  //   return axiosApi({
  //     url: `/dmerp-base/order/detail/${id}`,
  //     method: 'get',
  //   })
  // },

  // 通过id查询工单详情(整合erp)
  orderDetail(id) {
    return axiosApi({
      url: `/erp/order/detail/${id}`,
      method: 'get',
    })
  },

  //新增供应商维修记录
  orderRepairCreate(data) {
    return axiosApi({
      url: `/dmerp-base/orderRepair/create`,
      method: 'post',
      data,
    })
  },

  //删除供应商维修记录
  orderRepairDlete(id) {
    return axiosApi({
      url: `/dmerp-base/orderRepair/delete/${id}`,
      method: 'delete',
    })
  },
  //修改供应商维修记录
  orderRepairUpdate(data) {
    return axiosApi({
      url: `/dmerp-base/orderRepair/update`,
      method: 'post',
      data,
    })
  },
  //工单导出
  // orderExport(closePage, params, name) {
  //   return axiosApi({
  //     url: `/dmerp-base/order/export?closedPage=${closePage}`,
  //     method: 'get',
  //     params,
  //     responseType: 'blob',
  //     timeout: 60 * 60 * 1000,
  //   }).then(res => {
  //     fileDownload(res.data, `${name}.xlsx`);
  //   });
  // },
  orderExport(closePage, params) {
    return axiosApi({
      url: `/dmerp-base/order/export?closedPage=${closePage}`,
      method: 'get',
      params,
    })
  },

  // 运维工单-- 关闭工单
  // closeOrder(data) {
  //   return axiosApi({
  //     url: `/dmerp-base/order/close?id=${data.id}&closeReason=${data.closeReason}`,
  //     method: 'post',
  //     data,
  //   })
  // },

  // 运维工单-- 关闭工单
  closeOrder(data) {
    return axiosApi({
      url: `/erp/order/close`,
      method: 'post',
      data,
    })
  },

  // exportWookPool(params, name) {
  //   return axiosApi({
  //     url: `/dmerp-base/order/pool/export`,
  //     method: 'get',
  //     params,
  //     responseType: 'blob',
  //     timeout: 60 * 60 * 1000,
  //   }).then(res => {
  //     fileDownload(res.data, `${name}.xlsx`);
  //   });
  // },
  exportWookPool(params) {
    return axiosApi({
      url: `/dmerp-base/order/pool/export`,
      method: 'get',
      params,
    })
  },
  // 获取设备挂靠的项目
  project_device_projectList(imei) {
    return axiosApi({
      url: `/dmerp-base/project-device/projectList`,
      params: { imei },
    })
  },

  //获取物料编码、物料名称
  getMaterialTypes(params) {
    return axiosApi({
      url: `/dmerp-base/installDemand/getTypes`,
      method: 'get',
      params,
    })
  },

  //分页查询安装需求
  listNeedPages(data) {
    return axiosApi({
      url: `/dmerp-base/installDemand/listPage`,
      method: 'post',
      data,
    })
  },

  //导出安装需求
  exportByInstallDemand(params) {
    return axiosApi({
      url: `/dmerp-base/installDemand/export`,
      method: 'get',
      params,
    })
  },

  //下载需求补充模板
  needExceltemplate(type = 0, name) {
    return axiosApi({
      url: `/dmerp-base/installDemand/template/${type}`,
      method: 'get',
      responseType: 'blob',
    }).then(res => {
      fileDownload(res.data, `${name}.xlsx`)
    })
  },

  //需求模板数据校验
  batchUpdateValid(data) {
    const fd = new FormData()
    fd.append('file', data)
    return axiosApi({
      url: `/dmerp-base/installDemand/batchUpdateValid`,
      method: 'post',
      data: fd,
    })
  },

  //需求模板数据导入
  batchUpdate(data) {
    const fd = new FormData()
    fd.append('file', data)
    return axiosApi({
      url: `/dmerp-base/installDemand/batchUpdate`,
      method: 'post',
      data: fd,
    })
  },

  //安装需求详情
  getNeedDetail(params) {
    return axiosApi({
      url: `/dmerp-base/installDemand/getDetailByOrderNo`,
      method: 'get',
      params,
    })
  },

  //导出出库单详情 /dmerp-base/outboundOrderDetail/detailExport
  exportOutboundOrderDetail(params) {
    return axiosApi({
      url: `/dmerp-base/outboundOrderDetail/detailExport`,
      method: 'get',
      params,
    })
  },

  //安装需求修改
  updateNeedDetail(data) {
    return axiosApi({
      url: `/dmerp-base/installDemand/updateById`,
      method: 'post',
      data,
    })
  },

  //收集
  startCollect(params) {
    return axiosApi({
      url: `/dmerp-base/installDemand/startCollect`,
      method: 'get',
      params,
    })
  },

  //完成收集
  finishCollect(data) {
    return axiosApi({
      url: `/dmerp-base/installDemand/finishCollect`,
      method: 'post',
      data,
    })
  },

  //根据订单查询发货信息 /dmerp-base/outboundOrder/detailList
  queryDetailList(data) {
    return axiosApi({
      url: `/dmerp-base/outboundOrder/detailList`,
      method: 'post',
      data,
    })
  },

  //收集界面查询订单安装详情
  getInstallCollectByOrderNo(params) {
    return axiosApi({
      url: `/dmerp-base/installCollect/getInstallCollectByOrderNo`,
      method: 'get',
      params,
    })
  },

  //新增安装补充信息
  batchSaveInstall(data) {
    return axiosApi({
      url: `/dmerp-base/installCollect/batchSave`,
      method: 'post',
      data,
    })
  },

  //删除安装补充信息
  batchDeleteInstall(params) {
    return axiosApi({
      url: `/dmerp-base/installCollect/batchDelete`,
      method: 'get',
      params,
    })
  },

  //修改安装补充信息
  batchModifyInstall(data) {
    return axiosApi({
      url: `/dmerp-base/installCollect/batchModify`,
      method: 'post',
      data,
    })
  },

  getNetworkOrderList(data) {
    return axiosApi({
      url: `/dmerp-base/installDemand/listPage`,
      method: 'post',
      data,
    })
  },
  //入网工单 已处理-批量编辑模板下载
  networkOrderRecordSynchronizationtemplate(type = 2, name) {
    return axiosApi({
      url: `/dmerp-base/networkOrder/template/${type}`,
      method: 'get',
      responseType: 'blob',
    }).then(res => {
      fileDownload(res.data, `${name}.xlsx`)
    })
  },
  //入网工单 已处理-批量编辑模板校验
  networkOrderRecordSynchronizationValid(data) {
    const fd = new FormData()
    fd.append('file', data)
    return axiosApi({
      url: `/dmerp-base/networkOrder/batchUpdateValid`,
      method: 'post',
      data: fd,
    })
  },
  //入网工单 已处理-批量编辑
  networkOrderRecordSynchronization(data) {
    const fd = new FormData()
    fd.append('file', data)
    return axiosApi({
      url: `/dmerp-base/networkOrder/batchUpdate`,
      method: 'post',
      data: fd,
    })
  },
  //入网工单 操作更新记录
  getUpdateLogByBizKey(params) {
    return axiosApi({
      url: `/dmerp-base/networkOrder/getUpdateHistoryByBizKey`,
      method: 'get',
      params,
    })
  },
  //入网工单 查询跟进记录
  networkOrderRecordQuery(params) {
    return axiosApi({
      url: `/dmerp-base/networkOrderRecord/getRecodeList`,
      method: 'get',
      params,
    })
  },
  //入网工单 新增跟进记录
  addNetworkOrderList(data) {
    return axiosApi({
      url: `/dmerp-base/networkOrderRecord/addRecode`,
      method: 'post',
      data,
    })
  },
  //入网工单 批量跟进记录
  addRecodeBatchOrdereBatch(data) {
    return axiosApi({
      url: `/dmerp-base/networkOrderRecord/addRecodeBatch`,
      method: 'post',
      data,
    })
  },
  //入网工单 删除跟进记录
  networkOrderRecordDetele(params) {
    return axiosApi({
      url: `/dmerp-base/networkOrderRecord/deleteRecode`,
      method: 'get',
      params,
    })
  },
  //入网工单 编辑跟进记录
  networkOrderRecordEdit(data) {
    return axiosApi({
      url: `/dmerp-base/networkOrderRecord/editRecode`,
      method: 'post',
      data,
    })
  },

  //下载入网工单模板
  networkOrderExceltemplate(type = 0, name) {
    return axiosApi({
      url: `/dmerp-base/networkOrder/template/${type}`,
      method: 'get',
      responseType: 'blob',
    }).then(res => {
      fileDownload(res.data, `${name}.xlsx`)
    })
  },

  //入网工单数据校验
  networkOrderBatchUpdateValid(data) {
    const fd = new FormData()
    fd.append('file', data)
    return axiosApi({
      url: `/dmerp-base/networkOrder/batchCreateValid`,
      method: 'post',
      data: fd,
    })
  },

  //入网工单数据导入
  networkOrderBatchUpdate(data) {
    const fd = new FormData()
    fd.append('file', data)
    return axiosApi({
      url: `/dmerp-base/networkOrder/batchCreate`,
      method: 'post',
      data: fd,
    })
  },

  //入网结果反馈数据校验
  networkOrderbatchUpdateResultValid(data) {
    const fd = new FormData()
    fd.append('file', data)
    return axiosApi({
      url: `/dmerp-base/networkOrder/batchUpdateResultValid`,
      method: 'post',
      data: fd,
    })
  },

  //入网结果反馈数据导入
  networkOrderbatchUpdateResult(data) {
    const fd = new FormData()
    fd.append('file', data)
    return axiosApi({
      url: `/dmerp-base/networkOrder/batchUpdateResult`,
      method: 'post',
      data: fd,
    })
  },

  //入网分页查询
  networkOrderListPage(data) {
    return axiosApi({
      url: `/dmerp-base/networkOrder/listPage`,
      method: 'post',
      data,
    })
  },

  //删除入网工单
  deleteNetworkOrderById(data) {
    return axiosApi({
      url: `/dmerp-base/networkOrder/deleteById`,
      method: 'post',
      data,
    })
  },

  //修改入网工单-已处理
  updateByBizKey(data) {
    return axiosApi({
      url: `/dmerp-base/networkOrder/updateByBizKey`,
      method: 'post',
      data,
    })
  },
  //修改入网工单-未处理
  updateById(data) {
    return axiosApi({
      url: `/dmerp-base/networkOrder/updateById`,
      method: 'post',
      data,
    })
  },

  //新增入网工单
  addByBizKey(data) {
    return axiosApi({
      url: `/dmerp-base/networkOrder/add`,
      method: 'post',
      data,
    })
  },

  //导出入网工单 /dmerp-base/networkOrder/detailExport
  exportNetworkOrder(params) {
    return axiosApi({
      url: `/dmerp-base/networkOrder/export`,
      method: 'get',
      params,
    })
  },

  //查询历史工单
  getHistoryByCarNo(params) {
    return axiosApi({
      url: `/dmerp-base/networkOrder/getHistoryByCarNo`,
      method: 'get',
      params,
    })
  },

  //crm项目查询
  getSourceList(params) {
    return axiosApi({
      url: `/dmerp-base/sim/projectCrmIdMapped/listAll`,
      method: 'get',
      params,
    })
  },

  //---------------------------------------- 发货通知单 ---------------------------------------------------------------
  //通知单列表查询
  shippingNoticeListPage(data) {
    return axiosApi({
      url: `/dmerp-base/shippingNotice/listPage`,
      method: 'post',
      data,
    })
  },

  //删除预约单
  deleteAppointmentFormById(params) {
    return axiosApi({
      url: `/dmerp-base/shippingNotice/deleteAppointmentFormById`,
      method: 'get',
      params,
    })
  },

  //发货单详情
  getDetailById(params) {
    return axiosApi({
      url: `/dmerp-base/shippingNotice/getDetailById`,
      method: 'get',
      params,
    })
  },

  //出货单导出
  shippingNoticeExport(params) {
    return axiosApi({
      url: `/dmerp-base/shippingNotice/export`,
      method: 'get',
      params,
    })
  },

  //批量下载预约单
  downloadAppointmentFormById(params) {
    return axiosApi({
      url: `/dmerp-base/shippingNotice/downloadAppointmentFormById`,
      method: 'get',
      params,
    })
  },

  //导出发货单详情发货信息
  exportByNoticeOrderNo(params) {
    return axiosApi({
      url: `/dmerp-base/shippingNoticeDetail/exportByNoticeOrderNo`,
      method: 'get',
      params,
    })
  },

  //下载发货信息模板
  shippingNoticeExceltemplate(type = 0, name) {
    return axiosApi({
      url: `/dmerp-base/shippingNoticeDetail/template/${type}`,
      method: 'get',
      responseType: 'blob',
    }).then(res => {
      fileDownload(res.data, `${name}.xlsx`)
    })
  },

  //发货信息数据校验
  shippingNoticeBatchUpdateValid(data) {
    const fd = new FormData()
    fd.append('file', data)
    return axiosApi({
      url: `/dmerp-base/shippingNoticeDetail/batchCreateValid`,
      method: 'post',
      data: fd,
    })
  },

  //发货信息数据导入
  shippingNoticeBatchCreate(data) {
    const fd = new FormData()
    fd.append('file', data)
    return axiosApi({
      url: `/dmerp-base/shippingNoticeDetail/batchCreate`,
      method: 'post',
      data: fd,
    })
  },

  //发货通知单跟进记录查询
  listByShippingNoticeNo(params) {
    return axiosApi({
      url: `/dmerp-base/shippingNoticeRecord/listByShippingNoticeNo`,
      method: 'get',
      params,
    })
  },
  //发货通知单跟进记录修改
  updateByShippingNoticeNo(data) {
    return axiosApi({
      url: `/dmerp-base/shippingNoticeRecord/update`,
      method: 'post',
      data,
    })
  },
  //发货通知单跟进记录新增
  addShippingNoticeNo(data) {
    return axiosApi({
      url: `/dmerp-base/shippingNoticeRecord/add`,
      method: 'post',
      data,
    })
  },
  //发货通知单跟进记录删除
  deleteByShippingNoticeNo(data) {
    return axiosApi({
      url: `/dmerp-base/shippingNoticeRecord/delete`,
      method: 'post',
      data,
    })
  },

  //跟进原因列表
  getReasonList(params) {
    return axiosApi({
      url: `/dmerp-base/shippingNoticeRecord/getReasonList`,
      method: 'get',
      params,
    })
  },

  //发货通知单详情设备批量删除
  deleteBatchNoticeDetailById(params) {
    return axiosApi({
      url: `/dmerp-base/shippingNoticeDetail/deleteBatchById`,
      method: 'get',
      params,
    })
  },

  //发货通知单详情Tab页面分页查询
  shippingNoticeDetailList(data) {
    return axiosApi({
      url: `/dmerp-base/shippingNoticeDetail/listPage`,
      method: 'post',
      data,
    })
  },

  //发货通知单详情页面导出
  exportShippingNoticeDetail(params) {
    return axiosApi({
      url: `/dmerp-base/shippingNoticeDetail/export`,
      method: 'get',
      params,
    })
  },

  //安装收集信息模板
  installCollectExceltemplate(type = 0, name) {
    return axiosApi({
      url: `/dmerp-base/installCollect/template/${type}`,
      method: 'get',
      responseType: 'blob',
    }).then(res => {
      fileDownload(res.data, `${name}.xlsx`)
    })
  },

  //安装收集信息校验
  installCollectBatchUpdateValid(data, orderNo) {
    const fd = new FormData()
    fd.append('file', data)
    fd.append('orderNo', orderNo)
    return axiosApi({
      url: `/dmerp-base/installCollect/batchSaveByFileValid`,
      method: 'post',
      data: fd,
    })
  },

  //安装收集信息导入
  installCollectBatchUpdate(data, orderNo) {
    const fd = new FormData()
    fd.append('file', data)
    fd.append('orderNo', orderNo)
    return axiosApi({
      url: `/dmerp-base/installCollect/batchSaveByFile`,
      method: 'post',
      data: fd,
    })
  },

  // App版本管理查询所有版本型号信息
  equipmentModelList(data) {
    return axiosApi({
      url: `/dmerp-deepview/deepviewApp/equipmentModelList`,
      method: 'post',
      data,
    })
  },

  // 安装搜集批量删除
  installDemandBatchDel(data) {
    return axiosApi({
      url: `/dmerp-base/installDemand/batchDelete`,
      method: 'post',
      data,
    })
  },

  // 安装收集任务统计
  installDemandStatistics(params) {
    return axiosApi({
      url: `/dmerp-base/installDemand/getStatistics`,
      method: 'get',
      params,
    })
  },

  //查询Crm项目
  getCrmProjectList(params) {
    return axiosApi({
      url: `/dmerp-base/salesorder/getCrmProjectList`,
      method: 'get',
      params,
    })
  },

  //查询erp项目
  getErpProjectList(params) {
    return axiosApi({
      url: `/dmerp-base/project/allByUser`,
      method: 'get',
      params,
    })
  },

  //发货统计
  shippingStatistics(params) {
    return axiosApi({
      url: `/dmerp-base/shippingNotice/statistics`,
      method: 'get',
      params,
    })
  },
  //获取省市区
  getAreaTreeList(params) {
    return axiosApi({
      url: `/dmerp-base/sysArea/allAreaTree`,
      method: 'get',
      params,
    })
  },

  //单卡管理 卡商获取
  simSorce() {
    return axiosApi({
      url: `/admin/dict/type/sim_source`,
      method: 'get',
    })
  },
}
