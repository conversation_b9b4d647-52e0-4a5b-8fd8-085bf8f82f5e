import axiosApi from '@/router/axios'
import request from '@/router/axios'
import fileDownload from 'js-file-download'

export default {
  // //查询客户下一级数据
  // customerChildren(params) {
  //   return axiosApi({
  //     url: `/dmerp-base/customer/children`,
  //     method: 'get',
  //     params,
  //   });
  // },

  // 获取待我处理的工单列表(整合erp) 运维工作台 待我处理列表分页
  getToDeal(params) {
    return request({
      url: `/erp/order/toDeal`,
      method: 'get',
      params,
    })
  },

  // 获取工单池列表（整合erp）运营工作台 工单池-分页查询 和 已关闭-分页查询
  getPool(params) {
    return request({
      url: `/erp/custOrder/pool`,
      method: 'get',
      params,
    })
  },

  // 运营工作台-待我处理的(条件、分页) (整合erp)
  Operating(params) {
    return request({
      url: '/erp/custOrder/toDeal',
      method: 'get',
      params,
    })
  },

  //获取工单池列表（整合erp）运维工作台 工单池-分页查询 和 已关闭-分页查询
  getOrderPool(params) {
    return request({
      url: `/erp/order/pool`,
      method: 'get',
      params,
    })
  },

  // 运维工作台-工单池-领取工单(整合erp)
  receive(id) {
    return request({
      url: `/erp/order/receive/${id}`,
      method: 'get',
    })
  },

  // 运维工作台-工单池-批量领取工单 (整合erp)
  receive_batch(ids) {
    return request({
      url: `/erp/order/receive/batch`,
      method: 'post',
      data: ids,
    })
  },

  // 运营工作台-工单池-领取工单（整合erp）
  OperatingReceive(params) {
    return request({
      url: `/erp/custOrder/receive/${params.id}`,
      method: 'get',
    })
  },

  // 运营工作台-工单池-批量领取工单（整合erp）
  OperatingReceiveBatch(data) {
    return request({
      url: `/erp/custOrder/receive/batch`,
      method: 'post',
      data,
    })
  },

  // exportWookPool(params, name) {
  //   return axiosApi({
  //     url: `/dmerp-base/custOrder/pool/export`,
  //     method: 'get',
  //     params,
  //     responseType: 'blob',
  //     timeout: 60 * 60 * 1000,
  //   }).then(res => {
  //     fileDownload(res.data, `${name}.xlsx`);
  //   });
  // },
  exportWookPool(params) {
    return axiosApi({
      url: `/dmerp-base/custOrder/pool/export`,
      method: 'get',
      params,
    })
  },
}
