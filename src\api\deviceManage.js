import request from '@/router/axios'

// 设备信息管理
export function queryDeviceInfoPage(params) {
    return request({
        url: '/fleet/device/pageList',
        method: 'get',
        params
    })
}


// 查询用户根节点组织及车辆统计数
export function queryOwnerDeviceCount(data) {
    return request({
        url: '/fleet/devicestatus/queryOwnerDeviceCount',
        method: 'post',
        data,
    })
}

// 查询下层组织节点及车辆统计数
export function queryDownDeviceCount(params) {
    return request({
        url: '/fleet/devicestatus/queryDownDeviceCount',
        method: 'get',
        params,
    })
}

// 查询组织下的车辆详情
export function queryStatusList(params) {
    return request({
        url: '/fleet/devicestatus/queryStatusList',
        method: 'get',
        params,
    })
}