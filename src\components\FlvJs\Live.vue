<template>
  <div :id="div_id" style="height: 100%"></div>
</template>

<script lang="ts">
import { ref, shallowRef, computed, watch, getCurrentInstance, onMounted, onBeforeUnmount } from 'vue';
import flvjs from 'flv.js';
import DPlayer from 'dplayer';

import { useInterval, useTimeout } from '@/refs';

let DIV_ID = 0;

export default {
  props: {
    init_src: {
      type: String,
      required: true,
    },
  },
  emits: {
    create_player() {},
    decode_frame() {},
  },
  setup(props, ctx) {
    const { init_src } = props;
    const div_id = 'flvjs_live_' + DIV_ID++;

    const dp = shallowRef<DPlayer>();
    const fp = shallowRef<flvjs.Player>();

    const decode_frame_interval = useInterval();

    onMounted(() => {
      // const video_ele = insp.$refs.video_ele as HTMLVideoElement;
      // const flvjs_player = flvjs.createPlayer({ url: init_src, type: 'flv' }, { enableStashBuffer: false });
      // flvjs_player.attachMediaElement(video_ele);
      create_player();
      onBeforeUnmount(() => {
        dp.value?.destroy();
      });
    });

    return { div_id };

    function create_player() {
      dp.value?.destroy();
      dp.value = new DPlayer({
        container: document.getElementById(div_id),
        screenshot: true,
        preload: 'auto',
        live: true,
        volume: 0,
        mutex: false,
        autoplay: true,
        video: {
          url: init_src,
          type: 'customFlv',
          customType: {
            customFlv: function (video: any, player: any) {
              const flvjs_player = flvjs.createPlayer({ type: 'flv', url: video.src, isLive: true }, { enableStashBuffer: false });
              fp.value = flvjs_player;
              flvjs_player.attachMediaElement(video);
              flvjs_player.load();
              player.events.on('destroy', () => {
                if ((flvjs_player as any).e) {
                  flvjs_player.unload();
                  flvjs_player.detachMediaElement();
                  flvjs_player.destroy();
                }
              });
            },
          },
        },
      });

      ctx.emit('create_player');

      // let videoEl = dp.video as HTMLVideoElement;
      // let endTime1 = 0;
      // let endTime2 = 0;
      // player_buffer_interval.set(() => {
      //   if (videoEl.buffered.length > 0) {
      //     const end = videoEl.buffered.end(0); // 视频结尾时间
      //     const current = videoEl.currentTime; //  视频当前时间
      //     const diff = end - current; // 相差时间
      //     if (endTime1 != end) {
      //       endTime2 = endTime1;
      //       endTime1 = end;
      //     }
      //     if (diff > end - endTime2 + 3) {
      //       console.log('跳帧');
      //       // if (!this.list[playerIndex].isPause) {
      //       if (!videoEl.paused) {
      //         // let num = that.streamType == 1 ? 1 : 2;
      //         videoEl.currentTime = endTime2 - 1;
      //       }
      //     }
      //   }
      // }, 1000);

      // let video_current = -1;
      // retry_test_interval.set(() => {
      //   if (video_current === videoEl.currentTime) {
      //     createPlayer();
      //   } else {
      //     video_current = videoEl.currentTime;
      //   }
      // }, 5e3);

      const flvjs_player = fp.value!;
      let [decode_frame_a, decode_frame_b] = [0, 0];
      flvjs_player.on(flvjs.Events.STATISTICS_INFO, e => {
        // if (!!e.decodedFrames) loading.value = false;
        // console.log(decode_frame_a, decode_frame_b, e)
        decode_frame_a = decode_frame_b;
        decode_frame_b = e.decodedFrames;
      });
      decode_frame_interval.set(() => {
        if (decode_frame_a !== decode_frame_b) {
          ctx.emit('decode_frame');
          return;
        }
        create_player();
      }, 5e3);
    }
  },
};
</script>

<style>
.dplayer-controller .dplayer-icons .dplayer-volume {
  display: none !important;
}
</style>
