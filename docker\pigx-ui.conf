server {
    listen 80;
    server_name localhost;

    gzip on;
    gzip_static on;     # 需要http_gzip_static_module 模块
    gzip_min_length 1k;
    gzip_comp_level 4;
    gzip_proxied any;
    gzip_types text/plain text/xml text/css;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.(?!.*SV1)";

    # 前端打包好的dist目录文件
    root /data/;

    # 若新增后端路由前缀注意在此处添加（|新增）
    location ~* ^/(code|auth|admin|monitor|gen|job|tx|act|mp|pay) {
       proxy_pass http://pigx-gateway:9999;
       proxy_connect_timeout 15s;
       proxy_send_timeout 15s;
       proxy_read_timeout 15s;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto http;
    }
}
