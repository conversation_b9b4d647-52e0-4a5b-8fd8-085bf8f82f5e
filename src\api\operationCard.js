import request from '@/router/axios'

export function stepState(params) {
    return request({
        url: '/fleet/opScreen/stepStat',
        method: 'get',
        params
    })
}

export function deviceManage(params) {
    return request({
        url: '/fleet/opScreen/deviceManage',
        method: 'get',
        params,
    })
}

export function deviceDetailList(params) {
    return request({
        url: '/fleet/opScreen/deviceDetailList',
        method: 'get',
        params,
    })
}

export function alarmStat(params) {
    return request({
        url: '/fleet/opScreen/alarmStat',
        method: 'get',
        params,
    })
}

export function defendStat(params) {
    return request({
        url: '/fleet/opScreen/defendStat',
        method: 'get',
        params,
    })
}

// 报警明细列表
export function alarmDetailList(params) {
    return request({
        url: '/fleet/opScreen/alarmDetailList',
        method: 'get',
        params,
    })
}

// 【设备报警】报警判断量的明细列表 
export function alarmJudgeDetailList(params) {
    return request({
        url: '/fleet/opScreen/alarmJudgeDetailList',
        method: 'get',
        params,
    })
}

// 【设备报警】AI+人工双重审核后报警准确率的明细列表（质检工单）
export function inspectDetailList(params) {
    return request({
        url: '/fleet/opScreen/inspectDetailList',
        method: 'get',
        params,
    })
}

// 报警id查询报警详情
export function alarmDetailOne(params) {
    return request({
        url: '/fleet/opScreen/alarmDetailOne',
        method: 'get',
        params,
    })
}

// 报警id查询质检工单详情
export function inspectDetailOne(params) {
    return request({
        url: '/fleet/opScreen/inspectDetailOne',
        method: 'get',
        params,
    })
}

// 事故统计接口
export function accidentStat(params) {
    return request({
        url: '/fleet/opScreen/accidentStat',
        method: 'get',
        params,
    })
}

// 事故管理明细
export function accidentDetailList(params) {
    return request({
        url: '/fleet/opScreen/accidentDetailList',
        method: 'get',
        params,
    })
}

// 车辆（设备）数据统计
export function deviceStepStat(params) {
    return request({
        url: '/fleet/opScreen/deviceStepStat',
        method: 'get',
        params,
    })
}

// 设备管理-行驶里程明细
export function trackList(params) {
    return request({
        url: '/fleet/opScreen/trackList',
        method: 'get',
        params,
    })
}

