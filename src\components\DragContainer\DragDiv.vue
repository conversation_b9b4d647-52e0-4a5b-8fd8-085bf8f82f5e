<template>
  <div v-show="visible"
       ref="callDiv"
       class="call-container"
       :style="{'width':itemWidth+'px','left':left+'px','top':top+'px'}">
    <div class="title-div">
      <span>{{ title }}</span>
      <i class="el-icon-close"
         @click="handleClose"></i>
    </div>
    <slot name="content"></slot>
  </div>
</template>

<script>
import { setInterval, clearInterval } from "timers";

export default {
  props: {
    visible: Boolean,
    title: String,
    itemWidth: {
      type: Number,
      default: 360
    },
    posRight: {
      type: Number,
      default: 80
    },
    posTop: {
      type: Number,
      default: 300
    },
  },
  data() {
    return {
      moveTimer: null,
      currentTop: 0,
      left: 0,
      top: 0
    };
  },
  created() {
    this.left = document.documentElement.clientWidth - this.itemWidth - this.posRight;
    this.top = this.posTop
  },
  mounted() {
    this.listenDragDiv();
  },
  methods: {
    listenDragDiv() {
      this.$nextTick(() => {
        const callDiv = this.$refs.callDiv;
        callDiv.addEventListener("mousedown", e => {
          e.stopPropagation();
          this.flags = true;
          this.clearTimer();
          this.curDownX = e.x;
          this.curDownY = e.y;
          callDiv.style.transition = "none";
        });
        callDiv.addEventListener(
          "mousemove",
          e => {
            if (this.flags) {
              e.stopPropagation();
              let touch;
              if (e.touches) {
                touch = e.touches[0];
              } else {
                touch = e;
              }
              this.clearTimer();
              this.moveTimer = setInterval(() => {
                this.left += touch.clientX - this.curDownX;
                this.top += touch.clientY - this.curDownY;

                this.curDownX = touch.clientX;
                this.curDownY = touch.clientY;
              }, 3);
            }
          },
          false
        );
        callDiv.addEventListener("mouseup", e => {
          this.flags = false;
          e.stopPropagation();
          callDiv.style.transition = "all 0.03s";
          let touch;
          if (e.touches) {
            touch = e.touches[0];
          } else {
            touch = e;
          }
          this.left += touch.clientX - this.curDownX;
          this.top += touch.clientY - this.curDownY;
          this.curDownX = touch.clientX;
          this.curDownY = touch.clientY;

          this.clearTimer();
        });
      });
    },
    clearTimer() {
      if (this.moveTimer) {
        clearInterval(this.moveTimer);
        this.moveTimer = null;
      }
    },
    handleClose() {
        this.$emit('close')
    }
  }
};
</script>

<style lang="scss" scoped>
.call-container {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  color: #666666;
  z-index: 99;
  transition: all 0.03s;
  cursor: move;
  position: fixed;
  padding-bottom: 25px;
  text-align: center;
  .title-div {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 20px 16px;
    span {
      font-size: 18px;
      color: black;
      font-weight: bold;
    }
    i {
      font-size: 24px;
      cursor: pointer;
    }
  }
  span.close-span {
    border: 1px solid red;
    color: red;
    border-radius: 3px;
    padding: 6px 10px;
  }
}
</style>