import request from '@/router/axios'

//护航日报表
export function queryDefendFactList(data) {
    return request({
        url: '/rbi/defendFact/selectDefendFact',
        method: 'post',
        data,
    })
}

//护航日报表--导出
export function exportDefendFactList(data) {
    return request({
        url: '/rbi/defendFact/exportDefendFact',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}