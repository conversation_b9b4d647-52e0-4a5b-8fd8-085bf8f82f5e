<template>
    <div id="Amap">
        <div id="container" ref="refContainer"></div>
        <div v-if="map_layer.zoomlv && showModules.includes('scale')" class="map_controls">
            {{ Math.round(zoomlvnum) }}
        </div>
        <!-- 搜索框poi 底图切换 -->
        <div class="map_controlsseach">
            <div v-if="map_layer.poiseach && showModules.includes('poiseach')" class="seachbox">
                <i class="el-icon-search"></i>
                <el-autocomplete
                    v-model="poisearchText"
                    :fetch-suggestions="querySearchAsync"
                    placeholder="请输入内容"
                    popper-class="poi_search_autocomplete"
                    @select="handleSelect"
                ></el-autocomplete>
                <i v-if="poisearchText" class="el-icon-close lcpointer" @click="handleclose"></i>
            </div>
            <div v-if="showModules.includes('satellite')" class="mapchangebox">
                <el-select v-model="map_layer.satellite" placeholder="请选择" @change="changesatellite">
                    <el-option label="高德地图" value="1"></el-option>
                    <el-option label="宝石花地图" value="2"></el-option>
                    <el-option label="卫星地图" value="3"></el-option>
                </el-select>
            </div>
        </div>
        <!-- 工具栏 -->
        <div v-if="toolList && toolList.length > 0" class="controls-box">
            <el-popover
                v-if="showtool('tool')"
                v-model="tool.tool1"
                popper-class="tool-box-popover"
                placement="left-start"
                width="262"
                trigger="manual"
            >
                <div
                    slot="reference"
                    class="controls-boxitem"
                    :class="{ active: tool.tool1 }"
                    @click="changetool('tool1')"
                >
                    <div class="bgimg bg1"></div>
                    <div>工具</div>
                </div>
                <div class="tool-box">
                    <div class="tool-title">工具</div>
                    <div class="felx-box-item" :class="{ active: mouseToolboj.tool1 }" @click="drawZoom('tool1')">
                        <img v-if="mouseToolboj.tool1" src="@/assets/tool/fd1.png" alt="" />
                        <img v-else src="@/assets/tool/fd2.png" alt="" />
                        <div>画框放大</div>
                    </div>
                    <div class="felx-box-item" :class="{ active: mouseToolboj.tool2 }" @click="drawZoom('tool2')">
                        <img v-if="mouseToolboj.tool2" src="@/assets/tool/sx1.png" alt="" />
                        <img v-else src="@/assets/tool/sx2.png" alt="" />
                        <div>画框缩小</div>
                    </div>
                    <div class="felx-box-item" :class="{ active: mouseToolboj.tool3 }" @click="drawZoom('tool3')">
                        <img v-if="mouseToolboj.tool3" src="@/assets/tool/cj1.png" alt="" />
                        <img v-else src="@/assets/tool/cj2.png" alt="" />
                        <div>测量距离</div>
                    </div>
                    <div class="felx-box-item" :class="{ active: mouseToolboj.tool4 }" @click="drawZoom('tool4')">
                        <img v-if="mouseToolboj.tool4" src="@/assets/tool/cm1.png" alt="" />
                        <img v-else src="@/assets/tool/cm2.png" alt="" />
                        <div>测量面积</div>
                    </div>
                    <div class="felx-box-item" :class="{ active: mouseToolboj.tool5 }" @click="drawZoom('tool5')">
                        <img v-if="mouseToolboj.tool5" src="@/assets/tool/wz1.png" alt="" />
                        <img v-else src="@/assets/tool/wz2.png" alt="" />
                        <div>位置采集</div>
                    </div>
                    <div class="felx-box-item disab" type="text">
                        <img src="@/assets/tool/lj2.png" alt="" />
                        <div>路径规划</div>
                    </div>
                </div>
                <div v-if="mouseToolboj.tool5" class="locationCollectionbox">
                    <div>
                        经度：<span>{{ location.x }}</span>
                    </div>
                    <div>
                        纬度：<span>{{ location.y }}</span>
                    </div>
                </div>
            </el-popover>
            <el-popover
                v-if="showtool('display')"
                v-model="tool.tool2"
                popper-class="tool-box-popover"
                placement="left-start"
                width="262"
                trigger="manual"
            >
                <div
                    slot="reference"
                    class="controls-boxitem"
                    :class="{ active: tool.tool2 }"
                    @click="changetool('tool2')"
                >
                    <div class="bgimg bg2"></div>
                    <div>显示</div>
                </div>
                <div class="tool-box">
                    <div class="tool-title">显示</div>
                    <el-checkbox
                        v-model="map_layer.all_markers"
                        class="checkbox-box-item"
                        label="车辆聚合"
                        @change="changecarshow"
                    ></el-checkbox>
                    <el-checkbox v-model="map_layer.poiseach" class="checkbox-box-item" label="地址搜索"></el-checkbox>
                    <el-checkbox
                        v-if="showtool('tianqi')"
                        v-model="map_layer.tianqi"
                        class="checkbox-box-item"
                        label="实况天气"
                        @change="changetianqi"
                    ></el-checkbox>
                    <el-checkbox
                        v-model="map_layer.zoomlv"
                        class="checkbox-box-item"
                        label="缩放级别"
                        @change="changezoomlv"
                    ></el-checkbox>
                    <el-checkbox
                        v-model="map_layer.scale"
                        class="checkbox-box-item"
                        label="比例尺"
                        @change="changescale"
                    ></el-checkbox>
                </div>
            </el-popover>
            <el-popover
                v-if="showtool('checkcar')"
                v-model="tool.tool3"
                popper-class="tool-box-popover"
                placement="left-start"
                width="278"
                trigger="manual"
            >
                <div
                    slot="reference"
                    class="controls-boxitem"
                    :class="{ active: tool.tool3 }"
                    @click="changetool('tool3')"
                >
                    <div class="bgimg bg3"></div>
                    <div>查车</div>
                </div>
                <div class="tool-box">
                    <div class="tool-title">查车</div>
                    <div class="felx-box-item" :class="{ active: cartypes.city }" @click="carseach('city')">
                        <img v-if="cartypes.city" src="@/assets/tool/cs1.png" alt="" />
                        <img v-else src="@/assets/tool/cs2.png" alt="" />
                        <div>城市查车</div>
                    </div>
                    <div class="felx-box-item" :class="{ active: cartypes.yuan }" @click="carseach('yuan')">
                        <img v-if="cartypes.yuan" src="@/assets/tool/yx1.png" alt="" />
                        <img v-else src="@/assets/tool/yx2.png" alt="" />
                        <div>圆形查车</div>
                    </div>
                    <div class="felx-box-item" :class="{ active: cartypes.ju }" @click="carseach('ju')">
                        <img v-if="cartypes.ju" src="@/assets/tool/jx1.png" alt="" />
                        <img v-else src="@/assets/tool/jx2.png" alt="" />
                        <div>矩形查车</div>
                    </div>
                    <div class="felx-box-item" :class="{ active: cartypes.duo }" @click="carseach('duo')">
                        <img v-if="cartypes.duo" src="@/assets/tool/dbx1.png" alt="" />
                        <img v-else src="@/assets/tool/dbx2.png" alt="" />
                        <div>多边形查车</div>
                    </div>
                    <div class="felx-box-item" :class="{ active: cartypes.weilan }" @click="carseach('weilan')">
                        <img v-if="cartypes.weilan" src="@/assets/tool/wl1.png" alt="" />
                        <img v-else src="@/assets/tool/wl2.png" alt="" />
                        <div>围栏查车</div>
                    </div>
                    <div class="felx-box-item" @click="carseach('clear')">
                        <img src="@/assets/tool/sx.png" alt="" />
                        <div>清除围栏</div>
                    </div>
                </div>
                <div v-if="cartypes.city" class="citybox">
                    <el-cascader
                        v-model="cityvalue"
                        class="w100"
                        placeholder="试试搜索：城市名称"
                        :options="cityoptions"
                        :props="{ checkStrictly: true, emitPath: false }"
                        filterable
                        clearable
                        @change="cityhandleChange"
                    ></el-cascader>
                </div>
                <div v-if="cartypes.weilan">
                    <el-input v-model="polygon_search_txt" placeholder="请输入内容" class="input-with-select" @change="polygon_searchChange">
                        <el-button slot="append" icon="el-icon-search"></el-button>
                    </el-input>
                    <div class="polygon_search_list" v-loading="polygonlistLoading" element-loading-text="数据加载中">
                        <div
                            v-for="it in polygon_search_list"
                            :key="it.id"
                            class="polygon_item"
                            :class="{ selected: polygon_search_current_id === it.id }"
                            @click="polygon_search_select(it)"
                        >
                            {{ it.name }}
                        </div>
                        <div v-if="!polygon_search_list.length" class="polygin_item_empty">暂无数据</div>
                    </div>
                </div>
            </el-popover>
            <el-popover
                v-if="showtool('legend')"
                v-model="tool.tool4"
                popper-class="tool-box-popover"
                placement="left-start"
                width="245"
                trigger="manual"
            >
                <div
                    slot="reference"
                    class="controls-boxitem"
                    :class="{ active: tool.tool4 }"
                    @click="changetool('tool4')"
                >
                    <div class="bgimg bg4"></div>
                    <div>图例</div>
                </div>
                <div class="tool-box">
                    <div class="tool-title">图例</div>
                    <div v-for="item in cartypelist" :key="item.name" class="felx-box-item w100">
                        <img :src="item.img" alt="" />
                        <div>{{ item.name }}</div>
                    </div>
                </div>
            </el-popover>
        </div>
        <!-- 天气相关 -->
        <div v-if="map_layer.tianqi" class="Weathersbox">
            <el-popover placement="top-start" popper-class="Weathersbox-popover" width="551" trigger="hover">
                <div class="header-box">
                    <div
                        :class="!weathersactive ? 'header-box-item active' : 'header-box-item bg1'"
                        @click="changeWeathertype(0)"
                    >
                        城市天气预警
                    </div>
                    <div
                        :class="weathersactive ? 'header-box-item active' : 'header-box-item bg2'"
                        @click="changeWeathertype(1)"
                    >
                        气象灾害预警
                    </div>
                </div>
                <div v-show="!weathersactive" class="Weatheritems">
                    <div class="Weathersbox-list">
                        <div
                            v-for="item in weathersobjlist"
                            :key="item.code"
                            class="itembox"
                            :class="{ active: weathersobjactive === item.code }"
                            @click="handleWeathers(item, 0)"
                        >
                            <span class="single-line">{{ item.name }}</span>
                            <span>({{ item.alarmCount }})</span>
                        </div>
                    </div>
                    <div v-if="weathersobjactive && activechildren1list.length > 0" class="Weathersbox-list">
                        <div
                            v-for="item in activechildren1list"
                            :key="item.code"
                            class="itembox"
                            :class="{ active: activechildren1active === item.code }"
                            @click="handleWeathers(item, 1)"
                        >
                            <span class="single-line">{{ item.name }}</span>
                            <span>({{ item.alarmCount }})</span>
                        </div>
                    </div>
                    <div v-if="activechildren1active && activechildren2list.length > 0" class="Weathersbox-list">
                        <div
                            v-for="item in activechildren2list"
                            :key="item.code"
                            class="itembox"
                            :class="{ active: activechildren2active === item.code }"
                            @click="handleWeathers(item, 2)"
                        >
                            <span class="single-line">{{ item.name }}</span>
                            <span>({{ item.alarmCount }})</span>
                        </div>
                    </div>
                    <WeatherPopover
                        ref="weatherPopover"
                        class="weather-info"
                        @close="Weatherclose"
                        @toLocation="Weather_to_location"
                        @sendmsglsit="sendmsglsit"
                    />
                </div>
                <div v-show="weathersactive" class="Weatheritems flexd">
                    <div class="weatherstypelist">
                        <div
                            v-for="item in weatherstypelist"
                            :key="item.level"
                            :class="getclass(typelistactive, item)"
                            @click="changetypeitem(item)"
                        >
                            {{ item.level }}预警 {{ item.alarmCount }}
                        </div>
                        <div class="btnbox pointer" @click="typeSendlist">一键下发TTS</div>
                    </div>
                    <div class="weatherstypelistbox">
                        <div v-for="item in typelistactive.children" :key="item.typeName" class="box-item">
                            <div class="top">{{ item.typeName }}</div>
                            <div class="bottom">
                                <div class="imgbox">
                                    <img :src="gettypestr1(typelistactive, item).img" alt="暂无" />
                                </div>
                                <div class="right">
                                    <div class="btnbox pointer" @click="typeSendlistitem(item)">
                                        <img src="@/assets/caritem.png" alt="" />
                                        <div>{{ item.carnum || 0 }} 辆</div>
                                    </div>
                                    <div class="btnbox pointer" @click="typeSendlistitem(item)">
                                        <img src="@/assets/shenyin.png" alt="" />
                                        <div>{{ item.alarmCount }}个预警</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div slot="reference" class="bg1">
                    <el-checkbox
                        v-model="weathersobj.show"
                        class="checkbox"
                        @change="handleweatherpopupshow(weathersobj.show)"
                        >天气预警（{{ weathersobj.total }}）</el-checkbox
                    >
                    <el-checkbox
                        v-if="weathersobj.show"
                        v-model="cluster1show"
                        class="checkbox"
                        @change="handleCluster(cluster1show)"
                        >天气是否聚合</el-checkbox
                    >
                    <div v-if="weathersobj.text" class="showNowtq ml20">{{ weathersobj.text }}</div>
                </div>
            </el-popover>
        </div>
        <!-- <el-dialog
            title="模板下发语音"
            :visible.sync="tts_dialog.visible"
            append-to-body
            :close-on-click-modal="false"
            width="500px"
            @closed="tts_dialog.veh = null"
        >
            <TtsTemplate v-if="tts_dialog.veh" :init_veh="tts_dialog.veh"></TtsTemplate>
        </el-dialog> -->
        <el-dialog 
            custom-class="custom_dialogtts" 
            :visible.sync="tts_dialog.visible"
            @closed="tts_dialog.veh = null" 
            append-to-body 
            :close-on-click-modal="false" 
            :show-close="false"
        >
            <TtsTemplateNew v-if="tts_dialog.veh" :init_veh="tts_dialog.veh" @close_fun="closeTTs" :pageType="is_real_time_video?7:2"/>
        </el-dialog>
        <Cardetail ref="cardetail" title="车辆详情" width="615px" top="200px" :showFooter="false"> </Cardetail>
        <TalkBack v-if="is_veh_net_monitor" ref="talkback" :audioType="'intercom'" @isConnet="changeConnent" />
    </div>
</template>

<script>
import { queryPointPosByGeo } from '@/api/overallMonitor/historyTrack'
import { getWeathers, getAllTree, getmapWeather, queryGeoJSON } from '@/api/pointVehicleQuery'
import { sendMsgRecord } from '@/api/trackMonitor'
import Cardetail from '@/components/cardetail'
import { useApi } from '@/refs'
import typelist from '@/util/cartype'
import tianqitypelist, { gettypestr, gettypestr1 } from '@/util/tianqitype'
import { generateId } from '@/util/util'
import TalkBack from '@/views/real_time_video/talkBack.vue'
import * as apis from '@/views/veh_net_monitor/apis'
// import TtsTemplate from '@/views/veh_net_monitor/TtsTemplate.vue'
import TtsTemplateNew from '@/views/real_time_video/TtsTemplateNew.vue'
import WeatherPopover from '@/views/veh_net_monitor/WeatherPopover.vue'
import { mapGetters } from 'vuex'

export default {
    name: 'Amap',
    components: {
        WeatherPopover,
        // TtsTemplate,
        Cardetail,
        TalkBack,
        TtsTemplateNew,
    },
    inject: ['Index'],
    props: {
        // 默认工具
        toolList: {
            type: Array,
            default: () => {
                // 全部展示 分别是 工具  显示 天气 查车 图例
                // return ['tool', 'display', 'tianqi', 'checkcar', 'legend']
                return ['tool', 'display', 'legend']
            },
        },
        showModules: {
            // 展示的模块 搜索 切换地图 工具条 比例尺
            type: Array,
            default: () => {
                return ['poiseach', 'satellite', 'toolBar', 'scale']
            },
        },
        // 默认缩放比例
        zoom: {
            type: Number,
            default: 12,
        },
        // 组织树全量数据
        allvehlist: {
            type: Array,
            default: () => {
                return []
            },
        },
        // 组织树选中数据
        checked_vehs: {
            type: Array,
            default: () => {
                return []
            },
        },
        // 组织树实例
        tree_search: {
            type: Object,
            default: () => {
                return {}
            },
        },
        //实时视频特有标识
        is_real_time_video: {
            type: Boolean,
            default: false,
        },
        //实时定位特有标识
        is_veh_net_monitor: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            map: null,
            mouseTool: null,
            cartypelist: typelist,
            poisearch: null,
            center: [104, 34], // 设置地图中心点
            map_layer: {
                satellite: '1', // 地图地图切换
                all_markers: true, // 车辆聚合
                poiseach: true, // 地址搜索
                tianqi: false, // 实况天气
                zoomlv: true, // 缩放级别
                scale: true, // 比例尺
            },
            tool: {
                tool1: false, // 工具
                tool2: false, // 显示
                tool3: false, // 查车
                tool4: false, // 图例
            },
            mouseToolboj: {
                tool1: false, // 画框放大
                tool2: false, // 画框缩小
                tool3: false, // 测量距离
                tool4: false, // 测量面积
                tool5: false, // 位置采集
            },
            location: {
                x: '',
                y: '',
                name: '',
            },
            locapopup: null,
            mappopup: null,
            mapcarpopup: null,
            mapcarpopupg: '',
            poisearchText: '',
            poimakers: [],
            zoomlvnum: this.zoom,
            // 天气相关
            weathersactive: 0, // 天气弹窗tab切换
            weathersobj: {
                total: 0,
                show: false,
                text: '',
            },
            hasAlarmslist: [],
            weathersobjactive: '',
            weathersobjlist: [],
            weatherstypelist: [],
            typelistactive: {},
            activechildren1active: '',
            activechildren1list: [],
            activechildren2active: '',
            activechildren2list: [],
            cluster1show: true,
            alllist: [],
            gettypestr1: gettypestr1,
            tianqitypelist: tianqitypelist,
            tqcluster: null,
            citypolygon: null,
            tts_dialog: { visible: false, veh: {} },
            cartypes: {
                city: false, // 城市查车
                yuan: false, // 圆形查车
                ju: false, // 矩形查车
                duo: false, // 多边形查车
                weilan: false, // 围栏查车
            },
            cityvalue: '',
            cityoptions: [],
            polygonlist: [],
            polygonlistLoading: false,
            polygon_search_txt: '',
            polygon_search_current_id: '',
            talkback: '',
            microphoneConnent: false,
        }
    },
    computed: {
        ...mapGetters(['menu', 'permissions']),
        polygon_search_list() {
            // 取条件过滤后前10条数据
            // const list = this.polygonlist.filter((v) => v.name.includes(this.polygon_search_txt))
            // return list.slice(0, 10)
            return this.polygonlist;
        },
    },
    watch: {
        // 监听整个数组
        checked_vehs: {
            handler(newVal) {
                const list = newVal.map((ele) => {
                    return {
                        lnglat: [ele.x + '', ele.y + ''],
                        ...ele,
                    }
                })
                this.initClusterLayer(list)
                this.$nextTick().then(() => {
                    if (this.mapcarpopup.getIsOpen() && this.mapcarpopupg) {
                        const obj = newVal.find((item) => item.g == this.mapcarpopupg)
                        if (obj) {
                            this.veh_click(obj, true, false)
                        } else {
                            this.mapcarpopup && this.mapcarpopup.close()
                        }
                    }
                })
            },
            deep: true, // 深度监听
        },
    },
    created() {
        if (this.showtool('tianqi')) {
            this.getWeatherslist()
            this.getWeatherslist2()
        }
        if (this.showtool('checkcar')) {
            // 城市树
            this.getcitylist()
            // 围栏数据初始化 如果没权限不请求接口
            if (this.permissions['fence_query']) {
                this.getpolygonlist()
            }
        }
    },
    mounted() {
        this.initMap()
    },
    methods: {
        // 更新对讲状态
        changeConnent(val) {
            this.microphoneConnent = val
            const obj = this.checked_vehs.find((item) => item.g == this.mapcarpopupg)
            if (obj) {
                this.veh_click(obj, true, false)
            } else {
                this.mapcarpopup && this.mapcarpopup.close()
            }
        },
        showtool(key) {
            return this.toolList.includes(key)
        },
        getpolygonlist(name='') {
            this.polygonlistLoading = true;
            const api_polygon_list = useApi(apis.fleet.electricFence.queryPage)
            api_polygon_list.fn({ pageNo: 1, pageSize: 10, name }).then((res) => {
                this.polygonlist = res.data.data.records || [];
                this.polygonlistLoading = false;
            }).catch(() => {
                this.polygonlistLoading = false;
            });
        },
        getcitylist() {
            getAllTree().then((res) => {
                const list = res.data.data || []
                this.cityoptions = this.mapcitylist(list)
            })
        },
        async cityhandleChange(item) {
            this.citypolygon && this.map.remove(this.citypolygon)
            this.$emit('setdrawbox', null)
            const res = await queryGeoJSON(item)
            if (!res.data.data) return
            this.citypolygon = new AMap.Polygon({
                path: res.data.data.geometry.coordinates,
                fillColor: '#ccebc5',
                strokeOpacity: 1,
                fillOpacity: 0.5,
                strokeColor: '#2b8cbe',
                strokeWeight: 1,
                strokeStyle: 'dashed',
                strokeDasharray: [5, 5],
            })
            this.map.add(this.citypolygon)
            this.map.setFitView(this.citypolygon)
            this.$emit('setdrawbox', this.citypolygon)
        },
        // 围栏选择
        polygon_search_select(v) {
            this.polygon_search_current_id = v.id
            this.citypolygon && this.map.remove(this.citypolygon)
            this.$emit('setdrawbox', null)
            if (v.geomType == 1) {
                this.citypolygon = new AMap.Circle({
                    center: [v.lng, v.lat],
                    radius: v.radius, //半径
                })
            } else if (v.geomType == 2) {
                const list = v.geom.split(';').map((it) => it.split(',').map((n) => +n))
                // 避免多边形没有闭合导致报错
                if (list[0][0] !== list[list.length - 1][0]) {
                    list[list.length] = list[0]
                }
                this.citypolygon = new AMap.Polygon({
                    path: list,
                })
            }
            this.map.add(this.citypolygon)
            this.map.setFitView(this.citypolygon)
            this.$emit('setdrawbox', this.citypolygon)
        },
        mapcitylist(list) {
            return list.map((item) => {
                if (item.children) {
                    const children = this.mapcitylist(item.children)
                    return {
                        value: item.code,
                        label: item.name,
                        children: children,
                    }
                } else {
                    return {
                        value: item.code,
                        label: item.name,
                    }
                }
            })
        },
        // 车辆聚合图层
        initClusterLayer(list) {
            if (!this.carcluster) {
                this.carcluster = new AMap.MarkerCluster(this.map, list, {
                    gridSize: 50, // 设置网格像素大小
                    maxZoom: 10,
                    renderClusterMarker: this._rendercarClusterMarker, // 自定义聚合点样式
                    renderMarker: this._rendercarMarker, // 自定义非聚合点样式
                })
            } else {
                this.carcluster.setData(list)
            }
            // 绑定点击事件，注意绑定 this
            this.carcluster.on('click', this._handlecarClusterClick)
        },
        _handlecarClusterClick(event) {
            if (event.clusterData.length > 1) {
                // 聚合缩放
                const boundsInfo = this.calculateBoundsDirectly(event.clusterData, false)
                this.map.setBounds(boundsInfo.bounds, false, [100, 100, 100, 100])
            } else {
                // 单个车点详情
                this.initcaritempopup(event.cluster.m[0])
            }
        },
        // 车辆弹窗
        async initcaritempopup(obj1) {
            if (this.mapcarpopupg !== obj1.g) {
                this.mapcarpopup && this.mapcarpopup.close()
                this.microphoneConnent && that.$refs?.talkback?.stopTalkback()
            }
            this.mapcarpopupg = obj1.g
            const obj = { ...obj1 }
            const api_querySingleCarStatus = useApi(apis.fleet.track.querySingleCarStatus)
            const res = await api_querySingleCarStatus.fn(obj.g, 1)
            const view = Object.assign(obj, res.data.data)
            const veh_status_txt = [
                `ACC${+view.acc ? '开' : '关'}`,
                view.has3D === 1 && '3D定位',
                ['天线异常', '天线正常'][view.antenna] || '',
            ]
                .filter((v) => v)
                .join('、')
            this.mapcarpopup.setContent(
                `<div class="car-itembox">
                    ${
                        this.microphoneConnent
                            ? `<div class="talkback"><div class="talktext"></div> <div class="talkbtn" onclick="carinfobtnfunc('stoptalk')">结束</div></div>`
                            : ''
                    }
                    <div class="car-img"></div>
                    <div class="car-itemboxheader">
                    <div class="leftbox">${view.o}
                        <div class="${view.status > 0 ? 'kuang bg1' : 'kuang bg2'}">${
                    view.status > 0 ? '在线' : '离线'
                }</div>
                    </div>
                    <i class="el-icon-close" onclick="mapcarpopupcolse()"></i>
                    </div>
                    <div class="car-left">
                    <div class="car-text mb8"><span class="car-lable">行驶速度：</span>
                        <span class="car-content">${view.p}km/h</span></div>
                    <div class="car-text mb8"><span class="car-lable">今日里程：</span>
                        <span class="car-content">${view.todayMileage < 0 ? '-' : view.todayMileage}km</span></div>
                    <div class="car-text mb8"><span class="car-lable">定位时间：</span>
                        <span class="car-content">${view.gpsTime}</span></div>
                    <div class="car-text mb8"><span class="car-lable">车辆状态：</span>
                        <span class="car-content"> ${veh_status_txt}</span>
                    </div>
                    <div class="car-text"><span class="car-lable">地理位置：</span>
                        <span class="car-content text-ellipsis">${view.address} </span>
                    </div>
                    <div class="car-text mb8"><span class="car-lable"></span>
                        <span class="car-content">(经纬度：${view.x}、${view.y})</span>
                    </div>
                    </div>
                    <div class="car-btns">
                    ${
                        this.is_real_time_video
                            ? ''
                            : this.permissions['rt_live']
                            ? `<div class="car-btn" onclick="carinfobtnfunc('sp')">视频</div>`
                            : ''
                    }
                    ${
                        this.is_real_time_video
                            ? ''
                            : this.permissions['rt_talkback']
                            ? `<div class="car-btn" onclick="carinfobtnfunc('dj')">${
                                  this.microphoneConnent ? '对讲中' : '对讲'
                              }</div>`
                            : ''
                    }
                    ${
                        this.permissions['rt_track_query']
                            ? `<div class="car-btn" onclick="carinfobtnfunc('gj')">轨迹</div>`
                            : ''
                    }
                    ${
                        this.permissions['rt_tts_send']
                            ? `<div class="car-btn" onclick="carinfobtnfunc('tts')">TTS</div>`
                            : ''
                    }
                    ${
                        this.permissions['rt_vehicle_detail']
                            ? `<div class="car-btn" onclick="carinfobtnfunc('xq')">详情</div>`
                            : ''
                    }
                    <div class="car-btn" onclick="carinfobtnfunc('sc')">${view.isCollect ? '取消收藏' : '收藏'}</div>
                    </div>
                </div>`,
            )
            const that = this
            function carinfobtnfunc(id) {
                if (id === 'sp') {
                    // 实时视频页面显示
                    that.$emit('opnecarvideo', view)
                } else if (id === 'dj') {
                    if (view.status > 0) {
                        that.microphoneConnent
                            ? that.$refs.talkback.stopTalkback()
                            : that.$refs.talkback.talkback(view.imei, view.carNo)
                    } else {
                        that.$message.warning('车辆不在线无法发起对讲！')
                    }
                } else if (id === 'gj') {
                    that.$emit('toHistoryTrack', view)
                } else if (id === 'tts') {
                    if (view.status > 0) {
                        that.tts_dialog = { visible: true, veh: view }
                    } else {
                        that.$message.warning('车辆不在线无法下发tts！')
                    }
                } else if (id === 'xq') {
                    that.$refs.cardetail.opnecardetail(view)
                } else if (id === 'sc') {
                    that.tree_search.handleCollectcar(view)
                } else if (id === 'stoptalk') {
                    that.$refs.talkback.stopTalkback()
                }
            }
            window.mapcarpopupcolse = () => {
                this.mapcarpopup.close()
                this.microphoneConnent && this.$refs.talkback.stopTalkback()
            }
            window.carinfobtnfunc = carinfobtnfunc
            this.mapcarpopup.open(this.map, [obj.x, obj.y])
        },
        // 车辆定位
        veh_click(veh, open_popover = false, isclick = false) {
            if (isclick) {
                this.map.setZoom(16)
            }
            const pixel = this.map.lngLatToContainer([veh.x, veh.y])
            // 计算新的像素坐标
            const newPixel = new AMap.Pixel(pixel.x, pixel.y - 150)
            // 转换回经纬度坐标
            var newCenter = this.map.containerToLngLat(newPixel)
            this.map.setCenter([newCenter.lng, newCenter.lat])
            if (open_popover) {
                this.initcaritempopup(veh)
            }
        },
        _rendercarMarker(context) {
            const imgdata = typelist[context.data[0].typeicon]
            // 确保图片URL存在，如果不存在使用默认图片
            const content = `<div style="height: 30px; width: 30px;">
                                <img src="${imgdata.img}" alt=""
                                style="${
                                    imgdata.isBearing ? `transform: rotate(${context.data[0].b}deg);` : ''
                                }width: 100%; height: 100%; object-fit: cover;"/>
                                <div style="display: flex;justify-content: space-between;padding: 0 5px;background-color: #fff;color: #333;border-radius: 4px;line-height: 30px;text-align: center;position: absolute;bottom: -35px;left: calc(50% - 60px);font-size: 12px;height: 30px; width: 120px;box-shadow: 0px 2px 6px 0px #BCBCBD;">
                                    <span style="color: #3D50F1;">${context.data[0].p} km/h</span>
                                    <span style="color: #333;">${context.data[0].o}</span>
                                </div>
                            </div>`
            const offset = new AMap.Pixel(-15, -15)
            context.marker.setContent(content)
            context.marker.setOffset(offset)
        },
        _rendercarClusterMarker(context) {
            const content = `<div style="position: relative;height: 40px; width: 40px;background-color: #fb6806;border-radius: 20px;">
                                <div style="color: #fff;line-height: 30px;text-align: center;position: absolute;top: 3px;left: 3px;font-size: 14px;height: 34px; width: 34px;background-color: #fb6806;border: 2px solid #fff;border-radius: 17px;">${context.count}</div>
                            </div>`
            context.marker.setOffset(new AMap.Pixel(-20, -20))
            context.marker.setContent(content)
        },
        // 底图图层
        initLayer() {
            this.satellite = new AMap.TileLayer.Satellite()
            // this.RoadNet = new AMap.TileLayer.RoadNet()
            this.bshmap = new AMap.TileLayer({
                tileSize: 256,
                zIndex: 100,
                getTileUrl: (x, y, z) => {
                    const adjustedZ = z - 1
                    // 确保缩放级别不小于0
                    const finalZ = Math.max(0, adjustedZ)
                    return `bshmap/MapServer/tile/${finalZ}/${y}/${x}`
                },
            })
            this.map.addLayer(this.satellite)
            // this.map.addLayer(this.RoadNet)
            this.map.add(this.bshmap)
            this.bshmap.hide()
            this.satellite.hide()
            // this.RoadNet.hide()
        },
        // 底图图层
        initControl() {
            // 控件
            this.poisearch = new AMap.AutoComplete({})
            this.scale = new AMap.Scale({
                position: {
                    top: '16px',
                    left: '16px',
                },
            })
            this.toolBar = new AMap.ToolBar({
                position: {
                    top: '46px',
                    left: '16px',
                },
            })
            if (this.showModules.includes('scale')) {
                this.map.addControl(this.scale) // 比例尺
            }
            if (this.showModules.includes('toolBar')) {
                this.map.addControl(this.toolBar) // 缩放控件
            }
        },
        // 初始化地图
        initMap() {
            const map = new AMap.Map(this.$refs.refContainer, {
                zoom: this.zoom, // 设置地图缩放级别
                center: this.center, // 设置地图中心点
                zooms: [3, 20], // 缩放范围
                animateEnable: false, // 不使用平移动画
                WebGLParams: {
                    preserveDrawingBuffer: true, // 设置preserveDrawingBuffer为true
                },
            })

            // 先获取当前地图视图范围/可视区域 getBounds
            // const mapbounds = map.getBounds()
            // // 获取限制的Bounds的范围值传入设定区域限制
            // map.setLimitBounds(mapbounds)
            this.map = map
            this.initLayer()
            this.initControl()
            // 鼠标工具
            this.mouseTool = new AMap.MouseTool(map)
            this.mouseTool.on('draw', (event) => {
                //event.obj 为绘制出来的覆盖物对象
                this.$emit('setdrawbox', event.obj)
                this.mouseTool.close(false)
            })
            // 位置采集弹窗
            this.locapopup = new AMap.InfoWindow({
                anchor: 'bottom-center',
                isCustom: true, //使用自定义窗体
                autoMove: true,
                closeWhenClickMap: false,
                offset: new AMap.Pixel(0, -7),
            })
            // 地图 天气卡片等 弹窗
            this.mappopup = new AMap.InfoWindow({
                // anchor: 'bottom-center',
                isCustom: true, //使用自定义窗体
                autoMove: true,
                closeWhenClickMap: true,
                offset: new AMap.Pixel(0, -20),
            })
            // 地图 车辆卡片  弹窗
            this.mapcarpopup = new AMap.InfoWindow({
                // anchor: 'bottom-center',
                isCustom: true, //使用自定义窗体
                autoMove: false,
                closeWhenClickMap: false,
                offset: new AMap.Pixel(0, -20),
            })
            // 监听鼠标点击事件
            map.on('click', async (e) => {
                if (this.mouseToolboj.tool5) {
                    const res = await queryPointPosByGeo({ x: e.lnglat.getLng(), y: e.lnglat.getLat() })
                    this.location.x = e.lnglat.getLng()
                    this.location.y = e.lnglat.getLat()
                    this.location.name = res.data.data
                    // 如果开启了对讲开启这个弹窗关闭对讲
                    this.microphoneConnent && this.$refs.talkback.stopTalkback()
                    this.locapopup.open(map, [e.lnglat.getLng(), e.lnglat.getLat()])
                    this.locapopup.setContent(
                        `<div class='popupbox'>
                            <div class='popupbox-header'><span></span><i class="el-icon-close pointer" onclick="locapopup.close()"></i></div>
                            <div class='popupbox-text'>经度: ${this.location.x.toFixed(6)}</div>
                            <div class='popupbox-text'>纬度: ${this.location.y.toFixed(6)}</div>
                            <div class='popupbox-text'>地址: ${this.location.name || '当前位置无地址！'}</div>
                            <div class='amap-info-sharp'></div>
                        </div>`,
                    )
                    // window注册方法
                    window.locapopup = this.locapopup
                    this.map.setDefaultCursor('crosshair')
                    this.locapopup.on('close', () => {
                        this.map.setDefaultCursor('default')
                    })
                }
            })
            // 监听鼠标移动事件
            map.on('mousemove', (e) => {
                if (this.mouseToolboj.tool5) {
                    if (this.locapopup.getIsOpen()) return
                    this.location.x = e.lnglat.getLng()
                    this.location.y = e.lnglat.getLat()
                } else {
                    this.location.x = ''
                    this.location.y = ''
                    this.location.name = ''
                }
            })
            map.on('zoomend', () => {
                const currentZoom = map.getZoom() // 获取当前地图的缩放级别
                this.zoomlvnum = currentZoom
                if (currentZoom < 11) {
                    this.map_layer.all_markers = true
                } else {
                    this.map_layer.all_markers = false
                }
                // 当地图缩放结束时，这里的代码会被执行
                this.getmaphandle()
            })
            map.on('moveend', () => {
                // 当地图移动结束时，这里的代码会被执行
                this.getmaphandle()
            })
            this.$emit('map', map) // 向父组件传递地图实例
        },
        // 查车逻辑
        carseach(key) {
            const keys = Object.keys(this.cartypes)
            keys.forEach((item) => {
                if (item !== key) {
                    this.cartypes[item] = false
                }
            })
            this.cartypes[key] = !this.cartypes[key]
            this.mouseTool.close(true)
            this.polygon_search_current_id = ''
            this.polygon_search_txt = ''
            this.cityvalue = ''
            this.citypolygon && this.map.remove(this.citypolygon)
            this.$emit('setdrawbox', null)
            // 画框
            if (this.cartypes.ju) {
                this.mouseTool.rectangle()
            } else if (this.cartypes.duo) {
                this.mouseTool.polygon()
            } else if (this.cartypes.yuan) {
                this.mouseTool.circle()
            }
        },
        // 查车-围栏查车输入框事件、
        polygon_searchChange(value) {
            this.getpolygonlist(value);
        },
        // 清空所有工具栏状态
        cealrtool() {
            // 清空工具栏选中状态
            const keys = Object.keys(this.tool)
            keys.forEach((item) => {
                this.tool[item] = false
            })
            // 清空工具选中状态
            const keys1 = Object.keys(this.mouseToolboj)
            keys1.forEach((item) => {
                this.mouseToolboj[item] = false
            })
            this.setNormalMode()
            this.locapopup && this.locapopup.close()
            // 清空查车选中状态
            const keys2 = Object.keys(this.cartypes)
            keys2.forEach((item) => {
                this.cartypes[item] = false
            })
            this.mouseTool.close(true)
            this.polygon_search_current_id = ''
            this.polygon_search_txt = ''
            this.cityvalue = ''
            this.citypolygon && this.map.remove(this.citypolygon)
            this.$emit('setdrawbox', null)
        },
        // 工具栏
        changetool(key) {
            const keys = Object.keys(this.tool)
            keys.forEach((item) => {
                if (item !== key) {
                    this.tool[item] = false
                }
            })
            this.tool[key] = !this.tool[key]
            if (!this.tool.tool1) {
                const keys = Object.keys(this.mouseToolboj)
                keys.forEach((item) => {
                    if (item !== key) {
                        this.mouseToolboj[item] = false
                    }
                })
                this.setNormalMode()
                this.locapopup && this.locapopup.close()
            }
            if (!this.tool.tool3) {
                const keys = Object.keys(this.cartypes)
                keys.forEach((item) => {
                    this.cartypes[item] = false
                })
                this.mouseTool.close(true)
                this.polygon_search_current_id = ''
                this.polygon_search_txt = ''
                this.cityvalue = ''
                this.citypolygon && this.map.remove(this.citypolygon)
                this.$emit('setdrawbox', null)
            }
        },
        // 工具
        drawZoom(key) {
            const keys = Object.keys(this.mouseToolboj)
            keys.forEach((item) => {
                if (item !== key) {
                    this.mouseToolboj[item] = false
                }
            })
            this.mouseToolboj[key] = !this.mouseToolboj[key]
            this.map.setDefaultCursor('crosshair')
            this.locapopup && this.locapopup.close()
            if (this.mouseToolboj.tool1) {
                this.mouseTool.rectZoomIn({
                    strokeColor: '#80d8ff',
                    fillColor: '#80d8ff',
                    fillOpacity: 0.3,
                    //同 Polygon 的 Option 设置
                })
            } else if (this.mouseToolboj.tool2) {
                this.mouseTool.rectZoomOut({
                    strokeColor: '#80d8ff',
                    fillColor: '#80d8ff',
                    fillOpacity: 0.3,
                    //同 Polygon 的 Option 设置
                })
            } else if (this.mouseToolboj.tool3) {
                this.mouseTool.rule()
            } else if (this.mouseToolboj.tool4) {
                this.mouseTool.measureArea()
            } else if (this.mouseToolboj.tool5) {
                this.mouseTool.close(true)
                this.map.setStatus({
                    dragEnable: true,
                    zoomEnable: true,
                    doubleClickZoom: true,
                })
            } else {
                this.setNormalMode()
            }
        },
        // 清除工具状态
        setNormalMode() {
            this.map.setDefaultCursor('default')
            this.mouseTool.close(true)
            this.map.setStatus({
                dragEnable: true,
                zoomEnable: true,
                doubleClickZoom: true,
            })
            this.location.x = ''
            this.location.y = ''
            this.location.name = ''
        },
        // 切换底图
        changesatellite(e) {
            if (e === '2') {
                this.bshmap.show()
                this.satellite.hide()
                // this.RoadNet.hide()
            } else if (e === '3') {
                this.bshmap.hide()
                this.satellite.show()
                // this.RoadNet.show()
            } else {
                this.bshmap.hide()
                this.satellite.hide()
                // this.RoadNet.hide()
            }
        },
        // 切换天气显示隐藏
        changetianqi(e) {
            if (!e) {
                this.weathersobj.show = false
                this.cluster1show = true
                this.handleCluster(true)
                this.handleweatherpopupshow(false)
            }
        },
        // 切换缩放级别显示隐藏
        changezoomlv(e) {
            e ? this.toolBar.show() : this.toolBar.hide()
        },
        // 切换比例尺
        changescale(e) {
            e ? this.scale.show() : this.scale.hide()
        },
        // 切换车辆聚合状态
        changecarshow(e) {
            const currentCenter = this.map.getCenter()
            if (!e) {
                if (this.zoomlvnum < 11) {
                    this.map.setZoomAndCenter(11, [currentCenter.lng, currentCenter.lat])
                }
            } else {
                if (this.zoomlvnum > 10) {
                    this.map.setZoomAndCenter(10, [currentCenter.lng, currentCenter.lat])
                }
            }
        },
        // poi搜索选中
        handleSelect(item) {
            this.map.setZoomAndCenter(16, [item.x, item.y])
            this.$emit('poi-select', item)
        },
        // poi清空搜索
        handleclose() {
            this.poisearchText = ''
            this.map.remove(this.poimakers)
        },
        // poi搜索
        querySearchAsync(queryString, cb) {
            if (!queryString) {
                this.map.remove(this.poimakers)
                cb([])
                return
            }
            this.poisearch.search(queryString, (status, result) => {
                if (status === 'complete' && result.info === 'OK') {
                    const list = result.tips.filter((item) => item.location)
                    const maplist = list.map((item) => {
                        return {
                            value: item.name,
                            address: item.address,
                            x: item.location.lng,
                            y: item.location.lat,
                            ...item,
                        }
                    })
                    // 每次开始渲染标记前删除所有标记
                    this.map.remove(this.poimakers)
                    this.poimakers = []
                    maplist.forEach((item) => {
                        const marker = new AMap.Marker({
                            map: this.map,
                            position: [item.x, item.y],
                        })
                        // 设置鼠标划过点标记显示的文字提示
                        marker.setTitle(item.value + '')
                        // 设置label标签
                        // label默认蓝框白底左上角显示，样式className为：amap-marker-label
                        marker.setLabel({
                            direction: 'top',
                            offset: new AMap.Pixel(0, -5), //设置文本标注偏移量
                            content: `<div class='info'>${item.value + ''}</div>`, //设置文本标注内容
                        })
                        this.poimakers.push(marker)
                    })
                    if (maplist.length < 2) {
                        if (maplist.length > 0) {
                            this.handleSelect(maplist[0])
                        }
                    } else {
                        const boundsInfo = this.calculateBoundsDirectly(this.poimakers)
                        this.map.setBounds(boundsInfo.bounds, false, [50, 50, 50, 50])
                    }
                    cb(maplist)
                }
            })
        },
        // 计算边界
        calculateBoundsDirectly(markers, ismarker = true) {
            if (!markers || markers.length === 0) return null
            let minLng = Infinity
            let maxLng = -Infinity
            let minLat = Infinity
            let maxLat = -Infinity
            markers.forEach((marker) => {
                let lng
                let lat
                if (ismarker) {
                    const pos = marker.getPosition()
                    lng = pos.getLng()
                    lat = pos.getLat()
                } else {
                    lng = marker.x
                    lat = marker.y
                }
                minLng = Math.min(minLng, lng)
                maxLng = Math.max(maxLng, lng)
                minLat = Math.min(minLat, lat)
                maxLat = Math.max(maxLat, lat)
            })
            return {
                minLng,
                minLat,
                maxLng,
                maxLat,
                bounds: new AMap.Bounds([minLng, minLat], [maxLng, maxLat]),
            }
        },
        // 天气相关
        // 获取实况天气
        getmaphandle() {
            if (!this.showtool('tianqi')) return
            const currentZoom = this.map.getZoom() // 获取当前地图的缩放级别
            // 在这里可以执行您需要的操作，例如更新UI或者发送数据
            if (currentZoom >= 7 && this.map_layer.tianqi) {
                const Center = this.map.getCenter()
                this.mapWeather(currentZoom, Center)
            } else {
                this.weathersobj.text = ''
            }
        },
        // 查询实况天气
        mapWeather(currentZoom, Center) {
            const params = {
                level: currentZoom, //地图缩放级别
                x: Center.lng, //经度
                y: Center.lat, //纬度
            }
            getmapWeather(params).then((res) => {
                this.weathersobj.text = res.data.data
            })
        },
        // 关闭城市geo覆盖层
        Weatherclose() {
            this.citypolygon && this.map.remove(this.citypolygon)
        },
        // 天气坐标定位
        Weather_to_location(obj) {
            if (!this.weathersobj.show) {
                this.weathersobj.show = true
                this.handleweatherpopupshow(this.weathersobj.show)
            }
            if (obj.active) {
                // 获取城市geo
                this.getcitygeo(obj.code)
                this.initweatheritempopup(obj)
            } else {
                this.Weatherclose()
            }
        },
        // 城市geo覆盖
        async getcitygeo(citycode) {
            const res = await queryGeoJSON(citycode)
            this.citypolygon && this.map.remove(this.citypolygon)
            this.citypolygon = new AMap.Polygon({
                path: res.data.data.geometry.coordinates,
                fillColor: '#ccebc5',
                strokeOpacity: 1,
                fillOpacity: 0.5,
                strokeColor: '#2b8cbe',
                strokeWeight: 1,
                strokeStyle: 'dashed',
                strokeDasharray: [5, 5],
            })
            this.map.add(this.citypolygon)
            this.map.setFitView(this.citypolygon)
        },
        // 天气弹窗
        initweatheritempopup(obj) {
            // 如果开启了对讲 开启这个弹窗关闭对讲
            this.microphoneConnent && this.$refs.talkback.stopTalkback()
            this.mappopup.open(this.map, [obj.x, obj.y])
            this.mappopup.setContent(
                `<div class="weatheritempopupbox">
                    <div class="car-headerbox">
                        <div>${obj.name}</div>
                        <i class="el-icon-close" onclick="mappopup.close()"></i>
                    </div>
                    <div class="weather-itembox">
                        <img class="weather-imgbox" src="${obj.img}" alt="">
                        <div class="text-box">${obj.name} ${obj.typeName} ${obj.level}</div>
                    </div>
                    <div class="weather-text">${obj.content}</div>
                </div>`,
            )
            // window注册方法
            window.mappopup = this.mappopup
        },
        //tts下发
        sendmsglsit(item) {
            this.$message.warning('已开始下发当前城市天气预警！')
            const list = [{ imeiList: item.carlist.map((v) => v.g), msg: item.content, type: '1' }]
            this.tts_sendlist(list)
        },
        //tts下发
        typeSendlist() {
            const datalist = []
            this.typelistactive.children.forEach((item) => {
                item.alarms.forEach((ele) => {
                    const list = this.getVehiclesByCityCode(ele.cityCode, this.allvehlist)
                    if (Array.isArray(list) && list.length > 0) {
                        const obj = { imeiList: list.map((v) => v.g), msg: ele.content, type: '1' }
                        datalist.push(obj)
                    }
                })
            })
            this.tts_sendlist(datalist)
        },
        // 切换天气类型
        changeWeathertype(num) {
            this.weathersactive = num
        },
        // 切换天气选中类型
        changetypeitem(item) {
            this.typelistactive = item
        },
        // 天气弹窗样式
        getclass(active, item) {
            return active.level === item.level ? 'listheader-item active' : 'listheader-item'
        },
        // 天气数据
        getWeatherslist() {
            getWeathers(1).then((res) => {
                if (res.data.code === 0) {
                    this.weathersobjlist = res.data.data
                    this.weathersobjlist.forEach((item) => {
                        this.weathersobj.total = this.weathersobj.total + item.alarmCount
                    })
                }
            })
        },
        // 天气数据
        getWeatherslist2() {
            getWeathers(2).then((res) => {
                if (res.data.code === 0) {
                    const list = res.data.data || []
                    this.weatherstypelist = list.map((item) => {
                        item.children.map((ele) => {
                            let carnum = 0
                            ele.cityCodes.forEach((v) => {
                                const list = this.getVehiclesByCityCode(v, this.allvehlist)
                                const num = Array.isArray(list) ? list.length : 0
                                carnum = carnum + num
                            })
                            ele.carnum = carnum
                            return ele
                        })
                        return item
                    })
                    // 初始化给个值
                    if (JSON.stringify(this.typelistactive) === '{}') {
                        this.changetypeitem(this.weatherstypelist[0])
                    }
                }
            })
        },
        // 获取当前天气城市下有多少车辆数据
        getVehiclesByCityCode(targetCityCode, vehicleData) {
            if (!targetCityCode || !Array.isArray(vehicleData)) {
                return []
            }
            // 标准化 cityCode：确保是 6 位字符串
            const normalizedTargetCode = String(targetCityCode).padEnd(6, '0')
            const level = normalizedTargetCode.endsWith('0000')
                ? 'province'
                : normalizedTargetCode.endsWith('00')
                ? 'city'
                : 'district'
            // 筛选逻辑：根据层级动态匹配
            const filteredVehicles = vehicleData.filter((vehicle) => {
                const str = vehicle.m ? String(vehicle.m) : ''
                const vehicleCityCode = str ? str.padEnd(6, '0') : '000000'
                // 省级：匹配前 2 位（如 "110000" → 所有 "11xxxx"）
                if (level === 'province') {
                    return vehicleCityCode.startsWith(normalizedTargetCode.slice(0, 2))
                }
                // 市级：匹配前 4 位（如 "110100" → 所有 "1101xx"）
                else if (level === 'city') {
                    return vehicleCityCode.startsWith(normalizedTargetCode.slice(0, 4))
                }
                // 区级：精确匹配 6 位（如 "110101" → "110101"）
                else if (level === 'district') {
                    return vehicleCityCode === normalizedTargetCode
                }
                // 无效层级
                else {
                    return false
                }
            })
            return filteredVehicles
        },
        // 天气预警数据开关
        handleweatherpopupshow(e) {
            if (!e) {
                this.weathersobjactive = ''
                this.activechildren1active = ''
                this.activechildren2active = ''
                this.activechildren1list = []
                this.activechildren2list = []
                this.hasAlarmslist = {}
                this.mappopup && this.mappopup.close()
                this.$refs.weatherPopover?.pub_close()
                this.alllist = []
            } else {
                const list = this.getlist(this.weathersobjlist)
                this.alllist = list.map((ele) => {
                    return {
                        lnglat: [ele.x + '', ele.y + ''],
                        ...ele,
                    }
                })
            }
            // 初始化天气点聚
            this.initweatherClusterLayer(this.alllist.filter((v) => v.x && v.y))
        },
        // 天气点聚合
        initweatherClusterLayer(list) {
            if (!this.tqcluster) {
                this.tqcluster = new AMap.MarkerCluster(this.map, list, {
                    gridSize: 50, // 设置网格像素大小
                    maxZoom: 14,
                    renderClusterMarker: this._renderClusterMarker, // 自定义聚合点样式
                    renderMarker: this._renderMarker, // 自定义非聚合点样式
                })
            } else {
                this.tqcluster.setData(list)
            }
            // 绑定点击事件，注意绑定 this
            this.tqcluster.on('click', this._handleClusterClick)
        },
        // 天气聚合开关
        handleCluster(e) {
            if (e) {
                this.tqcluster.setMaxZoom(16)
            } else {
                this.tqcluster.setMaxZoom(3)
            }
        },
        // 聚合点击
        _handleClusterClick(event) {
            if (event.clusterData.length > 1) {
                // 聚合缩放
                const boundsInfo = this.calculateBoundsDirectly(event.clusterData, false)
                this.map.setBounds(boundsInfo.bounds, false, [100, 100, 100, 100])
            } else {
                // 单个点详情
                this.initweatheritempopup(event.clusterData[0])
            }
        },
        _renderMarker(context) {
            // 确保图片URL存在，如果不存在使用默认图片
            const content = `<div style="height: 30px; width: 30px;">
                                <img src="${context.data[0].img}" alt="" style="width: 100%; height: 100%; object-fit: cover;"/>
                            </div>`
            const offset = new AMap.Pixel(-15, -15)
            context.marker.setContent(content)
            context.marker.setOffset(offset)
        },
        _renderClusterMarker(context) {
            const div = document.createElement('div')
            div.style.backgroundColor = '#4A5EFF'
            div.style.width = '40px'
            div.style.height = '40px'
            div.style.borderRadius = '20px'
            div.innerHTML = context.count
            div.style.lineHeight = '40px'
            div.style.color = '#fff'
            div.style.fontSize = '16px'
            div.style.textAlign = 'center'
            context.marker.setOffset(new AMap.Pixel(-20, -20))
            context.marker.setContent(div)
        },
        // tts下发
        typeSendlistitem(item) {
            const datalist = []
            item.alarms.forEach((ele) => {
                const list = this.getVehiclesByCityCode(ele.cityCode, this.allvehlist)
                if (Array.isArray(list) && list.length > 0) {
                    const obj = { imeiList: list.map((v) => v.g), msg: ele.content, type: '1' }
                    datalist.push(obj)
                }
            })
            this.tts_sendlist(datalist)
        },
        // tts下发
        async tts_sendlist(datalist) {
            if (datalist.length === 0) return
            //type 下发类型 0只下发离线车辆 1只下发在线车辆  2不区分在线离线都下发
            const res = await sendMsgRecord(datalist)
            if (res.data.success) {
                this.$message.success('语音发送成功')
            } else {
                this.$message.warning('语音发送失败')
            }
        },
        // 天气城市选中
        handleWeathers(item, lv) {
            if (lv === 0) {
                if (item.code === this.weathersobjactive) {
                    this.weathersobjactive = ''
                    this.activechildren1list = []
                    this.$refs.weatherPopover.pub_close()
                } else {
                    this.weathersobjactive = item.code
                    this.activechildren1list = item?.children || []
                    this.showWeatherpopup(item)
                }
                this.activechildren1active = ''
                this.activechildren2active = ''
                this.activechildren2list = []
            } else if (lv === 1) {
                if (item.code === this.activechildren1active) {
                    this.activechildren1active = ''
                    this.activechildren2list = []
                    this.$refs.weatherPopover.pub_close()
                } else {
                    this.activechildren1active = item.code
                    this.activechildren2list = item?.children || []
                    this.showWeatherpopup(item)
                }
                this.activechildren2active = ''
            } else {
                if (item.code === this.activechildren2active) {
                    this.activechildren2active = ''
                    this.$refs.weatherPopover.pub_close()
                } else {
                    this.activechildren2active = item.code
                    this.showWeatherpopup(item)
                }
            }
        },
        // 天气城市详情popup
        async showWeatherpopup(item) {
            this.hasAlarmslist = []
            const num = this.getVehiclesByCityCode(item.code, this.allvehlist)
            const list = this.getlist(this.weathersobjlist)
            const clickitem = list.filter((ele) => item.code === ele.code)
            if (clickitem.length > 0) {
                clickitem.forEach((item) => {
                    item.carnum = Array.isArray(num) ? num.length : 0
                    item.carlist = num
                })
                this.hasAlarmslist = clickitem
                this.$refs.weatherPopover.pub_open_weather(this.hasAlarmslist)
            } else {
                this.$refs.weatherPopover.pub_close()
            }
            this.mappopup && this.mappopup.close()
            this.Weatherclose()
        },
        // 递归平铺天气数据
        getlist(list) {
            let list1 = []
            list.forEach((item) => {
                if (item.alarms && Array.isArray(item.alarms)) {
                    item.alarms.forEach((e) => {
                        const idstr = generateId() // 生成唯一id
                        return list1.push({
                            id: idstr,
                            carnum: 0,
                            typeicon: gettypestr(e).key,
                            img: gettypestr(e).img,
                            content: e.content,
                            level: e.level,
                            pubTime: e.pubTime,
                            typeName: e.typeName,
                            code: item.code,
                            name: item.name,
                            x: item.x,
                            y: item.y,
                        })
                    })
                }
                if (item.children && Array.isArray(item.children)) {
                    const list2 = this.getlist(item.children)
                    list1 = [...list1, ...list2]
                }
            })
            return list1
        },
         //当TTS播放成功关闭dialog
        closeTTs(val){
      this.tts_dialog.visible = val
    }
    },
}
</script>

<style scoped lang="scss">
#Amap {
    width: 100%;
    height: 100%;
    position: relative;
    #container {
        width: 100%;
        height: 100%;
        ::v-deep .amap-icon {
            img {
                width: auto;
                height: 12px;
            }
        }
        ::v-deep .popupbox {
            width: 200px;
            color: #333;
            background-color: #fff;
            border-radius: 4px;
            padding: 4px;
            position: relative;
            .popupbox-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 14px;
            }
            .popupbox-text {
                font-size: 12px;
            }
            .pointer {
                cursor: pointer;
            }
            .amap-info-sharp {
                position: absolute;
                bottom: -7px;
                left: 50%;
                &::after {
                    position: absolute;
                    content: '';
                    margin-top: -7px;
                    margin-left: -8px;
                    border-left: 8px solid transparent;
                    border-right: 8px solid transparent;
                    border-top: 8px solid #fff;
                }
            }
        }
        ::v-deep .weatheritempopupbox {
            width: 300px;
            background-color: #fff;
            .car-headerbox {
                padding: 0 20px;
                font-size: 18px;
                color: #000;
                line-height: 50px;
                margin-bottom: 12px;
                background: linear-gradient(270deg, rgba(130, 155, 255, 0) 0%, #4b63ff27 100%);
                width: 100%;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-weight: 600;
                i {
                    font-size: 20px !important;
                    cursor: pointer;
                }
                div {
                    white-space: nowrap; /* 禁止换行 */
                    overflow: hidden; /* 超出隐藏 */
                    text-overflow: ellipsis;
                    width: calc(100% - 50px);
                }
            }
            .weather-itembox {
                display: flex;
                align-items: center;
                padding: 12px 20px;
                .weather-imgbox {
                    width: 22px;
                    height: 22px;
                    margin-right: 12px;
                }
                .text-box {
                    font-weight: 600;
                    font-size: 16px;
                    color: #4a5eff;
                }
            }
            .weather-text {
                padding: 0 20px 20px 20px;
                font-size: 14px;
                color: #333333;
                line-height: 22px;
            }
        }
        ::v-deep .car-itembox {
            width: 444px;
            background-color: #fff;
            position: relative;
            border-radius: 6px;
            .talkback {
                position: absolute;
                top: 9px;
                right: 50px;
                width: 170px;
                height: 32px;
                background: #ffffff;
                border-radius: 4px;
                border: 1px solid #dcdee0;
                display: flex;
                align-items: center;
                justify-content: space-around;
                font-size: 12px;
                .talktext {
                    width: 100px;
                    height: 17px;
                    background-image: url('../../assets/cartype/voiceimg.png');
                    background-size: cover;
                }
                .talkbtn {
                    width: 55px;
                    height: 30px;
                    background: #f4f6f9;
                    border-radius: 0px 4px 4px 0px;
                    color: #000;
                    line-height: 30px;
                    text-align: center;
                }
            }
            .car-img {
                position: absolute;
                top: 0;
                right: 0;
                width: 190px;
                height: 140px;
                background-image: url('../../assets/cartype/carbig.png');
                background-size: cover;
                pointer-events: none; // 鼠标穿透事件
            }
            .car-itemboxheader {
                padding: 0 16px;
                width: 100%;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-weight: 600;
                font-size: 18px;
                color: #000;
                line-height: 50px;
                margin-bottom: 12px;
                background: linear-gradient(270deg, rgba(130, 155, 255, 0) 0%, #4b63ff27 100%);
                .leftbox {
                    display: flex;
                    align-items: center;
                    .kuang {
                        width: 40px;
                        height: 20px;
                        border-radius: 4px;
                        margin-left: 8px;
                        font-weight: 500;
                        font-size: 12px;
                        color: #ffffff;
                        text-align: center;
                        line-height: 20px;
                    }
                    .bg1 {
                        background: #0ba33331;
                        color: #0ba333;
                    }
                    .bg2 {
                        background: #c2c1c1;
                    }
                }
                i {
                    cursor: pointer;
                }
            }
            .car-left {
                width: 100%;
                padding: 0 16px;
                .car-text {
                    font-size: 14px;
                    color: #777777;
                    line-height: 20px;
                    min-height: 20px;
                    display: flex;
                    .car-lable {
                        width: 76px;
                    }
                    .car-content {
                        width: calc(100% - 76px);
                        color: #262626;
                    }
                }
                .mb8 {
                    margin-bottom: 8px;
                }
            }
            .car-btns {
                width: 100%;
                height: 40px;
                border-top: 1px solid #dcdee0;
                display: flex;
                align-items: center;
                justify-content: space-around;
                .car-btn {
                    flex: 1;
                    color: #4b62ff;
                    font-size: 14px;
                    cursor: pointer;
                    border-right: 1px solid #dcdee0;
                    text-align: center;
                }
                /* 移除最后一个 .car-btn 的右边框 */
                .car-btn:last-child {
                    border-right: none;
                }
            }
            .text-ellipsis {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                line-height: 1.5em; /* 根据字体大小调整 */
                max-height: 3em; /* 行高 × 行数 */
            }
        }
    }
    .controls-box {
        width: 60px;
        background: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        position: absolute;
        top: 72px;
        right: 24px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .controls-boxitem {
            padding: 7px;
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            .bgimg {
                width: 24px;
                height: 24px;
                background-size: 100% 100%;
            }
            .bg1 {
                background-image: url('~@/assets/gj1.png');
            }
            .bg2 {
                background-image: url('~@/assets/xs1.png');
            }
            .bg3 {
                background-image: url('~@/assets/cc1.png');
            }
            .bg4 {
                background-image: url('~@/assets/tl1.png');
            }
            div {
                font-weight: 400;
                font-size: 13px;
                color: #333333;
            }
            &:hover {
                background: #3d4ff110;
                div {
                    font-weight: 500;
                    color: #3e53f2 !important;
                }
                .bg1 {
                    background-image: url('~@/assets/gj2.png');
                }
                .bg2 {
                    background-image: url('~@/assets/xs2.png');
                }
                .bg3 {
                    background-image: url('~@/assets/cc2.png');
                }
                .bg4 {
                    background-image: url('~@/assets/tl2.png');
                }
            }
        }
        .active {
            background: #3d4ff110;
            div {
                font-weight: 500;
                color: #3e53f2 !important;
            }
            .bg1 {
                background-image: url('~@/assets/gj2.png');
            }
            .bg2 {
                background-image: url('~@/assets/xs2.png');
            }
            .bg3 {
                background-image: url('~@/assets/cc2.png');
            }
            .bg4 {
                background-image: url('~@/assets/tl2.png');
            }
        }
    }
    ::v-deep .map_controlsseach {
        position: absolute;
        top: 16px;
        right: 24px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .seachbox {
            width: 185px;
            height: 40px;
            background: #ffffff;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);
            border-radius: 8px;
            padding: 0 12px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            .el-input__inner {
                border: none;
                padding-left: 5px;
            }
            .el-icon-search {
                color: #666;
            }
        }
        .mapchangebox {
            width: 125px;
            height: 40px;
            padding: 0 12px;
            background: #ffffff;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);
            border-radius: 8px;
            display: flex;
            align-items: center;
            .el-input__inner {
                padding: 0;
                border: none;
            }
            .el-select__caret {
                color: #666;
            }
        }
    }
    .map_controls {
        position: absolute;
        top: 106px;
        left: 16px;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
        font-size: 12px;
    }
}
.disab {
    cursor: not-allowed;
}
.allpointer {
    cursor: pointer;
}
.pointer {
    cursor: pointer;
}
.w100 {
    width: 100%;
}
::v-deep .custom_dialogtts {
  width: 438px;
  border-radius: 8px;
    .el-dialog__header {
      display: none;
    }
   .el-dialog__body {
    padding: 0px ;
    color: #333;
  }
}
</style>
<style lang="scss">
.Weathersbox-popover {
    padding: 0;
    border: none;
    box-shadow: none;
    background-color: #ffffff;
    height: 281px;
    border-radius: 12px 12px 12px 12px;
    overflow: hidden;
    .popper__arrow {
        display: none;
    }
    .header-box {
        height: 46px;
        display: flex;
        justify-content: space-between;
        border-radius: 12px 12px 0 0;
        overflow: hidden;
        background: #fff;
        .header-box-item {
            height: 50px;
            width: 50%;
            font-weight: 600;
            font-size: 14px;
            color: #333;
            line-height: 50px;
            text-align: center;
            cursor: pointer;
        }
        .active {
            color: #4a5eff;
            position: relative;
            &::before {
                content: ''; /* 必须设置，否则 ::before 不会生效 */
                display: block; /* 让伪元素变成块级元素 */
                position: absolute; /* 绝对定位，相对于 .original-box */
                left: 137px;
                top: 37px;
                width: 26px;
                height: 2px;
                background-color: #4a5eff;
            }
        }
        .bg1 {
            background-image: url('../../assets/tab1.png');
            background-size: cover;
        }
        .bg2 {
            background-image: url('../../assets/tab2.png');
            background-size: cover;
        }
    }
    .flexd {
        flex-direction: column;
        padding: 12px !important;
    }
    .Weatheritems {
        display: flex;
        height: 235px;
        position: relative;
        background-color: #fff;
        padding: 16px;
    }
    .Weathersbox-list {
        font-size: 12px;
        overflow: auto;
        height: 100%;
        border-top: 1px solid #dcdee0;
        border-bottom: 1px solid #dcdee0;
        &:first-child {
            border-left: 1px solid #dcdee0;
            border-radius: 8px 0px 0px 8px;
        }
        &:last-child {
            border-right: 1px solid #dcdee0;
        }
        &::-webkit-scrollbar {
            display: none;
        }
        .itembox {
            cursor: pointer;
            line-height: 42px;
            padding: 0 10px;
            background: #fff;
            width: 100px;
            display: flex;
            justify-content: space-between;
            color: #333;
            &.active {
                background: #409eff;
                font-weight: 500;
                color: #3d50f1;
                background: #f2f4fc;
            }
        }
    }
    .weatherstypelist {
        padding: 17px 0;
        display: flex;
        align-items: center;
        .listheader-item {
            padding: 6px;
            margin-right: 9px;
            border-radius: 4px;
            cursor: pointer;
            color: #666;
            border: 1px solid #dcdee0;
            &.active {
                color: #3d50f1;
                border: 1px solid #3d50f1;
            }
        }
        .btnbox {
            color: #3d50f1;
        }
    }
    .weatherstypelistbox {
        height: calc(100% - 68px);
        display: flex;
        overflow-x: auto; /* 横向滚动 */
        white-space: nowrap; /* 防止换行 */
        scrollbar-width: thin; /* 优化滚动条（Firefox） */
        .box-item {
            height: 100%;
            width: 237px;
            background: #f1f3ff;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
            margin-right: 12px;
            background-image: url('../../assets/tqbg.png');
            background-size: cover;
            .top {
                color: #333333;
                width: 100%;
                height: 44px;
                display: flex;
                align-items: center;
                background: linear-gradient(270deg, rgba(239, 241, 255, 0) 0%, #dadeff 100%);
                font-size: 14px;
                font-weight: 600;
                padding-left: 19px;
            }
            .bottom {
                display: flex;
                justify-content: space-between;
                padding: 13px;
                .imgbox {
                    width: 56px;
                    height: 56px;
                    background: #ffffff;
                    box-shadow: 0px 2px 10px 0px rgba(208, 214, 216, 0.52);
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    img {
                        width: 33px;
                        height: 33px;
                    }
                }
                .right {
                    .btnbox {
                        width: 90px;
                        height: 25px;
                        border-radius: 4px;
                        border: 1px solid #3265fe;
                        margin-bottom: 10px;
                        padding: 0 9px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        img {
                            width: 15px;
                            height: 15px;
                        }
                    }
                }
            }
        }
    }
}
.Weathersbox {
    position: absolute;
    bottom: 10px;
    left: 16px;
    transition: transform 0.3s ease;
    .bg1 {
        background-color: #fff;
        border-radius: 3px;
        padding: 5px 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333;
        cursor: pointer;
        img {
            width: 26px;
            height: 26px;
        }
        .ml20 {
            margin-left: 20px;
        }
        .showNowtq {
            font-size: 14px;
        }
        .checkbox {
            margin-bottom: 0px;
        }
    }
}
.tool-box-popover {
    .tool-box {
        display: flex;
        flex-wrap: wrap;
    }
    .tool-title {
        font-weight: 600;
        font-size: 14px;
        color: #000000;
        line-height: 20px;
        margin-bottom: 12px;
        padding-left: 12px;
        position: relative;
        width: 100%;
        &::before {
            content: ''; /* 必须设置，否则 ::before 不会生效 */
            display: block; /* 让伪元素变成块级元素 */
            position: absolute; /* 绝对定位，相对于 .original-box */
            left: 2px; /* 调整位置，使其出现在原有 DOM 左侧 */
            top: 3px;
            width: 2px;
            height: 14px;
            background: #4b62ff;
        }
    }
    .felx-box-item {
        height: 36px;
        line-height: 36px;
        margin-right: 4px;
        font-size: 14px;
        color: #111a34;
        cursor: pointer;
        display: flex;
        align-items: center;
        border-radius: 4px;
        padding: 0px 16px;
        &:hover {
            background: #eef1ff;
            color: #3d50f1;
        }
        img {
            width: 18px;
            height: 18px;
            margin-right: 4px;
        }
    }
    .w100 {
        width: 100%;
    }
    .checkbox-box-item {
        height: 36px;
        line-height: 36px;
        font-size: 14px;
        color: #111a34;
        cursor: pointer;
    }
    .active {
        color: #3d50f1;
        background: #eef1ff;
    }
    .disab {
        cursor: not-allowed;
    }
    .lcpointer {
        cursor: pointer;
    }
    .polygon_search_list {
        max-height: 200px;
        overflow: auto;
        margin: 6px 0;

        &::-webkit-scrollbar {
            display: block;
            width: 4px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background: #0001;
            border-radius: 4px;
        }

        .polygon_item {
            line-height: 26px;
            padding: 0 6px;
            cursor: pointer;

            &.selected,
            &:hover {
                background: #0001;
            }
        }
        .polygin_item_empty {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
.poi_search_autocomplete {
    width: 185px !important;
}
</style>
