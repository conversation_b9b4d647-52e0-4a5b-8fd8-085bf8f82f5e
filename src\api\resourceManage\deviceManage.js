import request from '@/router/axios'

// 设备信息管理
export function queryDeviceInfoPage(params) {
    return request({
        url: '/soc/device/pageList',
        method: 'get',
        params
    })
}

export function editDeviceInfo(data) {
    return request({
        url: '/soc/device/updateDevice',
        method: 'post',
        data
    })
}

export function exportDevice(data) {
    return request({
        url: '/soc/device/exportDevice',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}

// 下载设备导入模板
export function exportDeviceTemplate() {
    return request({
        url: '/soc/device/template',
        method: 'post',
    })
} 