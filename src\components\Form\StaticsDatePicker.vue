<template>
    <div class="date-type-picker">
        <!-- <el-form :inline="true"
                 label-width="120px"> -->
        <!-- <el-form-item label="时间维度:"> -->
        <!-- <span style="margin-right:10px;">时间维度：</span>
    <el-select v-model="dataForm.dateType"
               @change="changeDateType"
               style="width:80px !important;margin-right:10px;">
      <el-option v-for="item in dateTypeList"
                 :key="item.label"
                 :value="item.value"
                 :label="item.label">

      </el-option>
    </el-select> -->
        <!-- </el-form-item>
            <el-form-item> -->
        <el-date-picker
            v-if="dataForm.dateType === 1"
            key="dayRange"
            v-model="dayRange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :picker-options="propPickerOptions || pickerOptions"
            :clearable="clearable != null ? clearable : false"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 240px !important"
            @change="dateChange"
        >
        </el-date-picker>

        <el-date-picker
            v-else-if="dataForm.dateType === 3"
            key="monthRange"
            v-model="monthRange"
            type="monthrange"
            format="yyyy-MM"
            value-format="yyyy-MM"
            :picker-options="propPickerOptions || pickerOptions"
            :clearable="clearable != null ? clearable : false"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 220px !important"
            @change="monthDateChange"
        >
        </el-date-picker>
        <template v-else-if="dataForm.dateType === 2">
            <el-date-picker
                id="getStartWeek"
                key="weekStart"
                v-model="weekRange[0]"
                type="week"
                format="yyyy第WW周"
                value-format="yyyy-MM-dd"
                :picker-options="propPickerOptions || pickerOptions"
                :clearable="clearable != null ? clearable : false"
                placeholder="开始时间"
                style="width: 140px !important"
                @change="weekDateChange"
            >
            </el-date-picker>
            -
            <el-date-picker
                id="getEndWeek"
                key="weekEnd"
                v-model="weekRange[1]"
                type="week"
                format="yyyy第WW周"
                value-format="yyyy-MM-dd"
                :picker-options="propPickerOptions || pickerOptions"
                :clearable="clearable != null ? clearable : false"
                placeholder="结束时间"
                style="width: 140px !important"
                @change="weekDateChange"
            >
            </el-date-picker>
        </template>
        <template v-else>
            <el-date-picker
                v-model="timeRange"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="['00:00:00', '23:59:59']"
                :picker-options="propPickerOptions || pickerOptions"
                :clearable="clearable != null ? clearable : false"
                :style="{ width: width || '330px !important' }"
                @change="dateTimeChange"
            >
            </el-date-picker>
        </template>
        <!-- </el-form-item> -->
        <!-- </el-form> -->
    </div>
</template>

<script>
import dayjs from 'dayjs'

export default {
    model: {
        prop: 'dafaultForm',
        event: 'dateChange',
    },
    props: {
        dafaultForm: {
            type: Object,
            default: null,
        },
        width: String,
        propPickerOptions: Object,
        clearable: Boolean,
    },
    data() {
        return {
            pickerOptions: {
                firstDayOfWeek: 1,
                shortcuts: [
                    {
                        text: '当天',
                        onClick(picker) {
                            picker.$emit('pick', [
                                dayjs(dayjs().format('YYYY-MM-DD') + ' 00:00:00').format('YYYY-MM-DD HH:mm:ss'),
                                dayjs(dayjs().format('YYYY-MM-DD') + ' 23:59:59').format('YYYY-MM-DD HH:mm:ss'),
                            ])
                        },
                    },
                    {
                        text: '当月',
                        onClick(picker) {
                            picker.$emit('pick', [
                                dayjs(dayjs().format('YYYY-MM') + '-01 00:00:00').format('YYYY-MM-DD HH:mm:ss'),
                                dayjs(dayjs().format('YYYY-MM-DD') + ' 23:59:59').format('YYYY-MM-DD HH:mm:ss'),
                            ])
                        },
                    },
                ],
            },
            dateTypeList: [
                { value: 1, label: '日' },
                { value: 2, label: '周' },
                { value: 3, label: '月' },
            ],
            //   pickerTypeMap: {
            //     day: { type: "daterange", defaultDate: [dayjs().format()] },
            //     week: { type: "weekrange", defaultDate: [] },
            //     month: { type: "monthrange", defaultDate: [] }
            //   },
            dayRange: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
            weekRange: [dayjs().subtract(28, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
            monthRange: [dayjs().subtract(1, 'month').format('YYYY-MM'), dayjs().format('YYYY-MM')],
            timeRange: [
                dayjs(dayjs().format('YYYY-MM-DD') + ' 00:00:00').format('YYYY-MM-DD HH:mm:ss'),
                dayjs(dayjs().format('YYYY-MM-DD') + ' 23:59:59').format('YYYY-MM-DD HH:mm:ss'),
            ],
            dataForm: { dateType: 1, startTime: '', endTime: '' },
        }
    },
    watch: {
        dafaultForm() {
            this.initDateForm()
        },
    },
    mounted() {
        this.dafaultForm && this.initDateForm()
    },
    methods: {
        initDateForm() {
            let { dateType, startTime, endTime } = this.dafaultForm
            this.dataForm.dateType = dateType
            switch (dateType) {
                case 1:
                    this.dayRange = [startTime, endTime]
                    break
                case 2:
                    this.weekRange = [startTime, endTime]
                    break
                case 3:
                    this.monthRange = [startTime, endTime]
                    break
                case 4:
                    this.timeRange = [startTime, endTime]
                    break
                default:
                    break
            }
        },
        dateChange() {
            this.dataForm = Object.assign({}, this.dataForm, {
                startTime: this.dayRange ? this.dayRange[0] : '',
                endTime: this.dayRange ? this.dayRange[1] : '',
            })
            this.$emit('dateChange', this.dataForm)
        },
        weekDateChange() {
            let startDay = dayjs(this.weekRange[0]).day(),
                endDay = (7 - dayjs(this.weekRange[1]).day()) % 7
            this.dataForm = Object.assign({}, this.dataForm, {
                startTime: dayjs(this.weekRange[0])
                    .subtract(startDay - 1, 'day')
                    .format('YYYY-MM-DD'),
                endTime: dayjs(this.weekRange[1]).add(endDay, 'day').format('YYYY-MM-DD'),
            })

            this.$nextTick(() => {
                var startWeekValue = document.querySelector('#getStartWeek').value
                var endWeekValue = document.querySelector('#getEndWeek').value
                this.$emit('dateChange', Object.assign({ startWeekValue, endWeekValue }, this.dataForm))
            })
        },
        monthDateChange() {
            this.dataForm = Object.assign({}, this.dataForm, {
                startTime: this.monthRange[0] + '-01',
                endTime: this.monthRange[1] + '-31',
            })
            this.$emit('dateChange', this.dataForm)
        },
        dateTimeChange() {
            if (this.timeRange) {
                this.dataForm = Object.assign({}, this.dataForm, {
                    startTime: this.timeRange[0],
                    endTime: this.timeRange[1],
                })
            } else {
                this.dataForm = Object.assign({}, this.dataForm, {
                    startTime: '',
                    endTime: '',
                })
            }
            this.$emit('dateChange', this.dataForm)
        },
    },
}
</script>

<style lang="scss" scoped>
.date-type-picker {
    display: inline-block;
    margin-right: 10px;
}
</style>
