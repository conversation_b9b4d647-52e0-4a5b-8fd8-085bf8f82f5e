import request from '@/router/axios'

// 新增规则
export function addRule(data) {
    return request({
        url: '/soc/inspection/rule/add',
        method: 'post',
        data,
    })
}

//编辑规则
export function updateRule(data) {
    return request({
        url: '/soc/inspection/rule/update',
        method: 'post',
        data,
    })
}

// 删除规则
export function delRule(id) {
    return request({
      url: '/soc/inspection/rule/del/' + id,
      method: 'delete'
    })
}

// 分页查询
export function queryPage(data) {
    return request({
        url: '/soc/inspection/rule/page',
        methods: 'get',
        params: data
    })
}
