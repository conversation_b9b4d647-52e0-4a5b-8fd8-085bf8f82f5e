import request from '@/router/axios'

//刷新报警
export function queryAlarmRecent(obj) {
    return request({
        url: '/platform/alarm/queryAlarmRecent/' + obj.type,
        method: 'post',
        data: obj,
    })
}

// 提交任务
export function submitTask(obj) {
    return request({
        url: '/platform/alarm/submitTask',
        method: 'post',
        data: obj,
    })
}
// 审核升级干预
export function audit2Meddle(obj) {
    return request({
        url: '/platform/alarm/audit2Meddle',
        method: 'post',
        data: obj,
    })
}
// 审核升级护航
export function audit2Defend(obj) {
    return request({
        url: '/platform/alarm/audit2Defend',
        method: 'post',
        data: obj,
    })
}
// 干预升级护航
export function meddle2Defend(obj) {
    return request({
        url: '/platform/alarm/meddle2Defend',
        method: 'post',
        data: obj,
    })
}

//干预工作-任务回收
export function recoverAlarmById(obj) {
    return request({
        url: '/platform/alarm/recoverAlarmById',
        method: 'post',
        data: obj,
    })
}

//平均干预时长
export function getAlarmInterveneTime(obj) {
    return request({
        url: '/platform/alarm/queryMeddleSta/' + obj.type,
        method: 'post',
        data: obj,
    })
}

//手动获取附件
export function queryAlarmMediaByRemote(obj) {
    return request({
        url: '/fleet/alarm/queryAlarmMediaByRemote',
        method: 'get',
        params: obj,
    })
}

// 干预明细
export function queryAlarmMeddlePage(obj) {
    return request({
        url: '/soc/alarm/queryAlarmMeddlePage',
        method: 'post',
        data: obj,
    })
}

export function exportAlarmMeddle(data) {
    return request({
        url: '/rbi/alarm/exportAlarmMeddle',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer',
    })
}

// 获取最后一次干预记录
export function queryLatelyMeddle(obj) {
    return request({
        url: '/platform/alarm/latelyMeddle',
        method: 'get',
        params: obj,
    })
}

/**
 * 获取今日驾驶时长
 * @param {string} driverCode
 * @returns
 */
export function getDrivingHours(driverCode) {
    return request({
        url: '/erp/driverLog/getDrivingHours',
        method: 'get',
        params: { empCode: driverCode },
    })
}

// 请求录音文件详细地址
// /alarm/getAudioUrl
// post json
// 输入参数
// audio
// 输出：
// code =0，data=实际url
export function alarm_getAudioUrl(data) {
    return request.get('/soc/call/getRecord?callId=' + data)
}

// 请求对比视频
// /alarm/queryCompareVideo
// post json
// 输入参数
// alarmId
// warnId
// 输出：
// {
// "url":"" //3-提取完成才有
// "status":0 //0-工单未创建，1-工单已创建，2-提取中，3-提取完成，4-提取失败
// }
export function alarm_queryCompareVideo(data) {
    return request.post('/soc/alarm/queryCompareVideo', data)
}
// 请求报警操作日志
// {
// 	"alarmId": "68a6961b980d81115de97226", //报警ID（必传）
// 	"warnId": "1755747865000"  //报警时间戳（必传）
// }

export function alarm_queryAlarmProcessLog(data) {
    return request.post('/soc/alarm/queryAlarmProcessLog', data)
}
// 查询视频
// {
// 	"alarmId": "68a6961b980d81115de97226", //报警ID（必传）
// 	"warnId": "1755747865000"  //报警时间戳（必传）
// }

export function alarm_queryAlarmAttachInfo(data) {
    return request.post('/soc/alarm/queryAlarmAttachInfo', data)
}
