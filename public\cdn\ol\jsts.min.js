/**
 * JSTS. See https://github.com/bjornharrtell/jsts
 * https://github.com/bjornharrtell/jsts/blob/master/LICENSE_EDLv1.txt
 * https://github.com/bjornharrtell/jsts/blob/master/LICENSE_EPLv1.txt
 * https://github.com/bjornharrtell/jsts/blob/master/LICENSE_LICENSE_ES6_COLLECTIONS.txt
 * @license
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.jsts={})}(this,function(t){"use strict";function e(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])}function i(){}function c(){}function h(t,e){this.low=0|e,this.high=0|t}function S(){}var f,g,d,_,r,s,o;function n(){}function a(){}function u(){}function l(){}function v(t){this.name="RuntimeException",this.message=t,this.stack=(new Error).stack,Error.call(this,t)}function p(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t}function m(){if(0===arguments.length)v.call(this);else if(1===arguments.length){var t=arguments[0];v.call(this,t)}}function y(){}function x(){if(this.x=null,this.y=null,this.z=null,0===arguments.length)x.call(this,0,0);else if(1===arguments.length){var t=arguments[0];x.call(this,t.x,t.y,t.z)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];x.call(this,e,n,x.NULL_ORDINATE)}else if(3===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2];this.x=i,this.y=r,this.z=s}}function E(){if(this._dimensionsToTest=2,0===arguments.length)E.call(this,2);else if(1===arguments.length){var t=arguments[0];if(2!==t&&3!==t)throw new c("only 2 or 3 dimensions may be specified");this._dimensionsToTest=t}}function L(t,e){return t.interfaces_&&-1<t.interfaces_().indexOf(e)}function I(){}function N(){}function C(t){this.message=t||""}function w(){}function R(t){this.message=t||""}function T(t){this.message=t||""}function P(){this.array_=[],arguments[0]instanceof N&&this.addAll(arguments[0])}"fill"in Array.prototype||Object.defineProperty(Array.prototype,"fill",{configurable:!0,value:function(t){if(null==this)throw new TypeError(this+" is not an object");var e=Object(this),n=Math.max(Math.min(e.length,9007199254740991),0)||0,i=1 in arguments&&parseInt(Number(arguments[1]),10)||0;i=i<0?Math.max(n+i,0):Math.min(i,n);var r=2 in arguments&&void 0!==arguments[2]?parseInt(Number(arguments[2]),10)||0:n;for(r=r<0?Math.max(n+arguments[2],0):Math.min(r,n);i<r;)e[i]=t,++i;return e},writable:!0}),Number.isFinite=Number.isFinite||function(t){return"number"==typeof t&&isFinite(t)},Number.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},Number.parseFloat=Number.parseFloat||parseFloat,Number.isNaN=Number.isNaN||function(t){return t!=t},Math.trunc=Math.trunc||function(t){return t<0?Math.ceil(t):Math.floor(t)},Math.log2=Math.log2||function(t){return Math.log(t)*Math.LOG2E},e(i.prototype,{interfaces_:function(){return[]},getClass:function(){return i}}),i.equalsWithTolerance=function(t,e,n){return Math.abs(t-e)<=n},h.toBinaryString=function(t){for(var e="",n=2147483648;0<n;n>>>=1)e+=(t.high&n)===n?"1":"0";for(n=2147483648;0<n;n>>>=1)e+=(t.low&n)===n?"1":"0";return e},S.isNaN=function(t){return Number.isNaN(t)},S.isInfinite=function(t){return!Number.isFinite(t)},S.MAX_VALUE=Number.MAX_VALUE,"function"==typeof Float64Array&&"function"==typeof Int32Array?(r=2146435072,s=new Float64Array(1),o=new Int32Array(s.buffer),S.doubleToLongBits=function(t){s[0]=t;var e=0|o[0],n=0|o[1];return(n&r)===r&&0!=(1048575&n)&&0!==e&&(e=0,n=2146959360),new h(n,e)},S.longBitsToDouble=function(t){return o[0]=t.low,o[1]=t.high,s[0]}):(f=Math.log2,g=Math.floor,d=Math.pow,_=function(){for(var t=53;0<t;t--){var e=d(2,t)-1;if(g(f(e))+1===t)return e}return 0}(),S.doubleToLongBits=function(t){var e,n,i,r,s,o,a,u,l;if(t<0||1/t===Number.NEGATIVE_INFINITY?(o=1<<31,t=-t):o=0,0===t)return new h(u=o,l=0);if(t===1/0)return new h(u=2146435072|o,l=0);if(t!=t)return new h(u=2146959360,l=0);if(l=r=0,1<(e=g(t)))if(e<=_)(r=g(f(e)))<=20?(l=0,u=e<<20-r&1048575):(l=e%(n=d(2,i=r-20))<<32-i,u=e/n&1048575);else for(i=e,l=0;0!==(i=g(n=i/2));)r++,l>>>=1,l|=(1&u)<<31,u>>>=1,n!==i&&(u|=524288);if(a=r+1023,s=0===e,e=t-e,r<52&&0!==e)for(i=0;;){if(1<=(n=2*e)?(e=n-1,s?(a--,s=!1):(i<<=1,i|=1,r++)):(e=n,s?0==--a&&(r++,s=!1):(i<<=1,r++)),20===r)u|=i,i=0;else if(52===r){l|=i;break}if(1===n){r<20?u|=i<<20-r:r<52&&(l|=i<<52-r);break}}return u|=a<<20,new h(u|=o,l)},S.longBitsToDouble=function(t){var e,n,i,r,s=t.high,o=t.low;n=s&1<<31?-1:1,i=((2146435072&s)>>20)-1023,r=0,e=1<<19;for(var a=1;a<=20;a++)s&e&&(r+=d(2,-a)),e>>>=1;for(e=1<<31,a=21;a<=52;a++)o&e&&(r+=d(2,-a)),e>>>=1;if(-1023===i){if(0===r)return 0*n;i=-1022}else{if(1024===i)return 0===r?n/0:NaN;r+=1}return n*r*d(2,i)}),(v.prototype=Object.create(Error.prototype)).constructor=Error,p(m,v),e(m.prototype,{interfaces_:function(){return[]},getClass:function(){return m}}),e(y.prototype,{interfaces_:function(){return[]},getClass:function(){return y}}),y.shouldNeverReachHere=function(){if(0===arguments.length)y.shouldNeverReachHere(null);else if(1===arguments.length){var t=arguments[0];throw new m("Should never reach here"+(null!==t?": "+t:""))}},y.isTrue=function(){if(1===arguments.length){var t=arguments[0];y.isTrue(t,null)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];if(!e)throw null===n?new m:new m(n)}},y.equals=function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];y.equals(t,e,null)}else if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];if(!i.equals(n))throw new m("Expected "+n+" but encountered "+i+(null!==r?": "+r:""))}},e(x.prototype,{setOrdinate:function(t,e){switch(t){case x.X:this.x=e;break;case x.Y:this.y=e;break;case x.Z:this.z=e;break;default:throw new c("Invalid ordinate index: "+t)}},equals2D:function(){if(1===arguments.length){var t=arguments[0];return this.x===t.x&&this.y===t.y}if(2===arguments.length){var e=arguments[0],n=arguments[1];return!!i.equalsWithTolerance(this.x,e.x,n)&&!!i.equalsWithTolerance(this.y,e.y,n)}},getOrdinate:function(t){switch(t){case x.X:return this.x;case x.Y:return this.y;case x.Z:return this.z}throw new c("Invalid ordinate index: "+t)},equals3D:function(t){return this.x===t.x&&this.y===t.y&&(this.z===t.z||S.isNaN(this.z)&&S.isNaN(t.z))},equals:function(t){return t instanceof x&&this.equals2D(t)},equalInZ:function(t,e){return i.equalsWithTolerance(this.z,t.z,e)},compareTo:function(t){var e=t;return this.x<e.x?-1:this.x>e.x?1:this.y<e.y?-1:this.y>e.y?1:0},clone:function(){try{return null}catch(t){if(t instanceof CloneNotSupportedException)return y.shouldNeverReachHere("this shouldn't happen because this class is Cloneable"),null;throw t}},copy:function(){return new x(this)},toString:function(){return"("+this.x+", "+this.y+", "+this.z+")"},distance3D:function(t){var e=this.x-t.x,n=this.y-t.y,i=this.z-t.z;return Math.sqrt(e*e+n*n+i*i)},distance:function(t){var e=this.x-t.x,n=this.y-t.y;return Math.sqrt(e*e+n*n)},hashCode:function(){var t=17;return t=37*(t=37*t+x.hashCode(this.x))+x.hashCode(this.y)},setCoordinate:function(t){this.x=t.x,this.y=t.y,this.z=t.z},interfaces_:function(){return[n,a,l]},getClass:function(){return x}}),x.hashCode=function(){if(1===arguments.length&&"number"==typeof arguments[0]){var t=arguments[0],e=S.doubleToLongBits(t);return Math.trunc(e^e>>>32)}},e(E.prototype,{compare:function(t,e){var n=t,i=e,r=E.compare(n.x,i.x);if(0!==r)return r;var s=E.compare(n.y,i.y);return 0!==s?s:this._dimensionsToTest<=2?0:E.compare(n.z,i.z)},interfaces_:function(){return[u]},getClass:function(){return E}}),E.compare=function(t,e){return t<e?-1:e<t?1:S.isNaN(t)?S.isNaN(e)?0:-1:S.isNaN(e)?1:0},x.DimensionalComparator=E,x.serialVersionUID=0x5cbf2c235c7e5800,x.NULL_ORDINATE=S.NaN,x.X=0,x.Y=1,x.Z=2,I.prototype.hasNext=function(){},I.prototype.next=function(){},I.prototype.remove=function(){},N.prototype.add=function(){},N.prototype.addAll=function(){},N.prototype.isEmpty=function(){},N.prototype.iterator=function(){},N.prototype.size=function(){},N.prototype.toArray=function(){},N.prototype.remove=function(){},(C.prototype=new Error).name="IndexOutOfBoundsException",((w.prototype=Object.create(N.prototype)).constructor=w).prototype.get=function(){},w.prototype.set=function(){},w.prototype.isEmpty=function(){},(R.prototype=new Error).name="NoSuchElementException",(T.prototype=new Error).name="OperationNotSupported",((P.prototype=Object.create(w.prototype)).constructor=P).prototype.ensureCapacity=function(){},P.prototype.interfaces_=function(){return[w,N]},P.prototype.add=function(t){return 1===arguments.length?this.array_.push(t):this.array_.splice(t,0,arguments[1]),!0},P.prototype.clear=function(){this.array_=[]},P.prototype.addAll=function(t){for(var e=t.iterator();e.hasNext();)this.add(e.next());return!0},P.prototype.set=function(t,e){var n=this.array_[t];return this.array_[t]=e,n},P.prototype.iterator=function(){return new O(this)},P.prototype.get=function(t){if(t<0||t>=this.size())throw new C;return this.array_[t]},P.prototype.isEmpty=function(){return 0===this.array_.length},P.prototype.size=function(){return this.array_.length},P.prototype.toArray=function(){for(var t=[],e=0,n=this.array_.length;e<n;e++)t.push(this.array_[e]);return t},P.prototype.remove=function(t){for(var e=!1,n=0,i=this.array_.length;n<i;n++)if(this.array_[n]===t){this.array_.splice(n,1),e=!0;break}return e};var O=function(t){this.arrayList_=t,this.position_=0};function b(){if(P.apply(this),0===arguments.length);else if(1===arguments.length){var t=arguments[0];this.ensureCapacity(t.length),this.add(t,!0)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this.ensureCapacity(e.length),this.add(e,n)}}function M(){if(this._minx=null,this._maxx=null,this._miny=null,this._maxy=null,0===arguments.length)this.init();else if(1===arguments.length){if(arguments[0]instanceof x){var t=arguments[0];this.init(t.x,t.x,t.y,t.y)}else if(arguments[0]instanceof M){var e=arguments[0];this.init(e)}}else if(2===arguments.length){var n=arguments[0],i=arguments[1];this.init(n.x,i.x,n.y,i.y)}else if(4===arguments.length){var r=arguments[0],s=arguments[1],o=arguments[2],a=arguments[3];this.init(r,s,o,a)}}function D(){}function A(){D.call(this,"Projective point not representable on the Cartesian plane.")}function F(t){this.str=t}function G(t){this.value=t}function q(){}function B(){if(this._hi=0,(this._lo=0)===arguments.length)this.init(0);else if(1===arguments.length){if("number"==typeof arguments[0]){var t=arguments[0];this.init(t)}else if(arguments[0]instanceof B){var e=arguments[0];this.init(e)}else if("string"==typeof arguments[0]){var n=arguments[0];B.call(this,B.parse(n))}}else if(2===arguments.length){var i=arguments[0],r=arguments[1];this.init(i,r)}}function z(){}function V(){}function Y(){}function k(){if(this.x=null,this.y=null,this.w=null,0===arguments.length)this.x=0,this.y=0,this.w=1;else if(1===arguments.length){var t=arguments[0];this.x=t.x,this.y=t.y,this.w=1}else if(2===arguments.length){if("number"==typeof arguments[0]&&"number"==typeof arguments[1]){var e=arguments[0],n=arguments[1];this.x=e,this.y=n,this.w=1}else if(arguments[0]instanceof k&&arguments[1]instanceof k){var i=arguments[0],r=arguments[1];this.x=i.y*r.w-r.y*i.w,this.y=r.x*i.w-i.x*r.w,this.w=i.x*r.y-r.x*i.y}else if(arguments[0]instanceof x&&arguments[1]instanceof x){var s=arguments[0],o=arguments[1];this.x=s.y-o.y,this.y=o.x-s.x,this.w=s.x*o.y-o.x*s.y}}else if(3===arguments.length){var a=arguments[0],u=arguments[1],l=arguments[2];this.x=a,this.y=u,this.w=l}else if(4===arguments.length){var h=arguments[0],c=arguments[1],f=arguments[2],g=arguments[3],d=h.y-c.y,_=c.x-h.x,p=h.x*c.y-c.x*h.y,m=f.y-g.y,v=g.x-f.x,y=f.x*g.y-g.x*f.y;this.x=_*y-v*p,this.y=m*p-d*y,this.w=d*v-m*_}}function U(){}function X(){}function H(){}function W(){}function j(){}function K(){this._envelope=null,this._factory=null,this._SRID=null,this._userData=null;var t=arguments[0];this._factory=t,this._SRID=t.getSRID()}function Z(){}function Q(){}function J(){}function $(){}function tt(){}function et(){}function nt(){}function it(){}function rt(){}function st(){}function ot(){}function at(){}function ut(){this.array_=[],arguments[0]instanceof N&&this.addAll(arguments[0])}O.prototype.next=function(){if(this.position_===this.arrayList_.size())throw new R;return this.arrayList_.get(this.position_++)},O.prototype.hasNext=function(){return this.position_<this.arrayList_.size()},O.prototype.set=function(t){return this.arrayList_.set(this.position_-1,t)},O.prototype.remove=function(){this.arrayList_.remove(this.arrayList_.get(this.position_))},p(b,P),e(b.prototype,{getCoordinate:function(t){return this.get(t)},addAll:function(){if(2===arguments.length&&"boolean"==typeof arguments[1]&&L(arguments[0],N)){for(var t=arguments[0],e=arguments[1],n=!1,i=t.iterator();i.hasNext();)this.add(i.next(),e),n=!0;return n}return P.prototype.addAll.apply(this,arguments)},clone:function(){for(var t=P.prototype.clone.call(this),e=0;e<this.size();e++)t.add(e,this.get(e).clone());return t},toCoordinateArray:function(){return this.toArray(b.coordArrayType)},add:function(){if(1===arguments.length){var t=arguments[0];P.prototype.add.call(this,t)}else if(2===arguments.length){if(arguments[0]instanceof Array&&"boolean"==typeof arguments[1]){var e=arguments[0],n=arguments[1];return this.add(e,n,!0),!0}if(arguments[0]instanceof x&&"boolean"==typeof arguments[1]){var i=arguments[0];if(!arguments[1])if(1<=this.size())if(this.get(this.size()-1).equals2D(i))return null;P.prototype.add.call(this,i)}else if(arguments[0]instanceof Object&&"boolean"==typeof arguments[1]){var r=arguments[0],s=arguments[1];return this.add(r,s),!0}}else if(3===arguments.length){if("boolean"==typeof arguments[2]&&arguments[0]instanceof Array&&"boolean"==typeof arguments[1]){var o=arguments[0],a=arguments[1];if(arguments[2])for(var u=0;u<o.length;u++)this.add(o[u],a);else for(u=o.length-1;0<=u;u--)this.add(o[u],a);return!0}if("boolean"==typeof arguments[2]&&Number.isInteger(arguments[0])&&arguments[1]instanceof x){var l=arguments[0],h=arguments[1];if(!arguments[2]){var c=this.size();if(0<c){if(0<l)if(this.get(l-1).equals2D(h))return null;if(l<c)if(this.get(l).equals2D(h))return null}}P.prototype.add.call(this,l,h)}}else if(4===arguments.length){var f=arguments[0],g=arguments[1],d=arguments[2],_=arguments[3],p=1;_<d&&(p=-1);for(u=d;u!==_;u+=p)this.add(f[u],g);return!0}},closeRing:function(){0<this.size()&&this.add(new x(this.get(0)),!1)},interfaces_:function(){return[]},getClass:function(){return b}}),b.coordArrayType=new Array(0).fill(null),e(M.prototype,{getArea:function(){return this.getWidth()*this.getHeight()},equals:function(t){if(!(t instanceof M))return!1;var e=t;return this.isNull()?e.isNull():this._maxx===e.getMaxX()&&this._maxy===e.getMaxY()&&this._minx===e.getMinX()&&this._miny===e.getMinY()},intersection:function(t){if(this.isNull()||t.isNull()||!this.intersects(t))return new M;var e=this._minx>t._minx?this._minx:t._minx,n=this._miny>t._miny?this._miny:t._miny;return new M(e,this._maxx<t._maxx?this._maxx:t._maxx,n,this._maxy<t._maxy?this._maxy:t._maxy)},isNull:function(){return this._maxx<this._minx},getMaxX:function(){return this._maxx},covers:function(){if(1===arguments.length){if(arguments[0]instanceof x){var t=arguments[0];return this.covers(t.x,t.y)}if(arguments[0]instanceof M){var e=arguments[0];return!this.isNull()&&!e.isNull()&&(e.getMinX()>=this._minx&&e.getMaxX()<=this._maxx&&e.getMinY()>=this._miny&&e.getMaxY()<=this._maxy)}}else if(2===arguments.length){var n=arguments[0],i=arguments[1];return!this.isNull()&&(n>=this._minx&&n<=this._maxx&&i>=this._miny&&i<=this._maxy)}},intersects:function(){if(1===arguments.length){if(arguments[0]instanceof M){var t=arguments[0];return!this.isNull()&&!t.isNull()&&!(t._minx>this._maxx||t._maxx<this._minx||t._miny>this._maxy||t._maxy<this._miny)}if(arguments[0]instanceof x){var e=arguments[0];return this.intersects(e.x,e.y)}}else if(2===arguments.length){if(arguments[0]instanceof x&&arguments[1]instanceof x){var n=arguments[0],i=arguments[1];return!this.isNull()&&(!((n.x<i.x?n.x:i.x)>this._maxx)&&(!((n.x>i.x?n.x:i.x)<this._minx)&&(!((n.y<i.y?n.y:i.y)>this._maxy)&&!((n.y>i.y?n.y:i.y)<this._miny))))}if("number"==typeof arguments[0]&&"number"==typeof arguments[1]){var r=arguments[0],s=arguments[1];return!this.isNull()&&!(r>this._maxx||r<this._minx||s>this._maxy||s<this._miny)}}},getMinY:function(){return this._miny},getMinX:function(){return this._minx},expandToInclude:function(){if(1===arguments.length){if(arguments[0]instanceof x){var t=arguments[0];this.expandToInclude(t.x,t.y)}else if(arguments[0]instanceof M){var e=arguments[0];if(e.isNull())return null;this.isNull()?(this._minx=e.getMinX(),this._maxx=e.getMaxX(),this._miny=e.getMinY(),this._maxy=e.getMaxY()):(e._minx<this._minx&&(this._minx=e._minx),e._maxx>this._maxx&&(this._maxx=e._maxx),e._miny<this._miny&&(this._miny=e._miny),e._maxy>this._maxy&&(this._maxy=e._maxy))}}else if(2===arguments.length){var n=arguments[0],i=arguments[1];this.isNull()?(this._minx=n,this._maxx=n,this._miny=i,this._maxy=i):(n<this._minx&&(this._minx=n),n>this._maxx&&(this._maxx=n),i<this._miny&&(this._miny=i),i>this._maxy&&(this._maxy=i))}},minExtent:function(){if(this.isNull())return 0;var t=this.getWidth(),e=this.getHeight();return t<e?t:e},getWidth:function(){return this.isNull()?0:this._maxx-this._minx},compareTo:function(t){var e=t;return this.isNull()?e.isNull()?0:-1:e.isNull()?1:this._minx<e._minx?-1:this._minx>e._minx?1:this._miny<e._miny?-1:this._miny>e._miny?1:this._maxx<e._maxx?-1:this._maxx>e._maxx?1:this._maxy<e._maxy?-1:this._maxy>e._maxy?1:0},translate:function(t,e){if(this.isNull())return null;this.init(this.getMinX()+t,this.getMaxX()+t,this.getMinY()+e,this.getMaxY()+e)},toString:function(){return"Env["+this._minx+" : "+this._maxx+", "+this._miny+" : "+this._maxy+"]"},setToNull:function(){this._minx=0,this._maxx=-1,this._miny=0,this._maxy=-1},getHeight:function(){return this.isNull()?0:this._maxy-this._miny},maxExtent:function(){if(this.isNull())return 0;var t=this.getWidth(),e=this.getHeight();return e<t?t:e},expandBy:function(){if(1===arguments.length){var t=arguments[0];this.expandBy(t,t)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];if(this.isNull())return null;this._minx-=e,this._maxx+=e,this._miny-=n,this._maxy+=n,(this._minx>this._maxx||this._miny>this._maxy)&&this.setToNull()}},contains:function(){if(1===arguments.length){if(arguments[0]instanceof M){var t=arguments[0];return this.covers(t)}if(arguments[0]instanceof x){var e=arguments[0];return this.covers(e)}}else if(2===arguments.length){var n=arguments[0],i=arguments[1];return this.covers(n,i)}},centre:function(){return this.isNull()?null:new x((this.getMinX()+this.getMaxX())/2,(this.getMinY()+this.getMaxY())/2)},init:function(){if(0===arguments.length)this.setToNull();else if(1===arguments.length){if(arguments[0]instanceof x){var t=arguments[0];this.init(t.x,t.x,t.y,t.y)}else if(arguments[0]instanceof M){var e=arguments[0];this._minx=e._minx,this._maxx=e._maxx,this._miny=e._miny,this._maxy=e._maxy}}else if(2===arguments.length){var n=arguments[0],i=arguments[1];this.init(n.x,i.x,n.y,i.y)}else if(4===arguments.length){var r=arguments[0],s=arguments[1],o=arguments[2],a=arguments[3];r<s?(this._minx=r,this._maxx=s):(this._minx=s,this._maxx=r),o<a?(this._miny=o,this._maxy=a):(this._miny=a,this._maxy=o)}},getMaxY:function(){return this._maxy},distance:function(t){if(this.intersects(t))return 0;var e=0;this._maxx<t._minx?e=t._minx-this._maxx:this._minx>t._maxx&&(e=this._minx-t._maxx);var n=0;return this._maxy<t._miny?n=t._miny-this._maxy:this._miny>t._maxy&&(n=this._miny-t._maxy),0===e?n:0===n?e:Math.sqrt(e*e+n*n)},hashCode:function(){var t=17;return t=37*(t=37*(t=37*(t=37*t+x.hashCode(this._minx))+x.hashCode(this._maxx))+x.hashCode(this._miny))+x.hashCode(this._maxy)},interfaces_:function(){return[n,l]},getClass:function(){return M}}),M.intersects=function(){if(3===arguments.length){var t=arguments[0],e=arguments[1],n=arguments[2];return n.x>=(t.x<e.x?t.x:e.x)&&n.x<=(t.x>e.x?t.x:e.x)&&n.y>=(t.y<e.y?t.y:e.y)&&n.y<=(t.y>e.y?t.y:e.y)}if(4===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2],o=arguments[3],a=Math.min(s.x,o.x),u=Math.max(s.x,o.x),l=Math.min(i.x,r.x),h=Math.max(i.x,r.x);return!(u<l)&&(!(h<a)&&(a=Math.min(s.y,o.y),u=Math.max(s.y,o.y),l=Math.min(i.y,r.y),h=Math.max(i.y,r.y),!(u<l)&&!(h<a)))}},M.serialVersionUID=0x51845cd552189800,p(A,D),e(A.prototype,{interfaces_:function(){return[]},getClass:function(){return A}}),F.prototype.append=function(t){this.str+=t},F.prototype.setCharAt=function(t,e){this.str=this.str.substr(0,t)+e+this.str.substr(t+1)},F.prototype.toString=function(t){return this.str},G.prototype.intValue=function(){return this.value},G.prototype.compareTo=function(t){return this.value<t?-1:this.value>t?1:0},G.isNaN=function(t){return Number.isNaN(t)},q.isWhitespace=function(t){return t<=32&&0<=t||127==t},q.toUpperCase=function(t){return t.toUpperCase()},e(B.prototype,{le:function(t){return this._hi<t._hi||this._hi===t._hi&&this._lo<=t._lo},extractSignificantDigits:function(t,e){var n=this.abs(),i=B.magnitude(n._hi),r=B.TEN.pow(i);(n=n.divide(r)).gt(B.TEN)?(n=n.divide(B.TEN),i+=1):n.lt(B.ONE)&&(n=n.multiply(B.TEN),i-=1);for(var s=i+1,o=new F,a=B.MAX_PRINT_DIGITS-1,u=0;u<=a;u++){t&&u===s&&o.append(".");var l=Math.trunc(n._hi);if(l<0)break;var h=!1,c=0;9<l?(h=!0,c="9"):c="0"+l,o.append(c),n=n.subtract(B.valueOf(l)).multiply(B.TEN),h&&n.selfAdd(B.TEN);var f=!0,g=B.magnitude(n._hi);if(g<0&&Math.abs(g)>=a-u&&(f=!1),!f)break}return e[0]=i,o.toString()},sqr:function(){return this.multiply(this)},doubleValue:function(){return this._hi+this._lo},subtract:function(){if(arguments[0]instanceof B){var t=arguments[0];return this.add(t.negate())}if("number"==typeof arguments[0]){var e=arguments[0];return this.add(-e)}},equals:function(){if(1===arguments.length&&arguments[0]instanceof B){var t=arguments[0];return this._hi===t._hi&&this._lo===t._lo}},isZero:function(){return 0===this._hi&&0===this._lo},selfSubtract:function(){if(arguments[0]instanceof B){var t=arguments[0];return this.isNaN()?this:this.selfAdd(-t._hi,-t._lo)}if("number"==typeof arguments[0]){var e=arguments[0];return this.isNaN()?this:this.selfAdd(-e,0)}},getSpecialNumberString:function(){return this.isZero()?"0.0":this.isNaN()?"NaN ":null},min:function(t){return this.le(t)?this:t},selfDivide:function(){if(1===arguments.length){if(arguments[0]instanceof B){var t=arguments[0];return this.selfDivide(t._hi,t._lo)}if("number"==typeof arguments[0]){var e=arguments[0];return this.selfDivide(e,0)}}else if(2===arguments.length){var n,i,r,s,o=arguments[0],a=arguments[1],u=null,l=null,h=null,c=null;return r=this._hi/o,c=(u=(h=B.SPLIT*r)-(u=h-r))*(l=(c=B.SPLIT*o)-(l=c-o))-(s=r*o)+u*(i=o-l)+(n=r-u)*l+n*i,c=r+(h=(this._hi-s-c+this._lo-r*a)/o),this._hi=c,this._lo=r-c+h,this}},dump:function(){return"DD<"+this._hi+", "+this._lo+">"},divide:function(){if(arguments[0]instanceof B){var t,e,n,i,r=arguments[0],s=null,o=null,a=null,u=null;return t=(n=this._hi/r._hi)-(s=(a=B.SPLIT*n)-(s=a-n)),u=s*(o=(u=B.SPLIT*r._hi)-(o=u-r._hi))-(i=n*r._hi)+s*(e=r._hi-o)+t*o+t*e,new B(u=n+(a=(this._hi-i-u+this._lo-n*r._lo)/r._hi),n-u+a)}if("number"==typeof arguments[0]){var l=arguments[0];return S.isNaN(l)?B.createNaN():B.copy(this).selfDivide(l,0)}},ge:function(t){return this._hi>t._hi||this._hi===t._hi&&this._lo>=t._lo},pow:function(t){if(0===t)return B.valueOf(1);var e=new B(this),n=B.valueOf(1),i=Math.abs(t);if(1<i)for(;0<i;)i%2==1&&n.selfMultiply(e),0<(i/=2)&&(e=e.sqr());else n=e;return t<0?n.reciprocal():n},ceil:function(){if(this.isNaN())return B.NaN;var t=Math.ceil(this._hi),e=0;return t===this._hi&&(e=Math.ceil(this._lo)),new B(t,e)},compareTo:function(t){var e=t;return this._hi<e._hi?-1:this._hi>e._hi?1:this._lo<e._lo?-1:this._lo>e._lo?1:0},rint:function(){return this.isNaN()?this:this.add(.5).floor()},setValue:function(){if(arguments[0]instanceof B){var t=arguments[0];return this.init(t),this}if("number"==typeof arguments[0]){var e=arguments[0];return this.init(e),this}},max:function(t){return this.ge(t)?this:t},sqrt:function(){if(this.isZero())return B.valueOf(0);if(this.isNegative())return B.NaN;var t=1/Math.sqrt(this._hi),e=this._hi*t,n=B.valueOf(e),i=this.subtract(n.sqr())._hi*(.5*t);return n.add(i)},selfAdd:function(){if(1===arguments.length){if(arguments[0]instanceof B){var t=arguments[0];return this.selfAdd(t._hi,t._lo)}if("number"==typeof arguments[0]){var e=arguments[0],n=null,i=null,r=null,s=null,o=null,a=null;return s=(r=this._hi+e)-(o=r-this._hi),i=(a=(s=e-o+(this._hi-s))+this._lo)+(r-(n=r+a)),this._hi=n+i,this._lo=i+(n-this._hi),this}}else if(2===arguments.length){var u,l=arguments[0],h=arguments[1],c=(n=null,i=null,null);r=null,s=null,o=null,a=null;r=this._hi+l,u=this._lo+h,s=r-(o=r-this._hi),c=u-(a=u-this._lo);var f=(n=r+(o=(s=l-o+(this._hi-s))+u))+(o=(c=h-a+(this._lo-c))+(i=o+(r-n))),g=o+(n-f);return this._hi=f,this._lo=g,this}},selfMultiply:function(){if(1===arguments.length){if(arguments[0]instanceof B){var t=arguments[0];return this.selfMultiply(t._hi,t._lo)}if("number"==typeof arguments[0]){var e=arguments[0];return this.selfMultiply(e,0)}}else if(2===arguments.length){var n,i,r=arguments[0],s=arguments[1],o=null,a=null,u=null,l=null;o=(u=B.SPLIT*this._hi)-this._hi,l=B.SPLIT*r,o=u-o,n=this._hi-o,a=l-r;var h=(u=this._hi*r)+(l=o*(a=l-a)-u+o*(i=r-a)+n*a+n*i+(this._hi*s+this._lo*r)),c=l+(o=u-h);return this._hi=h,this._lo=c,this}},selfSqr:function(){return this.selfMultiply(this)},floor:function(){if(this.isNaN())return B.NaN;var t=Math.floor(this._hi),e=0;return t===this._hi&&(e=Math.floor(this._lo)),new B(t,e)},negate:function(){return this.isNaN()?this:new B(-this._hi,-this._lo)},clone:function(){try{return null}catch(t){if(t instanceof CloneNotSupportedException)return null;throw t}},multiply:function(){if(arguments[0]instanceof B){var t=arguments[0];return t.isNaN()?B.createNaN():B.copy(this).selfMultiply(t)}if("number"==typeof arguments[0]){var e=arguments[0];return S.isNaN(e)?B.createNaN():B.copy(this).selfMultiply(e,0)}},isNaN:function(){return S.isNaN(this._hi)},intValue:function(){return Math.trunc(this._hi)},toString:function(){var t=B.magnitude(this._hi);return-3<=t&&t<=20?this.toStandardNotation():this.toSciNotation()},toStandardNotation:function(){var t=this.getSpecialNumberString();if(null!==t)return t;var e=new Array(1).fill(null),n=this.extractSignificantDigits(!0,e),i=e[0]+1,r=n;if("."===n.charAt(0))r="0"+n;else if(i<0)r="0."+B.stringOfChar("0",-i)+n;else if(-1===n.indexOf(".")){var s=i-n.length;r=n+B.stringOfChar("0",s)+".0"}return this.isNegative()?"-"+r:r},reciprocal:function(){var t,e,n,i,r=null,s=null,o=null,a=null;t=(n=1/this._hi)-(r=(o=B.SPLIT*n)-(r=o-n)),s=(a=B.SPLIT*this._hi)-this._hi;var u=n+(o=(1-(i=n*this._hi)-(a=r*(s=a-s)-i+r*(e=this._hi-s)+t*s+t*e)-n*this._lo)/this._hi);return new B(u,n-u+o)},toSciNotation:function(){if(this.isZero())return B.SCI_NOT_ZERO;var t=this.getSpecialNumberString();if(null!==t)return t;var e=new Array(1).fill(null),n=this.extractSignificantDigits(!1,e),i=B.SCI_NOT_EXPONENT_CHAR+e[0];if("0"===n.charAt(0))throw new IllegalStateException("Found leading zero: "+n);var r="";1<n.length&&(r=n.substring(1));var s=n.charAt(0)+"."+r;return this.isNegative()?"-"+s+i:s+i},abs:function(){return this.isNaN()?B.NaN:this.isNegative()?this.negate():new B(this)},isPositive:function(){return 0<this._hi||0===this._hi&&0<this._lo},lt:function(t){return this._hi<t._hi||this._hi===t._hi&&this._lo<t._lo},add:function(){if(arguments[0]instanceof B){var t=arguments[0];return B.copy(this).selfAdd(t)}if("number"==typeof arguments[0]){var e=arguments[0];return B.copy(this).selfAdd(e)}},init:function(){if(1===arguments.length){if("number"==typeof arguments[0]){var t=arguments[0];this._hi=t,this._lo=0}else if(arguments[0]instanceof B){var e=arguments[0];this._hi=e._hi,this._lo=e._lo}}else if(2===arguments.length){var n=arguments[0],i=arguments[1];this._hi=n,this._lo=i}},gt:function(t){return this._hi>t._hi||this._hi===t._hi&&this._lo>t._lo},isNegative:function(){return this._hi<0||0===this._hi&&this._lo<0},trunc:function(){return this.isNaN()?B.NaN:this.isPositive()?this.floor():this.ceil()},signum:function(){return 0<this._hi?1:this._hi<0?-1:0<this._lo?1:this._lo<0?-1:0},interfaces_:function(){return[l,n,a]},getClass:function(){return B}}),B.sqr=function(t){return B.valueOf(t).selfMultiply(t)},B.valueOf=function(){if("string"==typeof arguments[0]){var t=arguments[0];return B.parse(t)}if("number"==typeof arguments[0])return new B(arguments[0])},B.sqrt=function(t){return B.valueOf(t).sqrt()},B.parse=function(e){for(var t=0,n=e.length;q.isWhitespace(e.charAt(t));)t++;var i=!1;if(t<n){var r=e.charAt(t);"-"!==r&&"+"!==r||(t++,"-"===r&&(i=!0))}for(var s=new B,o=0,a=0,u=0;!(n<=t);){var l=e.charAt(t);if(t++,q.isDigit(l)){var h=l-"0";s.selfMultiply(B.TEN),s.selfAdd(h),o++}else{if("."!==l){if("e"===l||"E"===l){var c=e.substring(t);try{u=G.parseInt(c)}catch(t){throw t instanceof NumberFormatException?new NumberFormatException("Invalid exponent "+c+" in string "+e):t}break}throw new NumberFormatException("Unexpected character '"+l+"' at position "+t+" in string "+e)}a=o}}var f=s,g=o-a-u;if(0===g)f=s;else if(0<g){var d=B.TEN.pow(g);f=s.divide(d)}else if(g<0){d=B.TEN.pow(-g);f=s.multiply(d)}return i?f.negate():f},B.createNaN=function(){return new B(S.NaN,S.NaN)},B.copy=function(t){return new B(t)},B.magnitude=function(t){var e=Math.abs(t),n=Math.log(e)/Math.log(10),i=Math.trunc(Math.floor(n));return 10*Math.pow(10,i)<=e&&(i+=1),i},B.stringOfChar=function(t,e){for(var n=new F,i=0;i<e;i++)n.append(t);return n.toString()},B.PI=new B(3.141592653589793,12246467991473532e-32),B.TWO_PI=new B(6.283185307179586,24492935982947064e-32),B.PI_2=new B(1.5707963267948966,6123233995736766e-32),B.E=new B(2.718281828459045,14456468917292502e-32),B.NaN=new B(S.NaN,S.NaN),B.EPS=123259516440783e-46,B.SPLIT=134217729,B.MAX_PRINT_DIGITS=32,B.TEN=B.valueOf(10),B.ONE=B.valueOf(1),B.SCI_NOT_EXPONENT_CHAR="E",B.SCI_NOT_ZERO="0.0E0",e(z.prototype,{interfaces_:function(){return[]},getClass:function(){return z}}),z.orientationIndex=function(t,e,n){var i=z.orientationIndexFilter(t,e,n);if(i<=1)return i;var r=B.valueOf(e.x).selfAdd(-t.x),s=B.valueOf(e.y).selfAdd(-t.y),o=B.valueOf(n.x).selfAdd(-e.x),a=B.valueOf(n.y).selfAdd(-e.y);return r.selfMultiply(a).selfSubtract(s.selfMultiply(o)).signum()},z.signOfDet2x2=function(){if(arguments[0]instanceof B){var t=arguments[0],e=arguments[1],n=arguments[2],i=arguments[3];return t.multiply(i).selfSubtract(e.multiply(n)).signum()}if("number"==typeof arguments[0]){var r=arguments[0],s=arguments[1],o=arguments[2],a=arguments[3],u=B.valueOf(r),l=B.valueOf(s),h=B.valueOf(o),c=B.valueOf(a);return u.multiply(c).selfSubtract(l.multiply(h)).signum()}},z.intersection=function(t,e,n,i){var r=B.valueOf(i.y).selfSubtract(n.y).selfMultiply(B.valueOf(e.x).selfSubtract(t.x)),s=B.valueOf(i.x).selfSubtract(n.x).selfMultiply(B.valueOf(e.y).selfSubtract(t.y)),o=r.subtract(s),a=B.valueOf(i.x).selfSubtract(n.x).selfMultiply(B.valueOf(t.y).selfSubtract(n.y)),u=B.valueOf(i.y).selfSubtract(n.y).selfMultiply(B.valueOf(t.x).selfSubtract(n.x)),l=a.subtract(u).selfDivide(o).doubleValue(),h=B.valueOf(t.x).selfAdd(B.valueOf(e.x).selfSubtract(t.x).selfMultiply(l)).doubleValue(),c=B.valueOf(e.x).selfSubtract(t.x).selfMultiply(B.valueOf(t.y).selfSubtract(n.y)),f=B.valueOf(e.y).selfSubtract(t.y).selfMultiply(B.valueOf(t.x).selfSubtract(n.x)),g=c.subtract(f).selfDivide(o).doubleValue();return new x(h,B.valueOf(n.y).selfAdd(B.valueOf(i.y).selfSubtract(n.y).selfMultiply(g)).doubleValue())},z.orientationIndexFilter=function(t,e,n){var i=null,r=(t.x-n.x)*(e.y-n.y),s=(t.y-n.y)*(e.x-n.x),o=r-s;if(0<r){if(s<=0)return z.signum(o);i=r+s}else{if(!(r<0))return z.signum(o);if(0<=s)return z.signum(o);i=-r-s}var a=z.DP_SAFE_EPSILON*i;return a<=o||a<=-o?z.signum(o):2},z.signum=function(t){return 0<t?1:t<0?-1:0},z.DP_SAFE_EPSILON=1e-15,e(V.prototype,{interfaces_:function(){return[]},getClass:function(){return V}}),V.index=function(t,e,n){return z.orientationIndex(t,e,n)},V.isCCW=function(t){var e=t.length-1;if(e<3)throw new c("Ring has fewer than 4 points, so orientation cannot be determined");for(var n=t[0],i=0,r=1;r<=e;r++){var s=t[r];s.y>n.y&&(n=s,i=r)}for(var o=i;(o-=1)<0&&(o=e),t[o].equals2D(n)&&o!==i;);for(var a=i;t[a=(a+1)%e].equals2D(n)&&a!==i;);var u=t[o],l=t[a];if(u.equals2D(n)||l.equals2D(n)||u.equals2D(l))return!1;var h=V.index(u,n,l);return 0===h?u.x>l.x:0<h},V.RIGHT=V.CLOCKWISE=-1,V.LEFT=V.COUNTERCLOCKWISE=1,V.STRAIGHT=V.COLLINEAR=0,Y.arraycopy=function(t,e,n,i,r){for(var s=0,o=e;o<e+r;o++)n[i+s]=t[o],s++},Y.getProperty=function(t){return{"line.separator":"\n"}[t]},e(k.prototype,{getY:function(){var t=this.y/this.w;if(S.isNaN(t)||S.isInfinite(t))throw new A;return t},getX:function(){var t=this.x/this.w;if(S.isNaN(t)||S.isInfinite(t))throw new A;return t},getCoordinate:function(){var t=new x;return t.x=this.getX(),t.y=this.getY(),t},interfaces_:function(){return[]},getClass:function(){return k}}),k.intersection=function(t,e,n,i){var r=t.y-e.y,s=e.x-t.x,o=t.x*e.y-e.x*t.y,a=n.y-i.y,u=i.x-n.x,l=n.x*i.y-i.x*n.y,h=r*u-a*s,c=(s*l-u*o)/h,f=(a*o-r*l)/h;if(S.isNaN(c)||S.isInfinite(c)||S.isNaN(f)||S.isInfinite(f))throw new A;return new x(c,f)},e(U.prototype,{interfaces_:function(){return[]},getClass:function(){return U}}),U.log10=function(t){var e=Math.log(t);return S.isInfinite(e)?e:S.isNaN(e)?e:e/U.LOG_10},U.min=function(t,e,n,i){var r=t;return e<r&&(r=e),n<r&&(r=n),i<r&&(r=i),r},U.clamp=function(){if("number"==typeof arguments[2]&&"number"==typeof arguments[0]&&"number"==typeof arguments[1]){var t=arguments[0],e=arguments[1],n=arguments[2];return t<e?e:n<t?n:t}if(Number.isInteger(arguments[2])&&Number.isInteger(arguments[0])&&Number.isInteger(arguments[1])){var i=arguments[0],r=arguments[1],s=arguments[2];return i<r?r:s<i?s:i}},U.wrap=function(t,e){return t<0?e- -t%e:t%e},U.max=function(){if(3===arguments.length){var t=arguments[0],e=arguments[1],n=arguments[2];return(i=t)<e&&(i=e),i<n&&(i=n),i}if(4===arguments.length){var i,r=arguments[0],s=arguments[1],o=arguments[2],a=arguments[3];return(i=r)<s&&(i=s),i<o&&(i=o),i<a&&(i=a),i}},U.average=function(t,e){return(t+e)/2},U.LOG_10=Math.log(10),e(X.prototype,{interfaces_:function(){return[]},getClass:function(){return X}}),X.segmentToSegment=function(t,e,n,i){if(t.equals(e))return X.pointToSegment(t,n,i);if(n.equals(i))return X.pointToSegment(i,t,e);var r=!1;if(M.intersects(t,e,n,i)){var s=(e.x-t.x)*(i.y-n.y)-(e.y-t.y)*(i.x-n.x);if(0===s)r=!0;else{var o=(t.y-n.y)*(i.x-n.x)-(t.x-n.x)*(i.y-n.y),a=((t.y-n.y)*(e.x-t.x)-(t.x-n.x)*(e.y-t.y))/s,u=o/s;(u<0||1<u||a<0||1<a)&&(r=!0)}}else r=!0;return r?U.min(X.pointToSegment(t,n,i),X.pointToSegment(e,n,i),X.pointToSegment(n,t,e),X.pointToSegment(i,t,e)):0},X.pointToSegment=function(t,e,n){if(e.x===n.x&&e.y===n.y)return t.distance(e);var i=(n.x-e.x)*(n.x-e.x)+(n.y-e.y)*(n.y-e.y),r=((t.x-e.x)*(n.x-e.x)+(t.y-e.y)*(n.y-e.y))/i;if(r<=0)return t.distance(e);if(1<=r)return t.distance(n);var s=((e.y-t.y)*(n.x-e.x)-(e.x-t.x)*(n.y-e.y))/i;return Math.abs(s)*Math.sqrt(i)},X.pointToLinePerpendicular=function(t,e,n){var i=(n.x-e.x)*(n.x-e.x)+(n.y-e.y)*(n.y-e.y),r=((e.y-t.y)*(n.x-e.x)-(e.x-t.x)*(n.y-e.y))/i;return Math.abs(r)*Math.sqrt(i)},X.pointToSegmentString=function(t,e){if(0===e.length)throw new c("Line array must contain at least one vertex");for(var n=t.distance(e[0]),i=0;i<e.length-1;i++){var r=X.pointToSegment(t,e[i],e[i+1]);r<n&&(n=r)}return n},e(H.prototype,{setOrdinate:function(t,e,n){},size:function(){},getOrdinate:function(t,e){},getCoordinate:function(){},getCoordinateCopy:function(t){},getDimension:function(){},getX:function(t){},expandEnvelope:function(t){},copy:function(){},getY:function(t){},toCoordinateArray:function(){},interfaces_:function(){return[a]},getClass:function(){return H}}),H.X=0,H.Y=1,H.Z=2,H.M=3,e(W.prototype,{create:function(){1===arguments.length&&(arguments[0]instanceof Array||L(arguments[0],H))},interfaces_:function(){return[]},getClass:function(){return W}}),e(j.prototype,{filter:function(t){},interfaces_:function(){return[]},getClass:function(){return j}}),e(K.prototype,{isGeometryCollection:function(){return this.getTypeCode()===K.TYPECODE_GEOMETRYCOLLECTION},getFactory:function(){return this._factory},getGeometryN:function(t){return this},getArea:function(){return 0},isRectangle:function(){return!1},equals:function(){if(arguments[0]instanceof K){var t=arguments[0];return null!==t&&this.equalsTopo(t)}if(arguments[0]instanceof Object){var e=arguments[0];if(!(e instanceof K))return!1;var n=e;return this.equalsExact(n)}},equalsExact:function(t){return this===t||this.equalsExact(t,0)},geometryChanged:function(){this.apply(K.geometryChangedFilter)},geometryChangedAction:function(){this._envelope=null},equalsNorm:function(t){return null!==t&&this.norm().equalsExact(t.norm())},getLength:function(){return 0},getNumGeometries:function(){return 1},compareTo:function(){if(1===arguments.length){var t=arguments[0],e=t;return this.getTypeCode()!==e.getTypeCode()?this.getTypeCode()-e.getTypeCode():this.isEmpty()&&e.isEmpty()?0:this.isEmpty()?-1:e.isEmpty()?1:this.compareToSameClass(t)}if(2===arguments.length){var n=arguments[0],i=arguments[1];e=n;return this.getTypeCode()!==e.getTypeCode()?this.getTypeCode()-e.getTypeCode():this.isEmpty()&&e.isEmpty()?0:this.isEmpty()?-1:e.isEmpty()?1:this.compareToSameClass(n,i)}},getUserData:function(){return this._userData},getSRID:function(){return this._SRID},getEnvelope:function(){return this.getFactory().toGeometry(this.getEnvelopeInternal())},checkNotGeometryCollection:function(t){if(t.getTypeCode()===K.TYPECODE_GEOMETRYCOLLECTION)throw new c("This method does not support GeometryCollection arguments")},equal:function(t,e,n){return 0===n?t.equals(e):t.distance(e)<=n},norm:function(){var t=this.copy();return t.normalize(),t},getPrecisionModel:function(){return this._factory.getPrecisionModel()},getEnvelopeInternal:function(){return null===this._envelope&&(this._envelope=this.computeEnvelopeInternal()),new M(this._envelope)},setSRID:function(t){this._SRID=t},setUserData:function(t){this._userData=t},compare:function(t,e){for(var n=t.iterator(),i=e.iterator();n.hasNext()&&i.hasNext();){var r=n.next(),s=i.next(),o=r.compareTo(s);if(0!==o)return o}return n.hasNext()?1:i.hasNext()?-1:0},hashCode:function(){return this.getEnvelopeInternal().hashCode()},isGeometryCollectionOrDerived:function(){return this.getTypeCode()===K.TYPECODE_GEOMETRYCOLLECTION||this.getTypeCode()===K.TYPECODE_MULTIPOINT||this.getTypeCode()===K.TYPECODE_MULTILINESTRING||this.getTypeCode()===K.TYPECODE_MULTIPOLYGON},interfaces_:function(){return[a,n,l]},getClass:function(){return K}}),K.hasNonEmptyElements=function(t){for(var e=0;e<t.length;e++)if(!t[e].isEmpty())return!0;return!1},K.hasNullElements=function(t){for(var e=0;e<t.length;e++)if(null===t[e])return!0;return!1},K.serialVersionUID=0x799ea46522854c00,K.TYPECODE_POINT=0,K.TYPECODE_MULTIPOINT=1,K.TYPECODE_LINESTRING=2,K.TYPECODE_LINEARRING=3,K.TYPECODE_MULTILINESTRING=4,K.TYPECODE_POLYGON=5,K.TYPECODE_MULTIPOLYGON=6,K.TYPECODE_GEOMETRYCOLLECTION=7,K.TYPENAME_POINT="Point",K.TYPENAME_MULTIPOINT="MultiPoint",K.TYPENAME_LINESTRING="LineString",K.TYPENAME_LINEARRING="LinearRing",K.TYPENAME_MULTILINESTRING="MultiLineString",K.TYPENAME_POLYGON="Polygon",K.TYPENAME_MULTIPOLYGON="MultiPolygon",K.TYPENAME_GEOMETRYCOLLECTION="GeometryCollection",K.geometryChangedFilter={interfaces_:function(){return[j]},filter:function(t){t.geometryChangedAction()}},e(Z.prototype,{filter:function(t){},interfaces_:function(){return[]},getClass:function(){return Z}}),e(Q.prototype,{isInBoundary:function(t){},interfaces_:function(){return[]},getClass:function(){return Q}}),e(J.prototype,{isInBoundary:function(t){return t%2==1},interfaces_:function(){return[Q]},getClass:function(){return J}}),e($.prototype,{isInBoundary:function(t){return 0<t},interfaces_:function(){return[Q]},getClass:function(){return $}}),e(tt.prototype,{isInBoundary:function(t){return 1<t},interfaces_:function(){return[Q]},getClass:function(){return tt}}),e(et.prototype,{isInBoundary:function(t){return 1===t},interfaces_:function(){return[Q]},getClass:function(){return et}}),Q.Mod2BoundaryNodeRule=J,Q.EndPointBoundaryNodeRule=$,Q.MultiValentEndPointBoundaryNodeRule=tt,Q.MonoValentEndPointBoundaryNodeRule=et,Q.MOD2_BOUNDARY_RULE=new J,Q.ENDPOINT_BOUNDARY_RULE=new $,Q.MULTIVALENT_ENDPOINT_BOUNDARY_RULE=new tt,Q.MONOVALENT_ENDPOINT_BOUNDARY_RULE=new et,Q.OGC_SFS_BOUNDARY_RULE=Q.MOD2_BOUNDARY_RULE,e(nt.prototype,{interfaces_:function(){return[]},getClass:function(){return nt}}),nt.isRing=function(t){return!(t.length<4)&&!!t[0].equals2D(t[t.length-1])},nt.ptNotInList=function(t,e){for(var n=0;n<t.length;n++){var i=t[n];if(nt.indexOf(i,e)<0)return i}return null},nt.scroll=function(t,e){var n=nt.indexOf(e,t);if(n<0)return null;var i=new Array(t.length).fill(null);Y.arraycopy(t,n,i,0,t.length-n),Y.arraycopy(t,0,i,t.length-n,n),Y.arraycopy(i,0,t,0,t.length)},nt.equals=function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];if(t===e)return!0;if(null===t||null===e)return!1;if(t.length!==e.length)return!1;for(var n=0;n<t.length;n++)if(!t[n].equals(e[n]))return!1;return!0}if(3===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2];if(i===r)return!0;if(null===i||null===r)return!1;if(i.length!==r.length)return!1;for(n=0;n<i.length;n++)if(0!==s.compare(i[n],r[n]))return!1;return!0}},nt.intersection=function(t,e){for(var n=new b,i=0;i<t.length;i++)e.intersects(t[i])&&n.add(t[i],!0);return n.toCoordinateArray()},nt.hasRepeatedPoints=function(t){for(var e=1;e<t.length;e++)if(t[e-1].equals(t[e]))return!0;return!1},nt.removeRepeatedPoints=function(t){return nt.hasRepeatedPoints(t)?new b(t,!1).toCoordinateArray():t},nt.reverse=function(t){for(var e=t.length-1,n=Math.trunc(e/2),i=0;i<=n;i++){var r=t[i];t[i]=t[e-i],t[e-i]=r}},nt.removeNull=function(t){for(var e=0,n=0;n<t.length;n++)null!==t[n]&&e++;var i=new Array(e).fill(null);if(0===e)return i;var r=0;for(n=0;n<t.length;n++)null!==t[n]&&(i[r++]=t[n]);return i},nt.copyDeep=function(){if(1===arguments.length){for(var t=arguments[0],e=new Array(t.length).fill(null),n=0;n<t.length;n++)e[n]=new x(t[n]);return e}if(5===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2],o=arguments[3],a=arguments[4];for(n=0;n<a;n++)s[o+n]=new x(i[r+n])}},nt.isEqualReversed=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[t.length-n-1];if(0!==i.compareTo(r))return!1}return!0},nt.envelope=function(t){for(var e=new M,n=0;n<t.length;n++)e.expandToInclude(t[n]);return e},nt.toCoordinateArray=function(t){return t.toArray(nt.coordArrayType)},nt.atLeastNCoordinatesOrNothing=function(t,e){return e.length>=t?e:[]},nt.indexOf=function(t,e){for(var n=0;n<e.length;n++)if(t.equals(e[n]))return n;return-1},nt.increasingDirection=function(t){for(var e=0;e<Math.trunc(t.length/2);e++){var n=t.length-1-e,i=t[e].compareTo(t[n]);if(0!==i)return i}return 1},nt.compare=function(t,e){for(var n=0;n<t.length&&n<e.length;){var i=t[n].compareTo(e[n]);if(0!==i)return i;n++}return n<e.length?-1:n<t.length?1:0},nt.minCoordinate=function(t){for(var e=null,n=0;n<t.length;n++)(null===e||0<e.compareTo(t[n]))&&(e=t[n]);return e},nt.extract=function(t,e,n){e=U.clamp(e,0,t.length);var i=(n=U.clamp(n,-1,t.length))-e+1;n<0&&(i=0),e>=t.length&&(i=0),n<e&&(i=0);var r=new Array(i).fill(null);if(0===i)return r;for(var s=0,o=e;o<=n;o++)r[s++]=t[o];return r},e(it.prototype,{compare:function(t,e){return nt.compare(t,e)},interfaces_:function(){return[u]},getClass:function(){return it}}),e(rt.prototype,{compare:function(t,e){var n=t,i=e;if(n.length<i.length)return-1;if(n.length>i.length)return 1;if(0===n.length)return 0;var r=nt.compare(n,i);return nt.isEqualReversed(n,i)?0:r},OLDcompare:function(t,e){var n=t,i=e;if(n.length<i.length)return-1;if(n.length>i.length)return 1;if(0===n.length)return 0;for(var r=nt.increasingDirection(n),s=nt.increasingDirection(i),o=0<r?0:n.length-1,a=0<s?0:n.length-1,u=0;u<n.length;u++){var l=n[o].compareTo(i[a]);if(0!==l)return l;o+=r,a+=s}return 0},interfaces_:function(){return[u]},getClass:function(){return rt}}),nt.ForwardComparator=it,nt.BidirectionalComparator=rt,nt.coordArrayType=new Array(0).fill(null),st.prototype.get=function(){},st.prototype.put=function(){},st.prototype.size=function(){},st.prototype.values=function(){},st.prototype.entrySet=function(){},ot.prototype=new st,(at.prototype=new N).contains=function(){},(ut.prototype=new at).contains=function(t){for(var e=0,n=this.array_.length;e<n;e++){if(this.array_[e]===t)return!0}return!1},ut.prototype.add=function(t){return!this.contains(t)&&(this.array_.push(t),!0)},ut.prototype.addAll=function(t){for(var e=t.iterator();e.hasNext();)this.add(e.next());return!0},ut.prototype.remove=function(t){throw new javascript.util.OperationNotSupported},ut.prototype.size=function(){return this.array_.length},ut.prototype.isEmpty=function(){return 0===this.array_.length},ut.prototype.toArray=function(){for(var t=[],e=0,n=this.array_.length;e<n;e++)t.push(this.array_[e]);return t},ut.prototype.iterator=function(){return new lt(this)};var lt=function(t){this.hashSet_=t,this.position_=0};lt.prototype.next=function(){if(this.position_===this.hashSet_.size())throw new R;return this.hashSet_.array_[this.position_++]},lt.prototype.hasNext=function(){return this.position_<this.hashSet_.size()},lt.prototype.remove=function(){throw new T};var ht=0;function ct(t){return null==t?ht:t.color}function ft(t){return null==t?null:t.parent}function gt(t,e){null!==t&&(t.color=e)}function dt(t){return null==t?null:t.left}function _t(t){return null==t?null:t.right}function pt(){this.root_=null,this.size_=0}function mt(){}function vt(){}function yt(){this.array_=[],arguments[0]instanceof N&&this.addAll(arguments[0])}(pt.prototype=new ot).get=function(t){for(var e=this.root_;null!==e;){var n=t.compareTo(e.key);if(n<0)e=e.left;else{if(!(0<n))return e.value;e=e.right}}return null},pt.prototype.put=function(t,e){if(null===this.root_)return this.root_={key:t,value:e,left:null,right:null,parent:null,color:ht,getValue:function(){return this.value},getKey:function(){return this.key}},this.size_=1,null;var n,i,r=this.root_;do{if(n=r,(i=t.compareTo(r.key))<0)r=r.left;else{if(!(0<i)){var s=r.value;return r.value=e,s}r=r.right}}while(null!==r);var o={key:t,left:null,right:null,value:e,parent:n,color:ht,getValue:function(){return this.value},getKey:function(){return this.key}};return i<0?n.left=o:n.right=o,this.fixAfterInsertion(o),this.size_++,null},pt.prototype.fixAfterInsertion=function(t){for(t.color=1;null!=t&&t!=this.root_&&1==t.parent.color;){var e;if(ft(t)==dt(ft(ft(t))))1==ct(e=_t(ft(ft(t))))?(gt(ft(t),ht),gt(e,ht),gt(ft(ft(t)),1),t=ft(ft(t))):(t==_t(ft(t))&&(t=ft(t),this.rotateLeft(t)),gt(ft(t),ht),gt(ft(ft(t)),1),this.rotateRight(ft(ft(t))));else 1==ct(e=dt(ft(ft(t))))?(gt(ft(t),ht),gt(e,ht),gt(ft(ft(t)),1),t=ft(ft(t))):(t==dt(ft(t))&&(t=ft(t),this.rotateRight(t)),gt(ft(t),ht),gt(ft(ft(t)),1),this.rotateLeft(ft(ft(t))))}this.root_.color=ht},pt.prototype.values=function(){var t=new P,e=this.getFirstEntry();if(null!==e)for(t.add(e.value);null!==(e=pt.successor(e));)t.add(e.value);return t},pt.prototype.entrySet=function(){var t=new ut,e=this.getFirstEntry();if(null!==e)for(t.add(e);null!==(e=pt.successor(e));)t.add(e);return t},pt.prototype.rotateLeft=function(t){if(null!=t){var e=t.right;t.right=e.left,null!=e.left&&(e.left.parent=t),e.parent=t.parent,null==t.parent?this.root_=e:t.parent.left==t?t.parent.left=e:t.parent.right=e,(e.left=t).parent=e}},pt.prototype.rotateRight=function(t){if(null!=t){var e=t.left;t.left=e.right,null!=e.right&&(e.right.parent=t),e.parent=t.parent,null==t.parent?this.root_=e:t.parent.right==t?t.parent.right=e:t.parent.left=e,(e.right=t).parent=e}},pt.prototype.getFirstEntry=function(){var t=this.root_;if(null!=t)for(;null!=t.left;)t=t.left;return t},pt.successor=function(t){if(null===t)return null;if(null!==t.right){for(var e=t.right;null!==e.left;)e=e.left;return e}e=t.parent;for(var n=t;null!==e&&n===e.right;)e=(n=e).parent;return e},pt.prototype.size=function(){return this.size_},e(mt.prototype,{interfaces_:function(){return[]},getClass:function(){return mt}}),vt.prototype=new at,(yt.prototype=new vt).contains=function(t){for(var e=0,n=this.array_.length;e<n;e++){if(0===this.array_[e].compareTo(t))return!0}return!1},yt.prototype.add=function(t){if(this.contains(t))return!1;for(var e=0,n=this.array_.length;e<n;e++){if(1===this.array_[e].compareTo(t))return this.array_.splice(e,0,t),!0}return this.array_.push(t),!0},yt.prototype.addAll=function(t){for(var e=t.iterator();e.hasNext();)this.add(e.next());return!0},yt.prototype.remove=function(t){throw new T},yt.prototype.size=function(){return this.array_.length},yt.prototype.isEmpty=function(){return 0===this.array_.length},yt.prototype.toArray=function(){for(var t=[],e=0,n=this.array_.length;e<n;e++)t.push(this.array_[e]);return t},yt.prototype.iterator=function(){return new Et(this)};var xt,Et=function(t){this.treeSet_=t,this.position_=0};function It(){}function Nt(){}function Ct(){}function St(){}function Lt(){this._geometries=null;var t=arguments[0],e=arguments[1];if(K.call(this,e),null===t&&(t=[]),K.hasNullElements(t))throw new c("geometries must not contain null elements");this._geometries=t}function wt(){var t=arguments[0],e=arguments[1];Lt.call(this,t,e)}function Rt(){if(this._geom=null,this._geomFact=null,this._bnRule=null,this._endpointMap=null,1===arguments.length){var t=arguments[0];Rt.call(this,t,Q.MOD2_BOUNDARY_RULE)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this._geom=e,this._geomFact=e.getFactory(),this._bnRule=n}}function Tt(){this.count=null}function Pt(){}function Ot(){}function bt(){}function Mt(){}function Dt(){}function At(){}function Ft(){}function Gt(t){this.str=t}function qt(){}function Bt(){this._points=null;var t=arguments[0],e=arguments[1];K.call(this,e),this.init(t)}function zt(){}function Vt(){this._coordinates=null;var t=arguments[0],e=arguments[1];K.call(this,e),this.init(t)}function Yt(){}function kt(){}function Ut(){this._shell=null,this._holes=null;var t=arguments[0],e=arguments[1],n=arguments[2];if(K.call(this,n),null===t&&(t=this.getFactory().createLinearRing()),null===e&&(e=[]),K.hasNullElements(e))throw new c("holes must not contain null elements");if(t.isEmpty()&&K.hasNonEmptyElements(e))throw new c("shell is empty but holes are not");this._shell=t,this._holes=e}function Xt(){var t=arguments[0],e=arguments[1];Lt.call(this,t,e)}function Ht(){if(arguments[0]instanceof Array&&arguments[1]instanceof le){var t=arguments[0],e=arguments[1];Ht.call(this,e.getCoordinateSequenceFactory().create(t),e)}else if(L(arguments[0],H)&&arguments[1]instanceof le){var n=arguments[0],i=arguments[1];Bt.call(this,n,i),this.validateConstruction()}}function Wt(){var t=arguments[0],e=arguments[1];Lt.call(this,t,e)}function jt(){if(this._factory=null,this._isUserDataCopied=!1,0===arguments.length);else if(1===arguments.length){var t=arguments[0];this._factory=t}}function Kt(){}function Zt(){}function Qt(){}function Jt(){}function $t(){if(this._dimension=3,this._coordinates=null,1===arguments.length){if(arguments[0]instanceof Array){var t=arguments[0];$t.call(this,t,3)}else if(Number.isInteger(arguments[0])){var e=arguments[0];this._coordinates=new Array(e).fill(null);for(var n=0;n<e;n++)this._coordinates[n]=new x}else if(L(arguments[0],H)){var i=arguments[0];if(null===i)return this._coordinates=new Array(0).fill(null),null;this._dimension=i.getDimension(),this._coordinates=new Array(i.size()).fill(null);for(n=0;n<this._coordinates.length;n++)this._coordinates[n]=i.getCoordinateCopy(n)}}else if(2===arguments.length)if(arguments[0]instanceof Array&&Number.isInteger(arguments[1])){var r=arguments[0],s=arguments[1];this._coordinates=r,this._dimension=s,null===r&&(this._coordinates=new Array(0).fill(null))}else if(Number.isInteger(arguments[0])&&Number.isInteger(arguments[1])){var o=arguments[0],a=arguments[1];this._coordinates=new Array(o).fill(null),this._dimension=a;for(n=0;n<o;n++)this._coordinates[n]=new x}}function te(){}Et.prototype.next=function(){if(this.position_===this.treeSet_.size())throw new R;return this.treeSet_.array_[this.position_++]},Et.prototype.hasNext=function(){return this.position_<this.treeSet_.size()},Et.prototype.remove=function(){throw new T},It.sort=function(){var t,e,n,i,r=arguments[0];if(1===arguments.length)return i=function(t,e){return t.compareTo(e)},void r.sort(i);if(2===arguments.length)n=arguments[1],i=function(t,e){return n.compare(t,e)},r.sort(i);else{if(3===arguments.length){(e=r.slice(arguments[1],arguments[2])).sort();var s=r.slice(0,arguments[1]).concat(e,r.slice(arguments[2],r.length));for(r.splice(0,r.length),t=0;t<s.length;t++)r.push(s[t]);return}if(4===arguments.length){for(e=r.slice(arguments[1],arguments[2]),n=arguments[3],i=function(t,e){return n.compare(t,e)},e.sort(i),s=r.slice(0,arguments[1]).concat(e,r.slice(arguments[2],r.length)),r.splice(0,r.length),t=0;t<s.length;t++)r.push(s[t]);return}}},It.asList=function(t){for(var e=new P,n=0,i=t.length;n<i;n++)e.add(t[n]);return e},e(Nt.prototype,{interfaces_:function(){return[]},getClass:function(){return Nt}}),Nt.toDimensionSymbol=function(t){switch(t){case Nt.FALSE:return Nt.SYM_FALSE;case Nt.TRUE:return Nt.SYM_TRUE;case Nt.DONTCARE:return Nt.SYM_DONTCARE;case Nt.P:return Nt.SYM_P;case Nt.L:return Nt.SYM_L;case Nt.A:return Nt.SYM_A}throw new c("Unknown dimension value: "+t)},Nt.toDimensionValue=function(t){switch(q.toUpperCase(t)){case Nt.SYM_FALSE:return Nt.FALSE;case Nt.SYM_TRUE:return Nt.TRUE;case Nt.SYM_DONTCARE:return Nt.DONTCARE;case Nt.SYM_P:return Nt.P;case Nt.SYM_L:return Nt.L;case Nt.SYM_A:return Nt.A}throw new c("Unknown dimension symbol: "+t)},Nt.P=0,Nt.L=1,Nt.A=2,Nt.FALSE=-1,Nt.TRUE=-2,Nt.DONTCARE=-3,Nt.SYM_FALSE="F",Nt.SYM_TRUE="T",Nt.SYM_DONTCARE="*",Nt.SYM_P="0",Nt.SYM_L="1",Nt.SYM_A="2",e(Ct.prototype,{filter:function(t){},interfaces_:function(){return[]},getClass:function(){return Ct}}),e(St.prototype,{filter:function(t,e){},isDone:function(){},isGeometryChanged:function(){},interfaces_:function(){return[]},getClass:function(){return St}}),p(Lt,K),e(Lt.prototype,{computeEnvelopeInternal:function(){for(var t=new M,e=0;e<this._geometries.length;e++)t.expandToInclude(this._geometries[e].getEnvelopeInternal());return t},getGeometryN:function(t){return this._geometries[t]},getCoordinates:function(){for(var t=new Array(this.getNumPoints()).fill(null),e=-1,n=0;n<this._geometries.length;n++)for(var i=this._geometries[n].getCoordinates(),r=0;r<i.length;r++)t[++e]=i[r];return t},getArea:function(){for(var t=0,e=0;e<this._geometries.length;e++)t+=this._geometries[e].getArea();return t},equalsExact:function(){if(2===arguments.length&&"number"==typeof arguments[1]&&arguments[0]instanceof K){var t=arguments[0],e=arguments[1];if(!this.isEquivalentClass(t))return!1;var n=t;if(this._geometries.length!==n._geometries.length)return!1;for(var i=0;i<this._geometries.length;i++)if(!this._geometries[i].equalsExact(n._geometries[i],e))return!1;return!0}return K.prototype.equalsExact.apply(this,arguments)},normalize:function(){for(var t=0;t<this._geometries.length;t++)this._geometries[t].normalize();It.sort(this._geometries)},getCoordinate:function(){return this.isEmpty()?null:this._geometries[0].getCoordinate()},getBoundaryDimension:function(){for(var t=Nt.FALSE,e=0;e<this._geometries.length;e++)t=Math.max(t,this._geometries[e].getBoundaryDimension());return t},getTypeCode:function(){return K.TYPECODE_GEOMETRYCOLLECTION},getDimension:function(){for(var t=Nt.FALSE,e=0;e<this._geometries.length;e++)t=Math.max(t,this._geometries[e].getDimension());return t},getLength:function(){for(var t=0,e=0;e<this._geometries.length;e++)t+=this._geometries[e].getLength();return t},getNumPoints:function(){for(var t=0,e=0;e<this._geometries.length;e++)t+=this._geometries[e].getNumPoints();return t},getNumGeometries:function(){return this._geometries.length},reverse:function(){for(var t=this._geometries.length,e=new Array(t).fill(null),n=0;n<this._geometries.length;n++)e[n]=this._geometries[n].reverse();return this.getFactory().createGeometryCollection(e)},compareToSameClass:function(){if(1===arguments.length){var t=arguments[0],e=new yt(It.asList(this._geometries)),n=new yt(It.asList(t._geometries));return this.compare(e,n)}if(2===arguments.length){for(var i=arguments[0],r=arguments[1],s=i,o=this.getNumGeometries(),a=s.getNumGeometries(),u=0;u<o&&u<a;){var l=this.getGeometryN(u),h=s.getGeometryN(u),c=l.compareToSameClass(h,r);if(0!==c)return c;u++}return u<o?1:u<a?-1:0}},apply:function(){if(L(arguments[0],Z))for(var t=arguments[0],e=0;e<this._geometries.length;e++)this._geometries[e].apply(t);else if(L(arguments[0],St)){var n=arguments[0];if(0===this._geometries.length)return null;for(e=0;e<this._geometries.length&&(this._geometries[e].apply(n),!n.isDone());e++);n.isGeometryChanged()&&this.geometryChanged()}else if(L(arguments[0],Ct)){var i=arguments[0];i.filter(this);for(e=0;e<this._geometries.length;e++)this._geometries[e].apply(i)}else if(L(arguments[0],j)){var r=arguments[0];r.filter(this);for(e=0;e<this._geometries.length;e++)this._geometries[e].apply(r)}},getBoundary:function(){return this.checkNotGeometryCollection(this),y.shouldNeverReachHere(),null},getGeometryType:function(){return K.TYPENAME_GEOMETRYCOLLECTION},copy:function(){for(var t=new Array(this._geometries.length).fill(null),e=0;e<t.length;e++)t[e]=this._geometries[e].copy();return new Lt(t,this._factory)},isEmpty:function(){for(var t=0;t<this._geometries.length;t++)if(!this._geometries[t].isEmpty())return!1;return!0},interfaces_:function(){return[]},getClass:function(){return Lt}}),Lt.serialVersionUID=-0x4f07bcb1f857d800,p(wt,Lt),e(wt.prototype,{equalsExact:function(){if(2===arguments.length&&"number"==typeof arguments[1]&&arguments[0]instanceof K){var t=arguments[0],e=arguments[1];return!!this.isEquivalentClass(t)&&Lt.prototype.equalsExact.call(this,t,e)}return Lt.prototype.equalsExact.apply(this,arguments)},getBoundaryDimension:function(){return this.isClosed()?Nt.FALSE:0},isClosed:function(){if(this.isEmpty())return!1;for(var t=0;t<this._geometries.length;t++)if(!this._geometries[t].isClosed())return!1;return!0},getTypeCode:function(){return K.TYPECODE_MULTILINESTRING},getDimension:function(){return 1},reverse:function(){for(var t=this._geometries.length,e=new Array(t).fill(null),n=0;n<this._geometries.length;n++)e[t-1-n]=this._geometries[n].reverse();return this.getFactory().createMultiLineString(e)},getBoundary:function(){return new Rt(this).getBoundary()},getGeometryType:function(){return K.TYPENAME_MULTILINESTRING},copy:function(){for(var t=new Array(this._geometries.length).fill(null),e=0;e<t.length;e++)t[e]=this._geometries[e].copy();return new wt(t,this._factory)},interfaces_:function(){return[mt]},getClass:function(){return wt}}),wt.serialVersionUID=0x7155d2ab4afa8000,e(Rt.prototype,{boundaryMultiLineString:function(t){if(this._geom.isEmpty())return this.getEmptyMultiPoint();var e=this.computeBoundaryCoordinates(t);return 1===e.length?this._geomFact.createPoint(e[0]):this._geomFact.createMultiPointFromCoords(e)},getBoundary:function(){return this._geom instanceof Bt?this.boundaryLineString(this._geom):this._geom instanceof wt?this.boundaryMultiLineString(this._geom):this._geom.getBoundary()},boundaryLineString:function(t){return this._geom.isEmpty()?this.getEmptyMultiPoint():t.isClosed()?this._bnRule.isInBoundary(2)?t.getStartPoint():this._geomFact.createMultiPoint():this._geomFact.createMultiPoint([t.getStartPoint(),t.getEndPoint()])},getEmptyMultiPoint:function(){return this._geomFact.createMultiPoint()},computeBoundaryCoordinates:function(t){var e=new P;this._endpointMap=new pt;for(var n=0;n<t.getNumGeometries();n++){var i=t.getGeometryN(n);0!==i.getNumPoints()&&(this.addEndpoint(i.getCoordinateN(0)),this.addEndpoint(i.getCoordinateN(i.getNumPoints()-1)))}for(var r=this._endpointMap.entrySet().iterator();r.hasNext();){var s=r.next(),o=s.getValue().count;this._bnRule.isInBoundary(o)&&e.add(s.getKey())}return nt.toCoordinateArray(e)},addEndpoint:function(t){var e=this._endpointMap.get(t);null===e&&(e=new Tt,this._endpointMap.put(t,e)),e.count++},interfaces_:function(){return[]},getClass:function(){return Rt}}),Rt.getBoundary=function(){return 1===arguments.length?new Rt(arguments[0]).getBoundary():2===arguments.length?new Rt(arguments[0],arguments[1]).getBoundary():void 0},e(Tt.prototype,{interfaces_:function(){return[]},getClass:function(){return Tt}}),e(Pt.prototype,{interfaces_:function(){return[]},getClass:function(){return Pt}}),Pt.ofLine=function(t){var e=t.size();if(e<=1)return 0;var n=0,i=new x;t.getCoordinate(0,i);for(var r=i.x,s=i.y,o=1;o<e;o++){t.getCoordinate(o,i);var a=i.x,u=i.y,l=a-r,h=u-s;n+=Math.sqrt(l*l+h*h),r=a,s=u}return n},e(Ft.prototype,{interfaces_:function(){return[]},getClass:function(){return Ft}}),Ft.chars=function(t,e){for(var n=new Array(e).fill(null),i=0;i<e;i++)n[i]=t;return new String(n)},Ft.getStackTrace=function(){if(1===arguments.length){var t=arguments[0],e=new Mt,n=new Ot(e);return t.printStackTrace(n),e.toString()}if(2===arguments.length){for(var i=arguments[0],r=arguments[1],s="",o=new At(new bt(Ft.getStackTrace(i))),a=0;a<r;a++)try{s+=o.readLine()+Ft.NEWLINE}catch(t){if(!(t instanceof Dt))throw t;y.shouldNeverReachHere()}return s}},Ft.split=function(t,e){for(var n=e.length,i=new P,r=""+t,s=r.indexOf(e);0<=s;){var o=r.substring(0,s);i.add(o),s=(r=r.substring(s+n)).indexOf(e)}0<r.length&&i.add(r);for(var a=new Array(i.size()).fill(null),u=0;u<a.length;u++)a[u]=i.get(u);return a},Ft.toString=function(){if(1===arguments.length&&"number"==typeof arguments[0]){var t=arguments[0];return Ft.SIMPLE_ORDINATE_FORMAT.format(t)}},Ft.spaces=function(t){return Ft.chars(" ",t)},Ft.NEWLINE=Y.getProperty("line.separator"),Ft.SIMPLE_ORDINATE_FORMAT=new function(){}("0.#"),Gt.prototype.append=function(t){this.str+=t},Gt.prototype.setCharAt=function(t,e){this.str=this.str.substr(0,t)+e+this.str.substr(t+1)},Gt.prototype.toString=function(t){return this.str},e(qt.prototype,{interfaces_:function(){return[]},getClass:function(){return qt}}),qt.copyCoord=function(t,e,n,i){for(var r=Math.min(t.getDimension(),n.getDimension()),s=0;s<r;s++)n.setOrdinate(i,s,t.getOrdinate(e,s))},qt.isRing=function(t){var e=t.size();return 0===e||!(e<=3)&&(t.getOrdinate(0,H.X)===t.getOrdinate(e-1,H.X)&&t.getOrdinate(0,H.Y)===t.getOrdinate(e-1,H.Y))},qt.isEqual=function(t,e){var n=t.size();if(n!==e.size())return!1;for(var i=Math.min(t.getDimension(),e.getDimension()),r=0;r<n;r++)for(var s=0;s<i;s++){var o=t.getOrdinate(r,s),a=e.getOrdinate(r,s);if(t.getOrdinate(r,s)!==e.getOrdinate(r,s)&&(!S.isNaN(o)||!S.isNaN(a)))return!1}return!0},qt.extend=function(t,e,n){var i=t.create(n,e.getDimension()),r=e.size();if(qt.copy(e,0,i,0,r),0<r)for(var s=r;s<n;s++)qt.copy(e,r-1,i,s,1);return i},qt.reverse=function(t){for(var e=t.size()-1,n=Math.trunc(e/2),i=0;i<=n;i++)qt.swap(t,i,e-i)},qt.swap=function(t,e,n){if(e===n)return null;for(var i=0;i<t.getDimension();i++){var r=t.getOrdinate(e,i);t.setOrdinate(e,i,t.getOrdinate(n,i)),t.setOrdinate(n,i,r)}},qt.copy=function(t,e,n,i,r){for(var s=0;s<r;s++)qt.copyCoord(t,e+s,n,i+s)},qt.toString=function(){if(1===arguments.length&&L(arguments[0],H)){var t=arguments[0],e=t.size();if(0===e)return"()";var n=t.getDimension(),i=new Gt;i.append("(");for(var r=0;r<e;r++){0<r&&i.append(" ");for(var s=0;s<n;s++)0<s&&i.append(","),i.append(Ft.toString(t.getOrdinate(r,s)))}return i.append(")"),i.toString()}},qt.ensureValidRing=function(t,e){var n=e.size();return 0===n?e:n<=3?qt.createClosedRing(t,e,4):e.getOrdinate(0,H.X)===e.getOrdinate(n-1,H.X)&&e.getOrdinate(0,H.Y)===e.getOrdinate(n-1,H.Y)?e:qt.createClosedRing(t,e,n+1)},qt.createClosedRing=function(t,e,n){var i=t.create(n,e.getDimension()),r=e.size();qt.copy(e,0,i,0,r);for(var s=r;s<n;s++)qt.copy(e,0,i,s,1);return i},p(Bt,K),e(Bt.prototype,{computeEnvelopeInternal:function(){return this.isEmpty()?new M:this._points.expandEnvelope(new M)},isRing:function(){return this.isClosed()&&this.isSimple()},getCoordinates:function(){return this._points.toCoordinateArray()},equalsExact:function(){if(2===arguments.length&&"number"==typeof arguments[1]&&arguments[0]instanceof K){var t=arguments[0],e=arguments[1];if(!this.isEquivalentClass(t))return!1;var n=t;if(this._points.size()!==n._points.size())return!1;for(var i=0;i<this._points.size();i++)if(!this.equal(this._points.getCoordinate(i),n._points.getCoordinate(i),e))return!1;return!0}return K.prototype.equalsExact.apply(this,arguments)},normalize:function(){for(var t=0;t<Math.trunc(this._points.size()/2);t++){var e=this._points.size()-1-t;if(!this._points.getCoordinate(t).equals(this._points.getCoordinate(e))){if(0<this._points.getCoordinate(t).compareTo(this._points.getCoordinate(e))){var n=this._points.copy();qt.reverse(n),this._points=n}return null}}},getCoordinate:function(){return this.isEmpty()?null:this._points.getCoordinate(0)},getBoundaryDimension:function(){return this.isClosed()?Nt.FALSE:0},isClosed:function(){return!this.isEmpty()&&this.getCoordinateN(0).equals2D(this.getCoordinateN(this.getNumPoints()-1))},getEndPoint:function(){return this.isEmpty()?null:this.getPointN(this.getNumPoints()-1)},getTypeCode:function(){return K.TYPECODE_LINESTRING},getDimension:function(){return 1},getLength:function(){return Pt.ofLine(this._points)},getNumPoints:function(){return this._points.size()},reverse:function(){var t=this._points.copy();return qt.reverse(t),this.getFactory().createLineString(t)},compareToSameClass:function(){if(1===arguments.length){for(var t=arguments[0],e=0,n=0;e<this._points.size()&&n<t._points.size();){var i=this._points.getCoordinate(e).compareTo(t._points.getCoordinate(n));if(0!==i)return i;e++,n++}return e<this._points.size()?1:n<t._points.size()?-1:0}if(2===arguments.length){t=arguments[0];return arguments[1].compare(this._points,t._points)}},apply:function(){if(L(arguments[0],Z))for(var t=arguments[0],e=0;e<this._points.size();e++)t.filter(this._points.getCoordinate(e));else if(L(arguments[0],St)){var n=arguments[0];if(0===this._points.size())return null;for(e=0;e<this._points.size()&&(n.filter(this._points,e),!n.isDone());e++);n.isGeometryChanged()&&this.geometryChanged()}else if(L(arguments[0],Ct)){arguments[0].filter(this)}else if(L(arguments[0],j)){arguments[0].filter(this)}},getBoundary:function(){return new Rt(this).getBoundary()},isEquivalentClass:function(t){return t instanceof Bt},getCoordinateN:function(t){return this._points.getCoordinate(t)},getGeometryType:function(){return K.TYPENAME_LINESTRING},copy:function(){return new Bt(this._points.copy(),this._factory)},getCoordinateSequence:function(){return this._points},isEmpty:function(){return 0===this._points.size()},init:function(t){if(null===t&&(t=this.getFactory().getCoordinateSequenceFactory().create([])),1===t.size())throw new c("Invalid number of points in LineString (found "+t.size()+" - must be 0 or >= 2)");this._points=t},isCoordinate:function(t){for(var e=0;e<this._points.size();e++)if(this._points.getCoordinate(e).equals(t))return!0;return!1},getStartPoint:function(){return this.isEmpty()?null:this.getPointN(0)},getPointN:function(t){return this.getFactory().createPoint(this._points.getCoordinate(t))},interfaces_:function(){return[mt]},getClass:function(){return Bt}}),Bt.serialVersionUID=0x2b2b51ba435c8e00,e(zt.prototype,{interfaces_:function(){return[]},getClass:function(){return zt}}),p(Vt,K),e(Vt.prototype,{computeEnvelopeInternal:function(){if(this.isEmpty())return new M;var t=new M;return t.expandToInclude(this._coordinates.getX(0),this._coordinates.getY(0)),t},getCoordinates:function(){return this.isEmpty()?[]:[this.getCoordinate()]},equalsExact:function(){if(2===arguments.length&&"number"==typeof arguments[1]&&arguments[0]instanceof K){var t=arguments[0],e=arguments[1];return!!this.isEquivalentClass(t)&&(!(!this.isEmpty()||!t.isEmpty())||this.isEmpty()===t.isEmpty()&&this.equal(t.getCoordinate(),this.getCoordinate(),e))}return K.prototype.equalsExact.apply(this,arguments)},normalize:function(){},getCoordinate:function(){return 0!==this._coordinates.size()?this._coordinates.getCoordinate(0):null},getBoundaryDimension:function(){return Nt.FALSE},getTypeCode:function(){return K.TYPECODE_POINT},getDimension:function(){return 0},getNumPoints:function(){return this.isEmpty()?0:1},reverse:function(){return this.copy()},getX:function(){if(null===this.getCoordinate())throw new IllegalStateException("getX called on empty Point");return this.getCoordinate().x},compareToSameClass:function(){if(1===arguments.length){var t=arguments[0];return this.getCoordinate().compareTo(t.getCoordinate())}if(2===arguments.length){t=arguments[0];return arguments[1].compare(this._coordinates,t._coordinates)}},apply:function(){if(L(arguments[0],Z)){var t=arguments[0];if(this.isEmpty())return null;t.filter(this.getCoordinate())}else if(L(arguments[0],St)){var e=arguments[0];if(this.isEmpty())return null;e.filter(this._coordinates,0),e.isGeometryChanged()&&this.geometryChanged()}else if(L(arguments[0],Ct)){arguments[0].filter(this)}else if(L(arguments[0],j)){arguments[0].filter(this)}},getBoundary:function(){return this.getFactory().createGeometryCollection()},getGeometryType:function(){return K.TYPENAME_POINT},copy:function(){return new Vt(this._coordinates.copy(),this._factory)},getCoordinateSequence:function(){return this._coordinates},getY:function(){if(null===this.getCoordinate())throw new IllegalStateException("getY called on empty Point");return this.getCoordinate().y},isEmpty:function(){return 0===this._coordinates.size()},init:function(t){null===t&&(t=this.getFactory().getCoordinateSequenceFactory().create([])),y.isTrue(t.size()<=1),this._coordinates=t},isSimple:function(){return!0},interfaces_:function(){return[zt]},getClass:function(){return Vt}}),Vt.serialVersionUID=0x44077bad161cbc00,e(Yt.prototype,{interfaces_:function(){return[]},getClass:function(){return Yt}}),Yt.ofRing=function(){if(arguments[0]instanceof Array){var t=arguments[0];return Math.abs(Yt.ofRingSigned(t))}if(L(arguments[0],H)){var e=arguments[0];return Math.abs(Yt.ofRingSigned(e))}},Yt.ofRingSigned=function(){if(arguments[0]instanceof Array){var t=arguments[0];if(t.length<3)return 0;for(var e=0,n=t[0].x,i=1;i<t.length-1;i++){var r=t[i].x-n,s=t[i+1].y;e+=r*(t[i-1].y-s)}return e/2}if(L(arguments[0],H)){var o=arguments[0],a=o.size();if(a<3)return 0;var u=new x,l=new x,h=new x;o.getCoordinate(0,l),o.getCoordinate(1,h);n=l.x;h.x-=n;for(e=0,i=1;i<a-1;i++)u.y=l.y,l.x=h.x,l.y=h.y,o.getCoordinate(i+1,h),h.x-=n,e+=l.x*(u.y-h.y);return e/2}},e(kt.prototype,{interfaces_:function(){return[]},getClass:function(){return kt}}),p(Ut,K),e(Ut.prototype,{computeEnvelopeInternal:function(){return this._shell.getEnvelopeInternal()},getCoordinates:function(){if(this.isEmpty())return[];for(var t=new Array(this.getNumPoints()).fill(null),e=-1,n=this._shell.getCoordinates(),i=0;i<n.length;i++)t[++e]=n[i];for(var r=0;r<this._holes.length;r++)for(var s=this._holes[r].getCoordinates(),o=0;o<s.length;o++)t[++e]=s[o];return t},getArea:function(){var t=0;t+=Yt.ofRing(this._shell.getCoordinateSequence());for(var e=0;e<this._holes.length;e++)t-=Yt.ofRing(this._holes[e].getCoordinateSequence());return t},isRectangle:function(){if(0!==this.getNumInteriorRing())return!1;if(null===this._shell)return!1;if(5!==this._shell.getNumPoints())return!1;for(var t=this._shell.getCoordinateSequence(),e=this.getEnvelopeInternal(),n=0;n<5;n++){if((s=t.getX(n))!==e.getMinX()&&s!==e.getMaxX())return!1;if((o=t.getY(n))!==e.getMinY()&&o!==e.getMaxY())return!1}var i=t.getX(0),r=t.getY(0);for(n=1;n<=4;n++){var s,o;if((s=t.getX(n))!==i===((o=t.getY(n))!==r))return!1;i=s,r=o}return!0},equalsExact:function(){if(2===arguments.length&&"number"==typeof arguments[1]&&arguments[0]instanceof K){var t=arguments[0],e=arguments[1];if(!this.isEquivalentClass(t))return!1;var n=t,i=this._shell,r=n._shell;if(!i.equalsExact(r,e))return!1;if(this._holes.length!==n._holes.length)return!1;for(var s=0;s<this._holes.length;s++)if(!this._holes[s].equalsExact(n._holes[s],e))return!1;return!0}return K.prototype.equalsExact.apply(this,arguments)},normalize:function(){if(0===arguments.length){this.normalize(this._shell,!0);for(var t=0;t<this._holes.length;t++)this.normalize(this._holes[t],!1);It.sort(this._holes)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];if(e.isEmpty())return null;var i=new Array(e.getCoordinates().length-1).fill(null);Y.arraycopy(e.getCoordinates(),0,i,0,i.length);var r=nt.minCoordinate(e.getCoordinates());nt.scroll(i,r),Y.arraycopy(i,0,e.getCoordinates(),0,i.length),e.getCoordinates()[i.length]=i[0],V.isCCW(e.getCoordinates())===n&&nt.reverse(e.getCoordinates())}},getCoordinate:function(){return this._shell.getCoordinate()},getNumInteriorRing:function(){return this._holes.length},getBoundaryDimension:function(){return 1},getTypeCode:function(){return K.TYPECODE_POLYGON},getDimension:function(){return 2},getLength:function(){var t=0;t+=this._shell.getLength();for(var e=0;e<this._holes.length;e++)t+=this._holes[e].getLength();return t},getNumPoints:function(){for(var t=this._shell.getNumPoints(),e=0;e<this._holes.length;e++)t+=this._holes[e].getNumPoints();return t},reverse:function(){var t=this.copy();t._shell=this._shell.copy().reverse(),t._holes=new Array(this._holes.length).fill(null);for(var e=0;e<this._holes.length;e++)t._holes[e]=this._holes[e].copy().reverse();return t},convexHull:function(){return this.getExteriorRing().convexHull()},compareToSameClass:function(){if(1===arguments.length){var t=arguments[0],e=this._shell,n=t._shell;return e.compareToSameClass(n)}if(2===arguments.length){var i=arguments[0],r=arguments[1],s=i,o=(e=this._shell,n=s._shell,e.compareToSameClass(n,r));if(0!==o)return o;for(var a=this.getNumInteriorRing(),u=s.getNumInteriorRing(),l=0;l<a&&l<u;){var h=this.getInteriorRingN(l),c=s.getInteriorRingN(l),f=h.compareToSameClass(c,r);if(0!==f)return f;l++}return l<a?1:l<u?-1:0}},apply:function(){if(L(arguments[0],Z)){var t=arguments[0];this._shell.apply(t);for(var e=0;e<this._holes.length;e++)this._holes[e].apply(t)}else if(L(arguments[0],St)){var n=arguments[0];if(this._shell.apply(n),!n.isDone())for(e=0;e<this._holes.length&&(this._holes[e].apply(n),!n.isDone());e++);n.isGeometryChanged()&&this.geometryChanged()}else if(L(arguments[0],Ct)){arguments[0].filter(this)}else if(L(arguments[0],j)){var i=arguments[0];i.filter(this),this._shell.apply(i);for(e=0;e<this._holes.length;e++)this._holes[e].apply(i)}},getBoundary:function(){if(this.isEmpty())return this.getFactory().createMultiLineString();var t=new Array(this._holes.length+1).fill(null);t[0]=this._shell;for(var e=0;e<this._holes.length;e++)t[e+1]=this._holes[e];return t.length<=1?this.getFactory().createLinearRing(t[0].getCoordinateSequence()):this.getFactory().createMultiLineString(t)},getGeometryType:function(){return K.TYPENAME_POLYGON},copy:function(){for(var t=this._shell.copy(),e=new Array(this._holes.length).fill(null),n=0;n<this._holes.length;n++)e[n]=this._holes[n].copy();return new Ut(t,e,this._factory)},getExteriorRing:function(){return this._shell},isEmpty:function(){return this._shell.isEmpty()},getInteriorRingN:function(t){return this._holes[t]},interfaces_:function(){return[kt]},getClass:function(){return Ut}}),Ut.serialVersionUID=-0x307ffefd8dc97200,p(Xt,Lt),e(Xt.prototype,{isValid:function(){return!0},equalsExact:function(){if(2===arguments.length&&"number"==typeof arguments[1]&&arguments[0]instanceof K){var t=arguments[0],e=arguments[1];return!!this.isEquivalentClass(t)&&Lt.prototype.equalsExact.call(this,t,e)}return Lt.prototype.equalsExact.apply(this,arguments)},getCoordinate:function(){if(1===arguments.length&&Number.isInteger(arguments[0])){var t=arguments[0];return this._geometries[t].getCoordinate()}return Lt.prototype.getCoordinate.apply(this,arguments)},getBoundaryDimension:function(){return Nt.FALSE},getTypeCode:function(){return K.TYPECODE_MULTIPOINT},getDimension:function(){return 0},getBoundary:function(){return this.getFactory().createGeometryCollection()},getGeometryType:function(){return K.TYPENAME_MULTIPOINT},copy:function(){for(var t=new Array(this._geometries.length).fill(null),e=0;e<t.length;e++)t[e]=this._geometries[e].copy();return new Xt(t,this._factory)},interfaces_:function(){return[zt]},getClass:function(){return Xt}}),Xt.serialVersionUID=-0x6fb1ed4162e0fc00,p(Ht,Bt),e(Ht.prototype,{getBoundaryDimension:function(){return Nt.FALSE},isClosed:function(){return!!this.isEmpty()||Bt.prototype.isClosed.call(this)},getTypeCode:function(){return K.TYPECODE_LINEARRING},reverse:function(){var t=this._points.copy();return qt.reverse(t),this.getFactory().createLinearRing(t)},validateConstruction:function(){if(!this.isEmpty()&&!Bt.prototype.isClosed.call(this))throw new c("Points of LinearRing do not form a closed linestring");if(1<=this.getCoordinateSequence().size()&&this.getCoordinateSequence().size()<Ht.MINIMUM_VALID_SIZE)throw new c("Invalid number of points in LinearRing (found "+this.getCoordinateSequence().size()+" - must be 0 or >= 4)")},getGeometryType:function(){return K.TYPENAME_LINEARRING},copy:function(){return new Ht(this._points.copy(),this._factory)},interfaces_:function(){return[]},getClass:function(){return Ht}}),Ht.MINIMUM_VALID_SIZE=4,Ht.serialVersionUID=-0x3b229e262367a600,p(Wt,Lt),e(Wt.prototype,{equalsExact:function(){if(2===arguments.length&&"number"==typeof arguments[1]&&arguments[0]instanceof K){var t=arguments[0],e=arguments[1];return!!this.isEquivalentClass(t)&&Lt.prototype.equalsExact.call(this,t,e)}return Lt.prototype.equalsExact.apply(this,arguments)},getBoundaryDimension:function(){return 1},getTypeCode:function(){return K.TYPECODE_MULTIPOLYGON},getDimension:function(){return 2},reverse:function(){for(var t=this._geometries.length,e=new Array(t).fill(null),n=0;n<this._geometries.length;n++)e[n]=this._geometries[n].reverse();return this.getFactory().createMultiPolygon(e)},getBoundary:function(){if(this.isEmpty())return this.getFactory().createMultiLineString();for(var t=new P,e=0;e<this._geometries.length;e++)for(var n=this._geometries[e].getBoundary(),i=0;i<n.getNumGeometries();i++)t.add(n.getGeometryN(i));var r=new Array(t.size()).fill(null);return this.getFactory().createMultiLineString(t.toArray(r))},getGeometryType:function(){return K.TYPENAME_MULTIPOLYGON},copy:function(){for(var t=new Array(this._geometries.length).fill(null),e=0;e<t.length;e++)t[e]=this._geometries[e].copy();return new Wt(t,this._factory)},interfaces_:function(){return[kt]},getClass:function(){return Wt}}),Wt.serialVersionUID=-0x7a5aa1369171980,e(jt.prototype,{setCopyUserData:function(t){this._isUserDataCopied=t},edit:function(t,e){if(null===t)return null;var n=this.editInternal(t,e);return this._isUserDataCopied&&n.setUserData(t.getUserData()),n},editInternal:function(t,e){return null===this._factory&&(this._factory=t.getFactory()),t instanceof Lt?this.editGeometryCollection(t,e):t instanceof Ut?this.editPolygon(t,e):t instanceof Vt?e.edit(t,this._factory):t instanceof Bt?e.edit(t,this._factory):(y.shouldNeverReachHere("Unsupported Geometry class: "+t.getClass().getName()),null)},editGeometryCollection:function(t,e){for(var n=e.edit(t,this._factory),i=new P,r=0;r<n.getNumGeometries();r++){var s=this.edit(n.getGeometryN(r),e);null===s||s.isEmpty()||i.add(s)}return n.getClass()===Xt?this._factory.createMultiPoint(i.toArray([])):n.getClass()===wt?this._factory.createMultiLineString(i.toArray([])):n.getClass()===Wt?this._factory.createMultiPolygon(i.toArray([])):this._factory.createGeometryCollection(i.toArray([]))},editPolygon:function(t,e){var n=e.edit(t,this._factory);if(null===n&&(n=this._factory.createPolygon()),n.isEmpty())return n;var i=this.edit(n.getExteriorRing(),e);if(null===i||i.isEmpty())return this._factory.createPolygon();for(var r=new P,s=0;s<n.getNumInteriorRing();s++){var o=this.edit(n.getInteriorRingN(s),e);null===o||o.isEmpty()||r.add(o)}return this._factory.createPolygon(i,r.toArray([]))},interfaces_:function(){return[]},getClass:function(){return jt}}),jt.GeometryEditorOperation=Kt,e(Zt.prototype,{edit:function(t,e){return t},interfaces_:function(){return[Kt]},getClass:function(){return Zt}}),e(Qt.prototype,{edit:function(t,e){var n=this.edit(t.getCoordinates(),t);return t instanceof Ht?null===n?e.createLinearRing():e.createLinearRing(n):t instanceof Bt?null===n?e.createLineString():e.createLineString(n):t instanceof Vt?null===n||0===n.length?e.createPoint():e.createPoint(n[0]):t},interfaces_:function(){return[Kt]},getClass:function(){return Qt}}),e(Jt.prototype,{edit:function(t,e){return t instanceof Ht?e.createLinearRing(this.edit(t.getCoordinateSequence(),t)):t instanceof Bt?e.createLineString(this.edit(t.getCoordinateSequence(),t)):t instanceof Vt?e.createPoint(this.edit(t.getCoordinateSequence(),t)):t},interfaces_:function(){return[Kt]},getClass:function(){return Jt}}),jt.NoOpGeometryOperation=Zt,jt.CoordinateOperation=Qt,jt.CoordinateSequenceOperation=Jt,e($t.prototype,{setOrdinate:function(t,e,n){switch(e){case H.X:this._coordinates[t].x=n;break;case H.Y:this._coordinates[t].y=n;break;case H.Z:this._coordinates[t].z=n;break;default:throw new c("invalid ordinateIndex")}},size:function(){return this._coordinates.length},getOrdinate:function(t,e){switch(e){case H.X:return this._coordinates[t].x;case H.Y:return this._coordinates[t].y;case H.Z:return this._coordinates[t].z}return S.NaN},getCoordinate:function(){if(1===arguments.length){var t=arguments[0];return this._coordinates[t]}if(2===arguments.length){var e=arguments[0],n=arguments[1];n.x=this._coordinates[e].x,n.y=this._coordinates[e].y,n.z=this._coordinates[e].z}},getCoordinateCopy:function(t){return new x(this._coordinates[t])},getDimension:function(){return this._dimension},getX:function(t){return this._coordinates[t].x},expandEnvelope:function(t){for(var e=0;e<this._coordinates.length;e++)t.expandToInclude(this._coordinates[e]);return t},copy:function(){for(var t=new Array(this.size()).fill(null),e=0;e<this._coordinates.length;e++)t[e]=this._coordinates[e].copy();return new $t(t,this._dimension)},toString:function(){if(0<this._coordinates.length){var t=new Gt(17*this._coordinates.length);t.append("("),t.append(this._coordinates[0]);for(var e=1;e<this._coordinates.length;e++)t.append(", "),t.append(this._coordinates[e]);return t.append(")"),t.toString()}return"()"},getY:function(t){return this._coordinates[t].y},toCoordinateArray:function(){return this._coordinates},interfaces_:function(){return[H,l]},getClass:function(){return $t}}),$t.serialVersionUID=-0xcb44a778db18e00,e(te.prototype,{readResolve:function(){return te.instance()},create:function(){if(1===arguments.length){if(arguments[0]instanceof Array)return new $t(arguments[0]);if(L(arguments[0],H))return new $t(arguments[0])}else if(2===arguments.length){var t=arguments[0],e=arguments[1];return 3<e&&(e=3),e<2?new $t(t):new $t(t,e)}},interfaces_:function(){return[W,l]},getClass:function(){return te}}),te.instance=function(){return te.instanceObject},te.serialVersionUID=-0x38e49fa6cf6f2e00,te.instanceObject=new te;var ee=Object.defineProperty;var ne=function(t,e){function n(t){if(!this||this.constructor!==n)return new n(t);this._keys=[],this._values=[],this._itp=[],this.objectOnly=e,t&&function(t){this.add?t.forEach(this.add,this):t.forEach(function(t){this.set(t[0],t[1])},this)}.call(this,t)}e||ee(t,"size",{get:re});return(t.constructor=n).prototype=t,n}({delete:function(t){this.has(t)&&(this._keys.splice(xt,1),this._values.splice(xt,1),this._itp.forEach(function(t){xt<t[0]&&t[0]--}));return-1<xt},has:function(t){return function(t,e){if(this.objectOnly&&e!==Object(e))throw new TypeError("Invalid value used as weak collection key");if(e!=e||0===e)for(xt=t.length;xt--&&(n=t[xt],i=e,n!==i&&(n==n||i==i)););else xt=t.indexOf(e);var n,i;return-1<xt}.call(this,this._keys,t)},get:function(t){return this.has(t)?this._values[xt]:void 0},set:function(t,e){return this.has(t)?this._values[xt]=e:this._values[this._keys.push(t)-1]=e,this},keys:function(){return ie(this._itp,this._keys)},values:function(){return ie(this._itp,this._values)},entries:function(){return ie(this._itp,this._keys,this._values)},forEach:function(t,e){for(var n=this.entries();;){var i=n.next();if(i.done)break;t.call(e,i.value[1],i.value[0],this)}},clear:function(){(this._keys||0).length=this._values.length=0}});function ie(n,i,r){var s=[0],o=!1;return n.push(s),{next:function(){var t,e=s[0];return!o&&e<i.length?(t=r?[i[e],r[e]]:i[e],s[0]++):(o=!0,n.splice(n.indexOf(s),1)),{done:o,value:t}}}}function re(){return this._values.length}var se="undefined"!=typeof Map&&Map.prototype.values?Map:ne;function oe(){this.map_=new se}function ae(){if(this._modelType=null,this._scale=null,0===arguments.length)this._modelType=ae.FLOATING;else if(1===arguments.length)if(arguments[0]instanceof ue){var t=arguments[0];(this._modelType=t)===ae.FIXED&&this.setScale(1)}else if("number"==typeof arguments[0]){var e=arguments[0];this._modelType=ae.FIXED,this.setScale(e)}else if(arguments[0]instanceof ae){var n=arguments[0];this._modelType=n._modelType,this._scale=n._scale}}function ue(){this._name=null;var t=arguments[0];this._name=t,ue.nameToTypeMap.put(t,this)}function le(){if(this._precisionModel=null,this._coordinateSequenceFactory=null,this._SRID=null,0===arguments.length)le.call(this,new ae,0);else if(1===arguments.length){if(L(arguments[0],W)){var t=arguments[0];le.call(this,new ae,0,t)}else if(arguments[0]instanceof ae){var e=arguments[0];le.call(this,e,0,le.getDefaultCoordinateSequenceFactory())}}else if(2===arguments.length){var n=arguments[0],i=arguments[1];le.call(this,n,i,le.getDefaultCoordinateSequenceFactory())}else if(3===arguments.length){var r=arguments[0],s=arguments[1],o=arguments[2];this._precisionModel=r,this._coordinateSequenceFactory=o,this._SRID=s}}(oe.prototype=new st).get=function(t){return this.map_.get(t)||null},oe.prototype.put=function(t,e){return this.map_.set(t,e),e},oe.prototype.values=function(){for(var t=new P,e=this.map_.values(),n=e.next();!n.done;)t.add(n.value),n=e.next();return t},oe.prototype.entrySet=function(){var e=new ut;return this.map_.entries().forEach(function(t){return e.add(t)}),e},oe.prototype.size=function(){return this.map_.size()},e(ae.prototype,{equals:function(t){if(!(t instanceof ae))return!1;var e=t;return this._modelType===e._modelType&&this._scale===e._scale},compareTo:function(t){var e=t,n=this.getMaximumSignificantDigits(),i=e.getMaximumSignificantDigits();return new G(n).compareTo(new G(i))},getScale:function(){return this._scale},isFloating:function(){return this._modelType===ae.FLOATING||this._modelType===ae.FLOATING_SINGLE},getType:function(){return this._modelType},toString:function(){var t="UNKNOWN";return this._modelType===ae.FLOATING?t="Floating":this._modelType===ae.FLOATING_SINGLE?t="Floating-Single":this._modelType===ae.FIXED&&(t="Fixed (Scale="+this.getScale()+")"),t},makePrecise:function(){if("number"==typeof arguments[0]){var t=arguments[0];return S.isNaN(t)?t:this._modelType===ae.FLOATING_SINGLE?t:this._modelType===ae.FIXED?Math.round(t*this._scale)/this._scale:t}if(arguments[0]instanceof x){var e=arguments[0];if(this._modelType===ae.FLOATING)return null;e.x=this.makePrecise(e.x),e.y=this.makePrecise(e.y)}},getMaximumSignificantDigits:function(){var t=16;return this._modelType===ae.FLOATING?t=16:this._modelType===ae.FLOATING_SINGLE?t=6:this._modelType===ae.FIXED&&(t=1+Math.trunc(Math.ceil(Math.log(this.getScale())/Math.log(10)))),t},setScale:function(t){this._scale=Math.abs(t)},interfaces_:function(){return[l,n]},getClass:function(){return ae}}),ae.mostPrecise=function(t,e){return 0<=t.compareTo(e)?t:e},e(ue.prototype,{readResolve:function(){return ue.nameToTypeMap.get(this._name)},toString:function(){return this._name},interfaces_:function(){return[l]},getClass:function(){return ue}}),ue.serialVersionUID=-552860263173159e4,ue.nameToTypeMap=new oe,ae.Type=ue,ae.serialVersionUID=0x6bee6404e9a25c00,ae.FIXED=new ue("FIXED"),ae.FLOATING=new ue("FLOATING"),ae.FLOATING_SINGLE=new ue("FLOATING SINGLE"),ae.maximumPreciseValue=9007199254740992,e(le.prototype,{toGeometry:function(t){return t.isNull()?this.createPoint():t.getMinX()===t.getMaxX()&&t.getMinY()===t.getMaxY()?this.createPoint(new x(t.getMinX(),t.getMinY())):t.getMinX()===t.getMaxX()||t.getMinY()===t.getMaxY()?this.createLineString([new x(t.getMinX(),t.getMinY()),new x(t.getMaxX(),t.getMaxY())]):this.createPolygon(this.createLinearRing([new x(t.getMinX(),t.getMinY()),new x(t.getMinX(),t.getMaxY()),new x(t.getMaxX(),t.getMaxY()),new x(t.getMaxX(),t.getMinY()),new x(t.getMinX(),t.getMinY())]),null)},createLineString:function(){if(0===arguments.length)return this.createLineString(this.getCoordinateSequenceFactory().create([]));if(1===arguments.length){if(arguments[0]instanceof Array){var t=arguments[0];return this.createLineString(null!==t?this.getCoordinateSequenceFactory().create(t):null)}if(L(arguments[0],H))return new Bt(arguments[0],this)}},createMultiLineString:function(){return 0===arguments.length?new wt(null,this):1===arguments.length?new wt(arguments[0],this):void 0},buildGeometry:function(t){for(var e=null,n=!1,i=!1,r=t.iterator();r.hasNext();){var s=r.next(),o=s.getClass();null===e&&(e=o),o!==e&&(n=!0),s instanceof Lt&&(i=!0)}if(null===e)return this.createGeometryCollection();if(n||i)return this.createGeometryCollection(le.toGeometryArray(t));var a=t.iterator().next();if(1<t.size()){if(a instanceof Ut)return this.createMultiPolygon(le.toPolygonArray(t));if(a instanceof Bt)return this.createMultiLineString(le.toLineStringArray(t));if(a instanceof Vt)return this.createMultiPoint(le.toPointArray(t));y.shouldNeverReachHere("Unhandled class: "+a.getClass().getName())}return a},createMultiPointFromCoords:function(t){return this.createMultiPoint(null!==t?this.getCoordinateSequenceFactory().create(t):null)},createPoint:function(){if(0===arguments.length)return this.createPoint(this.getCoordinateSequenceFactory().create([]));if(1===arguments.length){if(arguments[0]instanceof x){var t=arguments[0];return this.createPoint(null!==t?this.getCoordinateSequenceFactory().create([t]):null)}if(L(arguments[0],H))return new Vt(arguments[0],this)}},getCoordinateSequenceFactory:function(){return this._coordinateSequenceFactory},createPolygon:function(){if(0===arguments.length)return this.createPolygon(null,null);if(1===arguments.length){if(L(arguments[0],H)){var t=arguments[0];return this.createPolygon(this.createLinearRing(t))}if(arguments[0]instanceof Array){var e=arguments[0];return this.createPolygon(this.createLinearRing(e))}if(arguments[0]instanceof Ht){var n=arguments[0];return this.createPolygon(n,null)}}else if(2===arguments.length){return new Ut(arguments[0],arguments[1],this)}},getSRID:function(){return this._SRID},createGeometryCollection:function(){return 0===arguments.length?new Lt(null,this):1===arguments.length?new Lt(arguments[0],this):void 0},createGeometry:function(t){return new jt(this).edit(t,{edit:function(){if(2===arguments.length&&arguments[1]instanceof K&&L(arguments[0],H)){var t=arguments[0];return this._coordinateSequenceFactory.create(t)}}})},getPrecisionModel:function(){return this._precisionModel},createLinearRing:function(){if(0===arguments.length)return this.createLinearRing(this.getCoordinateSequenceFactory().create([]));if(1===arguments.length){if(arguments[0]instanceof Array){var t=arguments[0];return this.createLinearRing(null!==t?this.getCoordinateSequenceFactory().create(t):null)}if(L(arguments[0],H))return new Ht(arguments[0],this)}},createMultiPolygon:function(){return 0===arguments.length?new Wt(null,this):1===arguments.length?new Wt(arguments[0],this):void 0},createMultiPoint:function(){if(0===arguments.length)return new Xt(null,this);if(1===arguments.length){if(arguments[0]instanceof Array)return new Xt(arguments[0],this);if(L(arguments[0],H)){var t=arguments[0];if(null===t)return this.createMultiPoint(new Array(0).fill(null));for(var e=new Array(t.size()).fill(null),n=0;n<t.size();n++){var i=this.getCoordinateSequenceFactory().create(1,t.getDimension());qt.copy(t,n,i,0,1),e[n]=this.createPoint(i)}return this.createMultiPoint(e)}}},interfaces_:function(){return[l]},getClass:function(){return le}}),le.toMultiPolygonArray=function(t){var e=new Array(t.size()).fill(null);return t.toArray(e)},le.toGeometryArray=function(t){if(null===t)return null;var e=new Array(t.size()).fill(null);return t.toArray(e)},le.getDefaultCoordinateSequenceFactory=function(){return te.instance()},le.toMultiLineStringArray=function(t){var e=new Array(t.size()).fill(null);return t.toArray(e)},le.toLineStringArray=function(t){var e=new Array(t.size()).fill(null);return t.toArray(e)},le.toMultiPointArray=function(t){var e=new Array(t.size()).fill(null);return t.toArray(e)},le.toLinearRingArray=function(t){var e=new Array(t.size()).fill(null);return t.toArray(e)},le.toPointArray=function(t){var e=new Array(t.size()).fill(null);return t.toArray(e)},le.toPolygonArray=function(t){var e=new Array(t.size()).fill(null);return t.toArray(e)},le.createPointFromInternalCoord=function(t,e){return e.getPrecisionModel().makePrecise(t),e.getFactory().createPoint(t)},le.serialVersionUID=-0x5ea75f2051eeb400;var he={typeStr:/^\s*(\w+)\s*\(\s*(.*)\s*\)\s*$/,emptyTypeStr:/^\s*(\w+)\s*EMPTY\s*$/,spaces:/\s+/,parenComma:/\)\s*,\s*\(/,doubleParenComma:/\)\s*\)\s*,\s*\(\s*\(/,trimParens:/^\s*\(?(.*?)\)?\s*$/};function ce(t){this.geometryFactory=t||new le,this.precisionModel=this.geometryFactory.getPrecisionModel()}e(ce.prototype,{read:function(t){var e,n,i;t=t.replace(/[\n\r]/g," ");var r=he.typeStr.exec(t);if(-1!==t.search("EMPTY")&&((r=he.emptyTypeStr.exec(t))[2]=void 0),r&&(n=r[1].toLowerCase(),i=r[2],ge[n]&&(e=ge[n].call(this,i))),void 0===e)throw new Error("Could not parse WKT "+t);return e},write:function(t){return this.extractGeometry(t)},extractGeometry:function(t){var e=t.getGeometryType().toLowerCase();if(!fe[e])return null;var n=e.toUpperCase();return t.isEmpty()?n+" EMPTY":n+"("+fe[e].call(this,t)+")"}});var fe={coordinate:function(t){return t.x+" "+t.y},point:function(t){return fe.coordinate.call(this,t._coordinates._coordinates[0])},multipoint:function(t){for(var e=[],n=0,i=t._geometries.length;n<i;++n)e.push("("+fe.point.call(this,t._geometries[n])+")");return e.join(",")},linestring:function(t){for(var e=[],n=0,i=t._points._coordinates.length;n<i;++n)e.push(fe.coordinate.call(this,t._points._coordinates[n]));return e.join(",")},linearring:function(t){for(var e=[],n=0,i=t._points._coordinates.length;n<i;++n)e.push(fe.coordinate.call(this,t._points._coordinates[n]));return e.join(",")},multilinestring:function(t){for(var e=[],n=0,i=t._geometries.length;n<i;++n)e.push("("+fe.linestring.call(this,t._geometries[n])+")");return e.join(",")},polygon:function(t){var e=[];e.push("("+fe.linestring.call(this,t._shell)+")");for(var n=0,i=t._holes.length;n<i;++n)e.push("("+fe.linestring.call(this,t._holes[n])+")");return e.join(",")},multipolygon:function(t){for(var e=[],n=0,i=t._geometries.length;n<i;++n)e.push("("+fe.polygon.call(this,t._geometries[n])+")");return e.join(",")},geometrycollection:function(t){for(var e=[],n=0,i=t._geometries.length;n<i;++n)e.push(this.extractGeometry(t._geometries[n]));return e.join(",")}},ge={coord:function(t){var e=t.trim().split(he.spaces),n=new x(Number.parseFloat(e[0]),Number.parseFloat(e[1]));return this.precisionModel.makePrecise(n),n},point:function(t){return void 0===t?this.geometryFactory.createPoint():this.geometryFactory.createPoint(ge.coord.call(this,t))},multipoint:function(t){if(void 0===t)return this.geometryFactory.createMultiPoint();for(var e,n=t.trim().split(","),i=[],r=0,s=n.length;r<s;++r)e=n[r].replace(he.trimParens,"$1"),i.push(ge.point.call(this,e));return this.geometryFactory.createMultiPoint(i)},linestring:function(t){if(void 0===t)return this.geometryFactory.createLineString();for(var e=t.trim().split(","),n=[],i=0,r=e.length;i<r;++i)n.push(ge.coord.call(this,e[i]));return this.geometryFactory.createLineString(n)},linearring:function(t){if(void 0===t)return this.geometryFactory.createLinearRing();for(var e=t.trim().split(","),n=[],i=0,r=e.length;i<r;++i)n.push(ge.coord.call(this,e[i]));return this.geometryFactory.createLinearRing(n)},multilinestring:function(t){if(void 0===t)return this.geometryFactory.createMultiLineString();for(var e,n=t.trim().split(he.parenComma),i=[],r=0,s=n.length;r<s;++r)e=n[r].replace(he.trimParens,"$1"),i.push(ge.linestring.call(this,e));return this.geometryFactory.createMultiLineString(i)},polygon:function(t){if(void 0===t)return this.geometryFactory.createPolygon();for(var e,n,i,r,s=t.trim().split(he.parenComma),o=[],a=0,u=s.length;a<u;++a)e=s[a].replace(he.trimParens,"$1"),n=ge.linestring.call(this,e),i=this.geometryFactory.createLinearRing(n._points),0===a?r=i:o.push(i);return this.geometryFactory.createPolygon(r,o)},multipolygon:function(t){if(void 0===t)return this.geometryFactory.createMultiPolygon();for(var e,n=t.trim().split(he.doubleParenComma),i=[],r=0,s=n.length;r<s;++r)e=n[r].replace(he.trimParens,"$1"),i.push(ge.polygon.call(this,e));return this.geometryFactory.createMultiPolygon(i)},geometrycollection:function(t){if(void 0===t)return this.geometryFactory.createGeometryCollection();for(var e=(t=t.replace(/,\s*([A-Za-z])/g,"|$1")).trim().split("|"),n=[],i=0,r=e.length;i<r;++i)n.push(this.read(e[i]));return this.geometryFactory.createGeometryCollection(n)}};function de(t){this.parser=new ce(t)}function _e(){this._result=null,this._inputLines=Array(2).fill().map(function(){return Array(2)}),this._intPt=new Array(2).fill(null),this._intLineIndex=null,this._isProper=null,this._pa=null,this._pb=null,this._precisionModel=null,this._intPt[0]=new x,this._intPt[1]=new x,this._pa=this._intPt[0],this._pb=this._intPt[1],this._result=0}function pe(){_e.apply(this)}function me(){if(this.p0=null,this.p1=null,0===arguments.length)me.call(this,new x,new x);else if(1===arguments.length){var t=arguments[0];me.call(this,t.p0,t.p1)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this.p0=e,this.p1=n}else if(4===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2],o=arguments[3];me.call(this,new x(i,r),new x(s,o))}}function ve(){}function ye(){if(this._matrix=null,0===arguments.length)this._matrix=Array(3).fill().map(function(){return Array(3)}),this.setAll(Nt.FALSE);else if(1===arguments.length)if("string"==typeof arguments[0]){var t=arguments[0];ye.call(this),this.set(t)}else if(arguments[0]instanceof ye){var e=arguments[0];ye.call(this),this._matrix[ve.INTERIOR][ve.INTERIOR]=e._matrix[ve.INTERIOR][ve.INTERIOR],this._matrix[ve.INTERIOR][ve.BOUNDARY]=e._matrix[ve.INTERIOR][ve.BOUNDARY],this._matrix[ve.INTERIOR][ve.EXTERIOR]=e._matrix[ve.INTERIOR][ve.EXTERIOR],this._matrix[ve.BOUNDARY][ve.INTERIOR]=e._matrix[ve.BOUNDARY][ve.INTERIOR],this._matrix[ve.BOUNDARY][ve.BOUNDARY]=e._matrix[ve.BOUNDARY][ve.BOUNDARY],this._matrix[ve.BOUNDARY][ve.EXTERIOR]=e._matrix[ve.BOUNDARY][ve.EXTERIOR],this._matrix[ve.EXTERIOR][ve.INTERIOR]=e._matrix[ve.EXTERIOR][ve.INTERIOR],this._matrix[ve.EXTERIOR][ve.BOUNDARY]=e._matrix[ve.EXTERIOR][ve.BOUNDARY],this._matrix[ve.EXTERIOR][ve.EXTERIOR]=e._matrix[ve.EXTERIOR][ve.EXTERIOR]}}function xe(){}function Ee(){this.p0=null,this.p1=null,this.p2=null;var t=arguments[0],e=arguments[1],n=arguments[2];this.p0=t,this.p1=e,this.p2=n}e(de.prototype,{write:function(t){return this.parser.write(t)}}),e(de,{toLineString:function(t,e){if(2!==arguments.length)throw new Error("Not implemented");return"LINESTRING ( "+t.x+" "+t.y+", "+e.x+" "+e.y+" )"}}),e(_e.prototype,{getIndexAlongSegment:function(t,e){return this.computeIntLineIndex(),this._intLineIndex[t][e]},getTopologySummary:function(){var t=new Gt;return this.isEndPoint()&&t.append(" endpoint"),this._isProper&&t.append(" proper"),this.isCollinear()&&t.append(" collinear"),t.toString()},computeIntersection:function(t,e,n,i){this._inputLines[0][0]=t,this._inputLines[0][1]=e,this._inputLines[1][0]=n,this._inputLines[1][1]=i,this._result=this.computeIntersect(t,e,n,i)},getIntersectionNum:function(){return this._result},computeIntLineIndex:function(){if(0===arguments.length)null===this._intLineIndex&&(this._intLineIndex=Array(2).fill().map(function(){return Array(2)}),this.computeIntLineIndex(0),this.computeIntLineIndex(1));else if(1===arguments.length){var t=arguments[0],e=this.getEdgeDistance(t,0);this.getEdgeDistance(t,1)<e?(this._intLineIndex[t][0]=0,this._intLineIndex[t][1]=1):(this._intLineIndex[t][0]=1,this._intLineIndex[t][1]=0)}},isProper:function(){return this.hasIntersection()&&this._isProper},setPrecisionModel:function(t){this._precisionModel=t},isInteriorIntersection:function(){if(0===arguments.length)return!!this.isInteriorIntersection(0)||!!this.isInteriorIntersection(1);if(1===arguments.length){for(var t=arguments[0],e=0;e<this._result;e++)if(!this._intPt[e].equals2D(this._inputLines[t][0])&&!this._intPt[e].equals2D(this._inputLines[t][1]))return!0;return!1}},getIntersection:function(t){return this._intPt[t]},isEndPoint:function(){return this.hasIntersection()&&!this._isProper},hasIntersection:function(){return this._result!==_e.NO_INTERSECTION},getEdgeDistance:function(t,e){return _e.computeEdgeDistance(this._intPt[e],this._inputLines[t][0],this._inputLines[t][1])},isCollinear:function(){return this._result===_e.COLLINEAR_INTERSECTION},toString:function(){return de.toLineString(this._inputLines[0][0],this._inputLines[0][1])+" - "+de.toLineString(this._inputLines[1][0],this._inputLines[1][1])+this.getTopologySummary()},getEndpoint:function(t,e){return this._inputLines[t][e]},isIntersection:function(t){for(var e=0;e<this._result;e++)if(this._intPt[e].equals2D(t))return!0;return!1},getIntersectionAlongSegment:function(t,e){return this.computeIntLineIndex(),this._intPt[this._intLineIndex[t][e]]},interfaces_:function(){return[]},getClass:function(){return _e}}),_e.computeEdgeDistance=function(t,e,n){var i=Math.abs(n.x-e.x),r=Math.abs(n.y-e.y),s=-1;if(t.equals(e))s=0;else if(t.equals(n))s=r<i?i:r;else{var o=Math.abs(t.x-e.x),a=Math.abs(t.y-e.y);0!==(s=r<i?o:a)||t.equals(e)||(s=Math.max(o,a))}return y.isTrue(!(0===s&&!t.equals(e)),"Bad distance calculation"),s},_e.nonRobustComputeEdgeDistance=function(t,e,n){var i=t.x-e.x,r=t.y-e.y,s=Math.sqrt(i*i+r*r);return y.isTrue(!(0===s&&!t.equals(e)),"Invalid distance calculation"),s},_e.DONT_INTERSECT=0,_e.DO_INTERSECT=1,_e.COLLINEAR=2,_e.NO_INTERSECTION=0,_e.POINT_INTERSECTION=1,_e.COLLINEAR_INTERSECTION=2,p(pe,_e),e(pe.prototype,{isInSegmentEnvelopes:function(t){var e=new M(this._inputLines[0][0],this._inputLines[0][1]),n=new M(this._inputLines[1][0],this._inputLines[1][1]);return e.contains(t)&&n.contains(t)},computeIntersection:function(){if(3!==arguments.length)return _e.prototype.computeIntersection.apply(this,arguments);var t=arguments[0],e=arguments[1],n=arguments[2];if(this._isProper=!1,M.intersects(e,n,t)&&0===V.index(e,n,t)&&0===V.index(n,e,t))return this._isProper=!0,(t.equals(e)||t.equals(n))&&(this._isProper=!1),this._result=_e.POINT_INTERSECTION,null;this._result=_e.NO_INTERSECTION},normalizeToMinimum:function(t,e,n,i,r){r.x=this.smallestInAbsValue(t.x,e.x,n.x,i.x),r.y=this.smallestInAbsValue(t.y,e.y,n.y,i.y),t.x-=r.x,t.y-=r.y,e.x-=r.x,e.y-=r.y,n.x-=r.x,n.y-=r.y,i.x-=r.x,i.y-=r.y},safeHCoordinateIntersection:function(e,n,i,r){var s=null;try{s=k.intersection(e,n,i,r)}catch(t){if(!(t instanceof A))throw t;s=pe.nearestEndpoint(e,n,i,r)}return s},intersection:function(t,e,n,i){var r=this.intersectionWithNormalization(t,e,n,i);return this.isInSegmentEnvelopes(r)||(r=new x(pe.nearestEndpoint(t,e,n,i))),null!==this._precisionModel&&this._precisionModel.makePrecise(r),r},smallestInAbsValue:function(t,e,n,i){var r=t,s=Math.abs(r);return Math.abs(e)<s&&(r=e,s=Math.abs(e)),Math.abs(n)<s&&(r=n,s=Math.abs(n)),Math.abs(i)<s&&(r=i),r},checkDD:function(t,e,n,i,r){var s=z.intersection(t,e,n,i),o=this.isInSegmentEnvelopes(s);Y.out.println("DD in env = "+o+"  --------------------- "+s),1e-4<r.distance(s)&&Y.out.println("Distance = "+r.distance(s))},intersectionWithNormalization:function(t,e,n,i){var r=new x(t),s=new x(e),o=new x(n),a=new x(i),u=new x;this.normalizeToEnvCentre(r,s,o,a,u);var l=this.safeHCoordinateIntersection(r,s,o,a);return l.x+=u.x,l.y+=u.y,l},computeCollinearIntersection:function(t,e,n,i){var r=M.intersects(t,e,n),s=M.intersects(t,e,i),o=M.intersects(n,i,t),a=M.intersects(n,i,e);return r&&s?(this._intPt[0]=n,this._intPt[1]=i,_e.COLLINEAR_INTERSECTION):o&&a?(this._intPt[0]=t,this._intPt[1]=e,_e.COLLINEAR_INTERSECTION):r&&o?(this._intPt[0]=n,this._intPt[1]=t,!n.equals(t)||s||a?_e.COLLINEAR_INTERSECTION:_e.POINT_INTERSECTION):r&&a?(this._intPt[0]=n,this._intPt[1]=e,!n.equals(e)||s||o?_e.COLLINEAR_INTERSECTION:_e.POINT_INTERSECTION):s&&o?(this._intPt[0]=i,this._intPt[1]=t,!i.equals(t)||r||a?_e.COLLINEAR_INTERSECTION:_e.POINT_INTERSECTION):s&&a?(this._intPt[0]=i,this._intPt[1]=e,!i.equals(e)||r||o?_e.COLLINEAR_INTERSECTION:_e.POINT_INTERSECTION):_e.NO_INTERSECTION},normalizeToEnvCentre:function(t,e,n,i,r){var s=t.x<e.x?t.x:e.x,o=t.y<e.y?t.y:e.y,a=t.x>e.x?t.x:e.x,u=t.y>e.y?t.y:e.y,l=n.x<i.x?n.x:i.x,h=n.y<i.y?n.y:i.y,c=n.x>i.x?n.x:i.x,f=n.y>i.y?n.y:i.y,g=((l<s?s:l)+(a<c?a:c))/2,d=((h<o?o:h)+(u<f?u:f))/2;r.x=g,r.y=d,t.x-=r.x,t.y-=r.y,e.x-=r.x,e.y-=r.y,n.x-=r.x,n.y-=r.y,i.x-=r.x,i.y-=r.y},computeIntersect:function(t,e,n,i){if(this._isProper=!1,!M.intersects(t,e,n,i))return _e.NO_INTERSECTION;var r=V.index(t,e,n),s=V.index(t,e,i);if(0<r&&0<s||r<0&&s<0)return _e.NO_INTERSECTION;var o=V.index(n,i,t),a=V.index(n,i,e);return 0<o&&0<a||o<0&&a<0?_e.NO_INTERSECTION:0===r&&0===s&&0===o&&0===a?this.computeCollinearIntersection(t,e,n,i):(0===r||0===s||0===o||0===a?(this._isProper=!1,t.equals2D(n)||t.equals2D(i)?this._intPt[0]=t:e.equals2D(n)||e.equals2D(i)?this._intPt[0]=e:0===r?this._intPt[0]=new x(n):0===s?this._intPt[0]=new x(i):0===o?this._intPt[0]=new x(t):0===a&&(this._intPt[0]=new x(e))):(this._isProper=!0,this._intPt[0]=this.intersection(t,e,n,i)),_e.POINT_INTERSECTION)},interfaces_:function(){return[]},getClass:function(){return pe}}),pe.nearestEndpoint=function(t,e,n,i){var r=t,s=X.pointToSegment(t,n,i),o=X.pointToSegment(e,n,i);return o<s&&(s=o,r=e),(o=X.pointToSegment(n,t,e))<s&&(s=o,r=n),(o=X.pointToSegment(i,t,e))<s&&(s=o,r=i),r},e(me.prototype,{minX:function(){return Math.min(this.p0.x,this.p1.x)},orientationIndex:function(){if(arguments[0]instanceof me){var t=arguments[0],e=V.index(this.p0,this.p1,t.p0),n=V.index(this.p0,this.p1,t.p1);return 0<=e&&0<=n?Math.max(e,n):e<=0&&n<=0?Math.max(e,n):0}if(arguments[0]instanceof x){var i=arguments[0];return V.index(this.p0,this.p1,i)}},toGeometry:function(t){return t.createLineString([this.p0,this.p1])},isVertical:function(){return this.p0.x===this.p1.x},equals:function(t){if(!(t instanceof me))return!1;var e=t;return this.p0.equals(e.p0)&&this.p1.equals(e.p1)},intersection:function(t){var e=new pe;return e.computeIntersection(this.p0,this.p1,t.p0,t.p1),e.hasIntersection()?e.getIntersection(0):null},project:function(){if(arguments[0]instanceof x){var t=arguments[0];if(t.equals(this.p0)||t.equals(this.p1))return new x(t);var e=this.projectionFactor(t),n=new x;return n.x=this.p0.x+e*(this.p1.x-this.p0.x),n.y=this.p0.y+e*(this.p1.y-this.p0.y),n}if(arguments[0]instanceof me){var i=arguments[0],r=this.projectionFactor(i.p0),s=this.projectionFactor(i.p1);if(1<=r&&1<=s)return null;if(r<=0&&s<=0)return null;var o=this.project(i.p0);r<0&&(o=this.p0),1<r&&(o=this.p1);var a=this.project(i.p1);return s<0&&(a=this.p0),1<s&&(a=this.p1),new me(o,a)}},normalize:function(){this.p1.compareTo(this.p0)<0&&this.reverse()},angle:function(){return Math.atan2(this.p1.y-this.p0.y,this.p1.x-this.p0.x)},getCoordinate:function(t){return 0===t?this.p0:this.p1},distancePerpendicular:function(t){return X.pointToLinePerpendicular(t,this.p0,this.p1)},minY:function(){return Math.min(this.p0.y,this.p1.y)},midPoint:function(){return me.midPoint(this.p0,this.p1)},projectionFactor:function(t){if(t.equals(this.p0))return 0;if(t.equals(this.p1))return 1;var e=this.p1.x-this.p0.x,n=this.p1.y-this.p0.y,i=e*e+n*n;return i<=0?S.NaN:((t.x-this.p0.x)*e+(t.y-this.p0.y)*n)/i},closestPoints:function(t){var e=this.intersection(t);if(null!==e)return[e,e];var n=new Array(2).fill(null),i=S.MAX_VALUE,r=null,s=this.closestPoint(t.p0);i=s.distance(t.p0),n[0]=s,n[1]=t.p0;var o=this.closestPoint(t.p1);(r=o.distance(t.p1))<i&&(i=r,n[0]=o,n[1]=t.p1);var a=t.closestPoint(this.p0);(r=a.distance(this.p0))<i&&(i=r,n[0]=this.p0,n[1]=a);var u=t.closestPoint(this.p1);return(r=u.distance(this.p1))<i&&(i=r,n[0]=this.p1,n[1]=u),n},closestPoint:function(t){var e=this.projectionFactor(t);return 0<e&&e<1?this.project(t):this.p0.distance(t)<this.p1.distance(t)?this.p0:this.p1},maxX:function(){return Math.max(this.p0.x,this.p1.x)},getLength:function(){return this.p0.distance(this.p1)},compareTo:function(t){var e=t,n=this.p0.compareTo(e.p0);return 0!==n?n:this.p1.compareTo(e.p1)},reverse:function(){var t=this.p0;this.p0=this.p1,this.p1=t},equalsTopo:function(t){return this.p0.equals(t.p0)&&this.p1.equals(t.p1)||this.p0.equals(t.p1)&&this.p1.equals(t.p0)},lineIntersection:function(t){try{return k.intersection(this.p0,this.p1,t.p0,t.p1)}catch(t){if(!(t instanceof A))throw t}return null},maxY:function(){return Math.max(this.p0.y,this.p1.y)},pointAlongOffset:function(t,e){var n=this.p0.x+t*(this.p1.x-this.p0.x),i=this.p0.y+t*(this.p1.y-this.p0.y),r=this.p1.x-this.p0.x,s=this.p1.y-this.p0.y,o=Math.sqrt(r*r+s*s),a=0,u=0;if(0!==e){if(o<=0)throw new IllegalStateException("Cannot compute offset from zero-length line segment");a=e*r/o,u=e*s/o}return new x(n-u,i+a)},setCoordinates:function(){if(1===arguments.length){var t=arguments[0];this.setCoordinates(t.p0,t.p1)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this.p0.x=e.x,this.p0.y=e.y,this.p1.x=n.x,this.p1.y=n.y}},segmentFraction:function(t){var e=this.projectionFactor(t);return e<0?e=0:(1<e||S.isNaN(e))&&(e=1),e},toString:function(){return"LINESTRING( "+this.p0.x+" "+this.p0.y+", "+this.p1.x+" "+this.p1.y+")"},isHorizontal:function(){return this.p0.y===this.p1.y},distance:function(){if(arguments[0]instanceof me){var t=arguments[0];return X.segmentToSegment(this.p0,this.p1,t.p0,t.p1)}if(arguments[0]instanceof x){var e=arguments[0];return X.pointToSegment(e,this.p0,this.p1)}},pointAlong:function(t){var e=new x;return e.x=this.p0.x+t*(this.p1.x-this.p0.x),e.y=this.p0.y+t*(this.p1.y-this.p0.y),e},hashCode:function(){var t=java.lang.Double.doubleToLongBits(this.p0.x);t^=31*java.lang.Double.doubleToLongBits(this.p0.y);var e=Math.trunc(t)^Math.trunc(t>>32),n=java.lang.Double.doubleToLongBits(this.p1.x);return n^=31*java.lang.Double.doubleToLongBits(this.p1.y),e^(Math.trunc(n)^Math.trunc(n>>32))},interfaces_:function(){return[n,l]},getClass:function(){return me}}),me.midPoint=function(t,e){return new x((t.x+e.x)/2,(t.y+e.y)/2)},me.serialVersionUID=0x2d2172135f411c00,e(ve.prototype,{interfaces_:function(){return[]},getClass:function(){return ve}}),ve.toLocationSymbol=function(t){switch(t){case ve.EXTERIOR:return"e";case ve.BOUNDARY:return"b";case ve.INTERIOR:return"i";case ve.NONE:return"-"}throw new c("Unknown location value: "+t)},ve.INTERIOR=0,ve.BOUNDARY=1,ve.EXTERIOR=2,ve.NONE=-1,e(ye.prototype,{isIntersects:function(){return!this.isDisjoint()},isCovers:function(){return(ye.isTrue(this._matrix[ve.INTERIOR][ve.INTERIOR])||ye.isTrue(this._matrix[ve.INTERIOR][ve.BOUNDARY])||ye.isTrue(this._matrix[ve.BOUNDARY][ve.INTERIOR])||ye.isTrue(this._matrix[ve.BOUNDARY][ve.BOUNDARY]))&&this._matrix[ve.EXTERIOR][ve.INTERIOR]===Nt.FALSE&&this._matrix[ve.EXTERIOR][ve.BOUNDARY]===Nt.FALSE},isCoveredBy:function(){return(ye.isTrue(this._matrix[ve.INTERIOR][ve.INTERIOR])||ye.isTrue(this._matrix[ve.INTERIOR][ve.BOUNDARY])||ye.isTrue(this._matrix[ve.BOUNDARY][ve.INTERIOR])||ye.isTrue(this._matrix[ve.BOUNDARY][ve.BOUNDARY]))&&this._matrix[ve.INTERIOR][ve.EXTERIOR]===Nt.FALSE&&this._matrix[ve.BOUNDARY][ve.EXTERIOR]===Nt.FALSE},set:function(){if(1===arguments.length)for(var t=arguments[0],e=0;e<t.length;e++){var n=Math.trunc(e/3),i=e%3;this._matrix[n][i]=Nt.toDimensionValue(t.charAt(e))}else if(3===arguments.length){var r=arguments[0],s=arguments[1],o=arguments[2];this._matrix[r][s]=o}},isContains:function(){return ye.isTrue(this._matrix[ve.INTERIOR][ve.INTERIOR])&&this._matrix[ve.EXTERIOR][ve.INTERIOR]===Nt.FALSE&&this._matrix[ve.EXTERIOR][ve.BOUNDARY]===Nt.FALSE},setAtLeast:function(){if(1===arguments.length)for(var t=arguments[0],e=0;e<t.length;e++){var n=Math.trunc(e/3),i=e%3;this.setAtLeast(n,i,Nt.toDimensionValue(t.charAt(e)))}else if(3===arguments.length){var r=arguments[0],s=arguments[1],o=arguments[2];this._matrix[r][s]<o&&(this._matrix[r][s]=o)}},setAtLeastIfValid:function(t,e,n){0<=t&&0<=e&&this.setAtLeast(t,e,n)},isWithin:function(){return ye.isTrue(this._matrix[ve.INTERIOR][ve.INTERIOR])&&this._matrix[ve.INTERIOR][ve.EXTERIOR]===Nt.FALSE&&this._matrix[ve.BOUNDARY][ve.EXTERIOR]===Nt.FALSE},isTouches:function(t,e){return e<t?this.isTouches(e,t):(t===Nt.A&&e===Nt.A||t===Nt.L&&e===Nt.L||t===Nt.L&&e===Nt.A||t===Nt.P&&e===Nt.A||t===Nt.P&&e===Nt.L)&&(this._matrix[ve.INTERIOR][ve.INTERIOR]===Nt.FALSE&&(ye.isTrue(this._matrix[ve.INTERIOR][ve.BOUNDARY])||ye.isTrue(this._matrix[ve.BOUNDARY][ve.INTERIOR])||ye.isTrue(this._matrix[ve.BOUNDARY][ve.BOUNDARY])))},isOverlaps:function(t,e){return t===Nt.P&&e===Nt.P||t===Nt.A&&e===Nt.A?ye.isTrue(this._matrix[ve.INTERIOR][ve.INTERIOR])&&ye.isTrue(this._matrix[ve.INTERIOR][ve.EXTERIOR])&&ye.isTrue(this._matrix[ve.EXTERIOR][ve.INTERIOR]):t===Nt.L&&e===Nt.L&&(1===this._matrix[ve.INTERIOR][ve.INTERIOR]&&ye.isTrue(this._matrix[ve.INTERIOR][ve.EXTERIOR])&&ye.isTrue(this._matrix[ve.EXTERIOR][ve.INTERIOR]))},isEquals:function(t,e){return t===e&&(ye.isTrue(this._matrix[ve.INTERIOR][ve.INTERIOR])&&this._matrix[ve.INTERIOR][ve.EXTERIOR]===Nt.FALSE&&this._matrix[ve.BOUNDARY][ve.EXTERIOR]===Nt.FALSE&&this._matrix[ve.EXTERIOR][ve.INTERIOR]===Nt.FALSE&&this._matrix[ve.EXTERIOR][ve.BOUNDARY]===Nt.FALSE)},toString:function(){for(var t=new Gt("123456789"),e=0;e<3;e++)for(var n=0;n<3;n++)t.setCharAt(3*e+n,Nt.toDimensionSymbol(this._matrix[e][n]));return t.toString()},setAll:function(t){for(var e=0;e<3;e++)for(var n=0;n<3;n++)this._matrix[e][n]=t},get:function(t,e){return this._matrix[t][e]},transpose:function(){var t=this._matrix[1][0];return this._matrix[1][0]=this._matrix[0][1],this._matrix[0][1]=t,t=this._matrix[2][0],this._matrix[2][0]=this._matrix[0][2],this._matrix[0][2]=t,t=this._matrix[2][1],this._matrix[2][1]=this._matrix[1][2],this._matrix[1][2]=t,this},matches:function(t){if(9!==t.length)throw new c("Should be length 9: "+t);for(var e=0;e<3;e++)for(var n=0;n<3;n++)if(!ye.matches(this._matrix[e][n],t.charAt(3*e+n)))return!1;return!0},add:function(t){for(var e=0;e<3;e++)for(var n=0;n<3;n++)this.setAtLeast(e,n,t.get(e,n))},isDisjoint:function(){return this._matrix[ve.INTERIOR][ve.INTERIOR]===Nt.FALSE&&this._matrix[ve.INTERIOR][ve.BOUNDARY]===Nt.FALSE&&this._matrix[ve.BOUNDARY][ve.INTERIOR]===Nt.FALSE&&this._matrix[ve.BOUNDARY][ve.BOUNDARY]===Nt.FALSE},isCrosses:function(t,e){return t===Nt.P&&e===Nt.L||t===Nt.P&&e===Nt.A||t===Nt.L&&e===Nt.A?ye.isTrue(this._matrix[ve.INTERIOR][ve.INTERIOR])&&ye.isTrue(this._matrix[ve.INTERIOR][ve.EXTERIOR]):t===Nt.L&&e===Nt.P||t===Nt.A&&e===Nt.P||t===Nt.A&&e===Nt.L?ye.isTrue(this._matrix[ve.INTERIOR][ve.INTERIOR])&&ye.isTrue(this._matrix[ve.EXTERIOR][ve.INTERIOR]):t===Nt.L&&e===Nt.L&&0===this._matrix[ve.INTERIOR][ve.INTERIOR]},interfaces_:function(){return[a]},getClass:function(){return ye}}),ye.matches=function(){if(Number.isInteger(arguments[0])&&"string"==typeof arguments[1]){var t=arguments[0],e=arguments[1];return e===Nt.SYM_DONTCARE||(e===Nt.SYM_TRUE&&(0<=t||t===Nt.TRUE)||(e===Nt.SYM_FALSE&&t===Nt.FALSE||(e===Nt.SYM_P&&t===Nt.P||(e===Nt.SYM_L&&t===Nt.L||e===Nt.SYM_A&&t===Nt.A))))}if("string"==typeof arguments[0]&&"string"==typeof arguments[1]){var n=arguments[0],i=arguments[1];return new ye(n).matches(i)}},ye.isTrue=function(t){return 0<=t||t===Nt.TRUE},e(xe.prototype,{interfaces_:function(){return[]},getClass:function(){return xe}}),xe.toDegrees=function(t){return 180*t/Math.PI},xe.normalize=function(t){for(;t>Math.PI;)t-=xe.PI_TIMES_2;for(;t<=-Math.PI;)t+=xe.PI_TIMES_2;return t},xe.angle=function(){if(1===arguments.length){var t=arguments[0];return Math.atan2(t.y,t.x)}if(2===arguments.length){var e=arguments[0],n=arguments[1],i=n.x-e.x,r=n.y-e.y;return Math.atan2(r,i)}},xe.isAcute=function(t,e,n){var i=t.x-e.x,r=t.y-e.y;return 0<i*(n.x-e.x)+r*(n.y-e.y)},xe.isObtuse=function(t,e,n){var i=t.x-e.x,r=t.y-e.y;return i*(n.x-e.x)+r*(n.y-e.y)<0},xe.interiorAngle=function(t,e,n){var i=xe.angle(e,t),r=xe.angle(e,n);return Math.abs(r-i)},xe.normalizePositive=function(t){if(t<0){for(;t<0;)t+=xe.PI_TIMES_2;xe.PI_TIMES_2<=t&&(t=0)}else{for(;xe.PI_TIMES_2<=t;)t-=xe.PI_TIMES_2;t<0&&(t=0)}return t},xe.angleBetween=function(t,e,n){var i=xe.angle(e,t),r=xe.angle(e,n);return xe.diff(i,r)},xe.diff=function(t,e){var n=null;return(n=t<e?e-t:t-e)>Math.PI&&(n=2*Math.PI-n),n},xe.toRadians=function(t){return t*Math.PI/180},xe.getTurn=function(t,e){var n=Math.sin(e-t);return 0<n?xe.COUNTERCLOCKWISE:n<0?xe.CLOCKWISE:xe.NONE},xe.angleBetweenOriented=function(t,e,n){var i=xe.angle(e,t),r=xe.angle(e,n)-i;return r<=-Math.PI?r+xe.PI_TIMES_2:r>Math.PI?r-xe.PI_TIMES_2:r},xe.PI_TIMES_2=2*Math.PI,xe.PI_OVER_2=Math.PI/2,xe.PI_OVER_4=Math.PI/4,xe.COUNTERCLOCKWISE=V.COUNTERCLOCKWISE,xe.CLOCKWISE=V.CLOCKWISE,xe.NONE=V.COLLINEAR,e(Ee.prototype,{area:function(){return Ee.area(this.p0,this.p1,this.p2)},signedArea:function(){return Ee.signedArea(this.p0,this.p1,this.p2)},interpolateZ:function(t){if(null===t)throw new c("Supplied point is null.");return Ee.interpolateZ(t,this.p0,this.p1,this.p2)},longestSideLength:function(){return Ee.longestSideLength(this.p0,this.p1,this.p2)},isAcute:function(){return Ee.isAcute(this.p0,this.p1,this.p2)},circumcentre:function(){return Ee.circumcentre(this.p0,this.p1,this.p2)},area3D:function(){return Ee.area3D(this.p0,this.p1,this.p2)},centroid:function(){return Ee.centroid(this.p0,this.p1,this.p2)},inCentre:function(){return Ee.inCentre(this.p0,this.p1,this.p2)},interfaces_:function(){return[]},getClass:function(){return Ee}}),Ee.area=function(t,e,n){return Math.abs(((n.x-t.x)*(e.y-t.y)-(e.x-t.x)*(n.y-t.y))/2)},Ee.signedArea=function(t,e,n){return((n.x-t.x)*(e.y-t.y)-(e.x-t.x)*(n.y-t.y))/2},Ee.det=function(t,e,n,i){return t*i-e*n},Ee.interpolateZ=function(t,e,n,i){var r=e.x,s=e.y,o=n.x-r,a=i.x-r,u=n.y-s,l=i.y-s,h=o*l-a*u,c=t.x-r,f=t.y-s,g=(l*c-a*f)/h,d=(-u*c+o*f)/h;return e.z+g*(n.z-e.z)+d*(i.z-e.z)},Ee.longestSideLength=function(t,e,n){var i=t.distance(e),r=e.distance(n),s=n.distance(t),o=i;return o<r&&(o=r),o<s&&(o=s),o},Ee.isAcute=function(t,e,n){return!!xe.isAcute(t,e,n)&&(!!xe.isAcute(e,n,t)&&!!xe.isAcute(n,t,e))},Ee.circumcentre=function(t,e,n){var i=n.x,r=n.y,s=t.x-i,o=t.y-r,a=e.x-i,u=e.y-r,l=2*Ee.det(s,o,a,u);return new x(i-Ee.det(o,s*s+o*o,u,a*a+u*u)/l,r+Ee.det(s,s*s+o*o,a,a*a+u*u)/l)},Ee.perpendicularBisector=function(t,e){var n=e.x-t.x,i=e.y-t.y;return new k(new k(t.x+n/2,t.y+i/2,1),new k(t.x-i+n/2,t.y+n+i/2,1))},Ee.angleBisector=function(t,e,n){var i=e.distance(t),r=i/(i+e.distance(n)),s=n.x-t.x,o=n.y-t.y;return new x(t.x+r*s,t.y+r*o)},Ee.area3D=function(t,e,n){var i=e.x-t.x,r=e.y-t.y,s=e.z-t.z,o=n.x-t.x,a=n.y-t.y,u=n.z-t.z,l=r*u-s*a,h=s*o-i*u,c=i*a-r*o,f=l*l+h*h+c*c;return Math.sqrt(f)/2},Ee.centroid=function(t,e,n){return new x((t.x+e.x+n.x)/3,(t.y+e.y+n.y)/3)},Ee.inCentre=function(t,e,n){var i=e.distance(n),r=t.distance(n),s=t.distance(e),o=i+r+s;return new x((i*t.x+r*e.x+s*n.x)/o,(i*t.y+r*e.y+s*n.y)/o)};var Ie=Object.freeze({Coordinate:x,CoordinateList:b,Envelope:M,LineSegment:me,GeometryFactory:le,Geometry:K,Point:Vt,LineString:Bt,LinearRing:Ht,Polygon:Ut,GeometryCollection:Lt,MultiPoint:Xt,MultiLineString:wt,MultiPolygon:Wt,Dimension:Nt,IntersectionMatrix:ye,PrecisionModel:ae,Location:ve,Triangle:Ee});function Ne(){this._pt=[new x,new x],this._distance=S.NaN,this._isNull=!0}function Ce(){}function Se(){this._g0=null,this._g1=null,this._ptDist=new Ne;var t=arguments[this._densifyFrac=0],e=arguments[1];this._g0=t,this._g1=e}function Le(){this._maxPtDist=new Ne,this._minPtDist=new Ne,this._euclideanDist=new Ce,this._geom=null;var t=arguments[0];this._geom=t}function we(){this._maxPtDist=new Ne,this._minPtDist=new Ne,this._geom=null;var t=arguments[this._numSubSegs=0],e=arguments[1];this._geom=t,this._numSubSegs=Math.trunc(Math.round(1/e))}e(Ne.prototype,{getCoordinates:function(){return this._pt},getCoordinate:function(t){return this._pt[t]},setMinimum:function(){if(1===arguments.length){var t=arguments[0];this.setMinimum(t._pt[0],t._pt[1])}else if(2===arguments.length){var e=arguments[0],n=arguments[1];if(this._isNull)return this.initialize(e,n),null;var i=e.distance(n);i<this._distance&&this.initialize(e,n,i)}},initialize:function(){if(0===arguments.length)this._isNull=!0;else if(2===arguments.length){var t=arguments[0],e=arguments[1];this._pt[0].setCoordinate(t),this._pt[1].setCoordinate(e),this._distance=t.distance(e),this._isNull=!1}else if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];this._pt[0].setCoordinate(n),this._pt[1].setCoordinate(i),this._distance=r,this._isNull=!1}},toString:function(){return de.toLineString(this._pt[0],this._pt[1])},getDistance:function(){return this._distance},setMaximum:function(){if(1===arguments.length){var t=arguments[0];this.setMaximum(t._pt[0],t._pt[1])}else if(2===arguments.length){var e=arguments[0],n=arguments[1];if(this._isNull)return this.initialize(e,n),null;var i=e.distance(n);i>this._distance&&this.initialize(e,n,i)}},interfaces_:function(){return[]},getClass:function(){return Ne}}),e(Ce.prototype,{interfaces_:function(){return[]},getClass:function(){return Ce}}),Ce.computeDistance=function(){if(arguments[2]instanceof Ne&&arguments[0]instanceof Bt&&arguments[1]instanceof x)for(var t=arguments[0],e=arguments[1],n=arguments[2],i=new me,r=t.getCoordinates(),s=0;s<r.length-1;s++){i.setCoordinates(r[s],r[s+1]);var o=i.closestPoint(e);n.setMinimum(o,e)}else if(arguments[2]instanceof Ne&&arguments[0]instanceof Ut&&arguments[1]instanceof x){var a=arguments[0],u=arguments[1],l=arguments[2];Ce.computeDistance(a.getExteriorRing(),u,l);for(s=0;s<a.getNumInteriorRing();s++)Ce.computeDistance(a.getInteriorRingN(s),u,l)}else if(arguments[2]instanceof Ne&&arguments[0]instanceof K&&arguments[1]instanceof x){var h=arguments[0],c=arguments[1],f=arguments[2];if(h instanceof Bt)Ce.computeDistance(h,c,f);else if(h instanceof Ut)Ce.computeDistance(h,c,f);else if(h instanceof Lt){var g=h;for(s=0;s<g.getNumGeometries();s++){var d=g.getGeometryN(s);Ce.computeDistance(d,c,f)}}else f.setMinimum(h.getCoordinate(),c)}else if(arguments[2]instanceof Ne&&arguments[0]instanceof me&&arguments[1]instanceof x){var _=arguments[0],p=arguments[1],m=arguments[2];o=_.closestPoint(p);m.setMinimum(o,p)}},e(Se.prototype,{getCoordinates:function(){return this._ptDist.getCoordinates()},setDensifyFraction:function(t){if(1<t||t<=0)throw new c("Fraction is not in range (0.0 - 1.0]");this._densifyFrac=t},compute:function(t,e){this.computeOrientedDistance(t,e,this._ptDist),this.computeOrientedDistance(e,t,this._ptDist)},distance:function(){return this.compute(this._g0,this._g1),this._ptDist.getDistance()},computeOrientedDistance:function(t,e,n){var i=new Le(e);if(t.apply(i),n.setMaximum(i.getMaxPointDistance()),0<this._densifyFrac){var r=new we(e,this._densifyFrac);t.apply(r),n.setMaximum(r.getMaxPointDistance())}},orientedDistance:function(){return this.computeOrientedDistance(this._g0,this._g1,this._ptDist),this._ptDist.getDistance()},interfaces_:function(){return[]},getClass:function(){return Se}}),Se.distance=function(){if(2===arguments.length)return(t=new Se(arguments[0],arguments[1])).distance();if(3===arguments.length){var t,e=arguments[0],n=arguments[1],i=arguments[2];return(t=new Se(e,n)).setDensifyFraction(i),t.distance()}},e(Le.prototype,{filter:function(t){this._minPtDist.initialize(),Ce.computeDistance(this._geom,t,this._minPtDist),this._maxPtDist.setMaximum(this._minPtDist)},getMaxPointDistance:function(){return this._maxPtDist},interfaces_:function(){return[Z]},getClass:function(){return Le}}),e(we.prototype,{filter:function(t,e){if(0===e)return null;for(var n=t.getCoordinate(e-1),i=t.getCoordinate(e),r=(i.x-n.x)/this._numSubSegs,s=(i.y-n.y)/this._numSubSegs,o=0;o<this._numSubSegs;o++){var a=new x(n.x+o*r,n.y+o*s);this._minPtDist.initialize(),Ce.computeDistance(this._geom,a,this._minPtDist),this._maxPtDist.setMaximum(this._minPtDist)}},isDone:function(){return!1},isGeometryChanged:function(){return!1},getMaxPointDistance:function(){return this._maxPtDist},interfaces_:function(){return[St]},getClass:function(){return we}}),Se.MaxPointDistanceFilter=Le,Se.MaxDensifiedByFractionDistanceFilter=we;var Re=Object.freeze({DiscreteHausdorffDistance:Se,DistanceToPoint:Ce,PointPairDistance:Ne});function Te(){}function Pe(){}function Oe(){this._min=S.POSITIVE_INFINITY,this._max=S.NEGATIVE_INFINITY}function be(){}function Me(){Oe.apply(this),this._item=null;var t=arguments[0],e=arguments[1],n=arguments[2];this._min=t,this._max=e,this._item=n}e(Te.prototype,{visitItem:function(t){},interfaces_:function(){return[]},getClass:function(){return Te}}),e(Pe.prototype,{locate:function(t){},interfaces_:function(){return[]},getClass:function(){return Pe}}),e(Oe.prototype,{getMin:function(){return this._min},intersects:function(t,e){return!(this._min>e||this._max<t)},getMax:function(){return this._max},toString:function(){return de.toLineString(new x(this._min,0),new x(this._max,0))},interfaces_:function(){return[]},getClass:function(){return Oe}}),e(be.prototype,{compare:function(t,e){var n=t,i=e,r=(n._min+n._max)/2,s=(i._min+i._max)/2;return r<s?-1:s<r?1:0},interfaces_:function(){return[u]},getClass:function(){return be}}),Oe.NodeComparator=be,p(Me,Oe),e(Me.prototype,{query:function(t,e,n){if(!this.intersects(t,e))return null;n.visitItem(this._item)},interfaces_:function(){return[]},getClass:function(){return Me}});var De={reverseOrder:function(){return{compare:function(t,e){return e.compareTo(t)}}},min:function(t){return De.sort(t),t.get(0)},sort:function(t,e){var n=t.toArray();e?It.sort(n,e):It.sort(n);for(var i=t.iterator(),r=0,s=n.length;r<s;r++)i.next(),i.set(n[r])},singletonList:function(t){var e=new P;return e.add(t),e}};function Ae(){Oe.apply(this),this._node1=null,this._node2=null;var t=arguments[0],e=arguments[1];this._node1=t,this._node2=e,this.buildExtent(this._node1,this._node2)}function Fe(){this._leaves=new P,this._root=null,this._level=0}function Ge(){if(this._lines=null,this._isForcedToLineString=!1,1===arguments.length){var t=arguments[0];this._lines=t}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this._lines=e,this._isForcedToLineString=n}}function qe(){this._items=new P}function Be(){this._p=null,this._crossingCount=0,this._isPointOnSegment=!1;var t=arguments[0];this._p=t}function ze(){this._index=null;var t=arguments[0];if(!(L(t,kt)||t instanceof Ht))throw new c("Argument must be Polygonal or LinearRing");this._index=new Ye(t)}function Ve(){this._counter=null;var t=arguments[0];this._counter=t}function Ye(){this._index=new Fe;var t=arguments[0];this.init(t)}function ke(){}function Ue(){this._parent=null,this._atStart=null,this._max=null,this._index=null,this._subcollectionIterator=null;var t=arguments[0];this._parent=t,this._atStart=!0,this._index=0,this._max=t.getNumGeometries()}function Xe(){this._geom=null;var t=arguments[0];this._geom=t}p(Ae,Oe),e(Ae.prototype,{buildExtent:function(t,e){this._min=Math.min(t._min,e._min),this._max=Math.max(t._max,e._max)},query:function(t,e,n){if(!this.intersects(t,e))return null;null!==this._node1&&this._node1.query(t,e,n),null!==this._node2&&this._node2.query(t,e,n)},interfaces_:function(){return[]},getClass:function(){return Ae}}),e(Fe.prototype,{buildTree:function(){De.sort(this._leaves,new Oe.NodeComparator);for(var t=this._leaves,e=null,n=new P;;){if(this.buildLevel(t,n),1===n.size())return n.get(0);e=t,t=n,n=e}},insert:function(t,e,n){if(null!==this._root)throw new IllegalStateException("Index cannot be added to once it has been queried");this._leaves.add(new Me(t,e,n))},query:function(t,e,n){this.init(),this._root.query(t,e,n)},buildRoot:function(){if(null!==this._root)return null;this._root=this.buildTree()},printNode:function(t){Y.out.println(de.toLineString(new x(t._min,this._level),new x(t._max,this._level)))},init:function(){if(null!==this._root)return null;this.buildRoot()},buildLevel:function(t,e){this._level++,e.clear();for(var n=0;n<t.size();n+=2){var i=t.get(n);if(null===(n+1<t.size()?t.get(n):null))e.add(i);else{var r=new Ae(t.get(n),t.get(n+1));e.add(r)}}},interfaces_:function(){return[]},getClass:function(){return Fe}}),e(Ge.prototype,{filter:function(t){if(this._isForcedToLineString&&t instanceof Ht){var e=t.getFactory().createLineString(t.getCoordinateSequence());return this._lines.add(e),null}t instanceof Bt&&this._lines.add(t)},setForceToLineString:function(t){this._isForcedToLineString=t},interfaces_:function(){return[j]},getClass:function(){return Ge}}),Ge.getGeometry=function(){if(1===arguments.length){var t=arguments[0];return t.getFactory().buildGeometry(Ge.getLines(t))}if(2===arguments.length){var e=arguments[0],n=arguments[1];return e.getFactory().buildGeometry(Ge.getLines(e,n))}},Ge.getLines=function(){if(1===arguments.length){var t=arguments[0];return Ge.getLines(t,!1)}if(2===arguments.length){if(L(arguments[0],N)&&L(arguments[1],N)){for(var e=arguments[0],n=arguments[1],i=e.iterator();i.hasNext();){var r=i.next();Ge.getLines(r,n)}return n}if(arguments[0]instanceof K&&"boolean"==typeof arguments[1]){var s=arguments[0],o=arguments[1],a=new P;return s.apply(new Ge(a,o)),a}if(arguments[0]instanceof K&&L(arguments[1],N)){var u=arguments[0],l=arguments[1];return u instanceof Bt?l.add(u):u.apply(new Ge(l)),l}}else if(3===arguments.length){if("boolean"==typeof arguments[2]&&L(arguments[0],N)&&L(arguments[1],N)){var h=arguments[0],c=arguments[1],f=arguments[2];for(i=h.iterator();i.hasNext();){r=i.next();Ge.getLines(r,c,f)}return c}if("boolean"==typeof arguments[2]&&arguments[0]instanceof K&&L(arguments[1],N)){var g=arguments[0],d=arguments[1],_=arguments[2];return g.apply(new Ge(d,_)),d}}},e(qe.prototype,{visitItem:function(t){this._items.add(t)},getItems:function(){return this._items},interfaces_:function(){return[Te]},getClass:function(){return qe}}),e(Be.prototype,{countSegment:function(t,e){if(t.x<this._p.x&&e.x<this._p.x)return null;if(this._p.x===e.x&&this._p.y===e.y)return this._isPointOnSegment=!0,null;if(t.y===this._p.y&&e.y===this._p.y){var n=t.x,i=e.x;return i<n&&(n=e.x,i=t.x),this._p.x>=n&&this._p.x<=i&&(this._isPointOnSegment=!0),null}if(t.y>this._p.y&&e.y<=this._p.y||e.y>this._p.y&&t.y<=this._p.y){var r=V.index(t,e,this._p);if(r===V.COLLINEAR)return this._isPointOnSegment=!0,null;e.y<t.y&&(r=-r),r===V.LEFT&&this._crossingCount++}},isPointInPolygon:function(){return this.getLocation()!==ve.EXTERIOR},getLocation:function(){return this._isPointOnSegment?ve.BOUNDARY:this._crossingCount%2==1?ve.INTERIOR:ve.EXTERIOR},isOnSegment:function(){return this._isPointOnSegment},interfaces_:function(){return[]},getClass:function(){return Be}}),Be.locatePointInRing=function(){if(arguments[0]instanceof x&&L(arguments[1],H)){for(var t=arguments[0],e=arguments[1],n=new Be(t),i=new x,r=new x,s=1;s<e.size();s++)if(e.getCoordinate(s,i),e.getCoordinate(s-1,r),n.countSegment(i,r),n.isOnSegment())return n.getLocation();return n.getLocation()}if(arguments[0]instanceof x&&arguments[1]instanceof Array){var o=arguments[0],a=arguments[1];for(n=new Be(o),s=1;s<a.length;s++){i=a[s],r=a[s-1];if(n.countSegment(i,r),n.isOnSegment())return n.getLocation()}return n.getLocation()}},e(ze.prototype,{locate:function(t){var e=new Be(t),n=new Ve(e);return this._index.query(t.y,t.y,n),e.getLocation()},interfaces_:function(){return[Pe]},getClass:function(){return ze}}),e(Ve.prototype,{visitItem:function(t){var e=t;this._counter.countSegment(e.getCoordinate(0),e.getCoordinate(1))},interfaces_:function(){return[Te]},getClass:function(){return Ve}}),e(Ye.prototype,{init:function(t){for(var e=Ge.getLines(t).iterator();e.hasNext();){var n=e.next().getCoordinates();this.addLine(n)}},addLine:function(t){for(var e=1;e<t.length;e++){var n=new me(t[e-1],t[e]),i=Math.min(n.p0.y,n.p1.y),r=Math.max(n.p0.y,n.p1.y);this._index.insert(i,r,n)}},query:function(){if(2===arguments.length){var t=arguments[0],e=arguments[1],n=new qe;return this._index.query(t,e,n),n.getItems()}if(3===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2];this._index.query(i,r,s)}},interfaces_:function(){return[]},getClass:function(){return Ye}}),ze.SegmentVisitor=Ve,ze.IntervalIndexedGeometry=Ye,e(ke.prototype,{interfaces_:function(){return[]},getClass:function(){return ke}}),ke.isOnLine=function(){if(arguments[0]instanceof x&&L(arguments[1],H)){for(var t=arguments[0],e=arguments[1],n=new pe,i=new x,r=new x,s=e.size(),o=1;o<s;o++)if(e.getCoordinate(o-1,i),e.getCoordinate(o,r),n.computeIntersection(t,i,r),n.hasIntersection())return!0;return!1}if(arguments[0]instanceof x&&arguments[1]instanceof Array){var a=arguments[0],u=arguments[1];for(n=new pe,o=1;o<u.length;o++){i=u[o-1],r=u[o];if(n.computeIntersection(a,i,r),n.hasIntersection())return!0}return!1}},ke.locateInRing=function(t,e){return Be.locatePointInRing(t,e)},ke.isInRing=function(t,e){return ke.locateInRing(t,e)!==ve.EXTERIOR},e(Ue.prototype,{next:function(){if(this._atStart)return this._atStart=!1,Ue.isAtomic(this._parent)&&this._index++,this._parent;if(null!==this._subcollectionIterator){if(this._subcollectionIterator.hasNext())return this._subcollectionIterator.next();this._subcollectionIterator=null}if(this._index>=this._max)throw new R;var t=this._parent.getGeometryN(this._index++);return t instanceof Lt?(this._subcollectionIterator=new Ue(t),this._subcollectionIterator.next()):t},remove:function(){throw new UnsupportedOperationException(this.getClass().getName())},hasNext:function(){if(this._atStart)return!0;if(null!==this._subcollectionIterator){if(this._subcollectionIterator.hasNext())return!0;this._subcollectionIterator=null}return!(this._index>=this._max)},interfaces_:function(){return[I]},getClass:function(){return Ue}}),Ue.isAtomic=function(t){return!(t instanceof Lt)},e(Xe.prototype,{locate:function(t){return Xe.locate(t,this._geom)},interfaces_:function(){return[Pe]},getClass:function(){return Xe}}),Xe.locatePointInPolygon=function(t,e){if(e.isEmpty())return ve.EXTERIOR;var n=e.getExteriorRing(),i=Xe.locatePointInRing(t,n);if(i!==ve.INTERIOR)return i;for(var r=0;r<e.getNumInteriorRing();r++){var s=e.getInteriorRingN(r),o=Xe.locatePointInRing(t,s);if(o===ve.BOUNDARY)return ve.BOUNDARY;if(o===ve.INTERIOR)return ve.EXTERIOR}return ve.INTERIOR},Xe.locatePointInRing=function(t,e){return e.getEnvelopeInternal().intersects(t)?ke.locateInRing(t,e.getCoordinates()):ve.EXTERIOR},Xe.containsPointInPolygon=function(t,e){return ve.EXTERIOR!==Xe.locatePointInPolygon(t,e)},Xe.locateInGeometry=function(t,e){if(e instanceof Ut)return Xe.locatePointInPolygon(t,e);if(e instanceof Lt)for(var n=new Ue(e);n.hasNext();){var i=n.next();if(i!==e){var r=Xe.locateInGeometry(t,i);if(r!==ve.EXTERIOR)return r}}return ve.EXTERIOR},Xe.locate=function(t,e){return e.isEmpty()?ve.EXTERIOR:Xe.locateInGeometry(t,e)};var He=Object.freeze({IndexedPointInAreaLocator:ze,PointOnGeometryLocator:Pe,SimplePointInAreaLocator:Xe});function We(){}function je(){}function Ke(){}function Ze(){}e(We.prototype,{measure:function(t,e){},interfaces_:function(){return[]},getClass:function(){return We}}),e(je.prototype,{measure:function(t,e){return t.intersection(e).getArea()/t.union(e).getArea()},interfaces_:function(){return[We]},getClass:function(){return je}}),e(Ke.prototype,{measure:function(t,e){var n=Se.distance(t,e,Ke.DENSIFY_FRACTION),i=new M(t.getEnvelopeInternal());i.expandToInclude(e.getEnvelopeInternal());var r=1-n/Ke.diagonalSize(i);return r},interfaces_:function(){return[We]},getClass:function(){return Ke}}),Ke.diagonalSize=function(t){if(t.isNull())return 0;var e=t.getWidth(),n=t.getHeight();return Math.sqrt(e*e+n*n)},Ke.DENSIFY_FRACTION=.25,e(Ze.prototype,{interfaces_:function(){return[]},getClass:function(){return Ze}}),Ze.combine=function(t,e){return Math.min(t,e)};var Qe=Object.freeze({AreaSimilarityMeasure:je,HausdorffSimilarityMeasure:Ke,SimilarityMeasure:We,SimilarityMeasureCombiner:Ze});function Je(){this._areaBasePt=null,this._triangleCent3=new x,this._areasum2=0,this._cg3=new x,this._lineCentSum=new x,this._totalLength=0,this._ptCount=0,this._ptCentSum=new x;var t=arguments[0];this._areaBasePt=null,this.add(t)}function $e(){}function tn(t){this.message=t||""}function en(){this.array_=[]}function nn(){this.treeSet=new yt,this.list=new P}function rn(){if(this._geomFactory=null,this._inputPts=null,1===arguments.length){var t=arguments[0];rn.call(this,rn.extractCoordinates(t),t.getFactory())}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this._inputPts=nn.filterCoordinates(e),this._geomFactory=n}}function sn(){this._origin=null;var t=arguments[0];this._origin=t}function on(){this._factory=null,this._interiorPoint=null;var t=arguments[this._maxWidth=0];this._factory=t.getFactory(),this.add(t)}function an(){this._poly=null,this._centreY=null,this._hiY=S.MAX_VALUE,this._loY=-S.MAX_VALUE;var t=arguments[0];this._poly=t,this._hiY=t.getEnvelopeInternal().getMaxY(),this._loY=t.getEnvelopeInternal().getMinY(),this._centreY=on.avg(this._loY,this._hiY)}function un(){this._centroid=null,this._minDistance=S.MAX_VALUE,this._interiorPoint=null;var t=arguments[0];this._centroid=t.getCentroid().getCoordinate(),this.addInterior(t),null===this._interiorPoint&&this.addEndpoints(t)}function ln(){this._centroid=null,this._minDistance=S.MAX_VALUE,this._interiorPoint=null;var t=arguments[0];this._centroid=t.getCentroid().getCoordinate(),this.add(t)}function hn(){this.selectedSegment=new me}function cn(){this._items=new P,this._subnode=new Array(2).fill(null)}function fn(){if(this.min=null,this.max=null,0===arguments.length)this.min=0,this.max=0;else if(1===arguments.length){var t=arguments[0];this.init(t.min,t.max)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this.init(e,n)}}function gn(){}function dn(){this._pt=0,this._level=0,this._interval=null;var t=arguments[0];this.computeKey(t)}function _n(){cn.apply(this),this._interval=null,this._centre=null,this._level=null;var t=arguments[0],e=arguments[1];this._interval=t,this._level=e,this._centre=(t.getMin()+t.getMax())/2}function pn(){}function mn(){cn.apply(this)}function vn(){this._root=null,this._minExtent=1,this._root=new mn}function yn(){this._pts=null,this._start=null,this._end=null,this._env=null,this._context=null,this._id=null;var t=arguments[0],e=arguments[1],n=arguments[2],i=arguments[3];this._pts=t,this._start=e,this._end=n,this._context=i}function xn(){}function En(){}function In(){}function Nn(){}function Cn(){this._ring=null,this._tree=null,this._crossings=0,this._interval=new fn;var t=arguments[0];this._ring=t,this.buildIndex()}function Sn(){hn.apply(this),this.mcp=null,this.p=null;var t=arguments[0],e=arguments[1];this.mcp=t,this.p=e}function Ln(){this._input=null,this._extremalPts=null,this._centre=null;var t=arguments[this._radius=0];this._input=t}function wn(){if(this._inputGeom=null,this._isConvex=null,this._convexHullPts=null,this._minBaseSeg=new me,this._minWidthPt=null,this._minPtIndex=null,this._minWidth=0,1===arguments.length){var t=arguments[0];wn.call(this,t,!1)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this._inputGeom=e,this._isConvex=n}}e(Je.prototype,{setAreaBasePoint:function(t){this._areaBasePt=t},addPoint:function(t){this._ptCount+=1,this._ptCentSum.x+=t.x,this._ptCentSum.y+=t.y},addLineSegments:function(t){for(var e=0,n=0;n<t.length-1;n++){var i=t[n].distance(t[n+1]);if(0!==i){e+=i;var r=(t[n].x+t[n+1].x)/2;this._lineCentSum.x+=i*r;var s=(t[n].y+t[n+1].y)/2;this._lineCentSum.y+=i*s}}this._totalLength+=e,0===e&&0<t.length&&this.addPoint(t[0])},addHole:function(t){for(var e=V.isCCW(t),n=0;n<t.length-1;n++)this.addTriangle(this._areaBasePt,t[n],t[n+1],e);this.addLineSegments(t)},getCentroid:function(){var t=new x;if(0<Math.abs(this._areasum2))t.x=this._cg3.x/3/this._areasum2,t.y=this._cg3.y/3/this._areasum2;else if(0<this._totalLength)t.x=this._lineCentSum.x/this._totalLength,t.y=this._lineCentSum.y/this._totalLength;else{if(!(0<this._ptCount))return null;t.x=this._ptCentSum.x/this._ptCount,t.y=this._ptCentSum.y/this._ptCount}return t},addShell:function(t){0<t.length&&this.setAreaBasePoint(t[0]);for(var e=!V.isCCW(t),n=0;n<t.length-1;n++)this.addTriangle(this._areaBasePt,t[n],t[n+1],e);this.addLineSegments(t)},addTriangle:function(t,e,n,i){var r=i?1:-1;Je.centroid3(t,e,n,this._triangleCent3);var s=Je.area2(t,e,n);this._cg3.x+=r*s*this._triangleCent3.x,this._cg3.y+=r*s*this._triangleCent3.y,this._areasum2+=r*s},add:function(){if(arguments[0]instanceof Ut){var t=arguments[0];this.addShell(t.getExteriorRing().getCoordinates());for(var e=0;e<t.getNumInteriorRing();e++)this.addHole(t.getInteriorRingN(e).getCoordinates())}else if(arguments[0]instanceof K){var n=arguments[0];if(n.isEmpty())return null;if(n instanceof Vt)this.addPoint(n.getCoordinate());else if(n instanceof Bt)this.addLineSegments(n.getCoordinates());else if(n instanceof Ut){var i=n;this.add(i)}else if(n instanceof Lt){var r=n;for(e=0;e<r.getNumGeometries();e++)this.add(r.getGeometryN(e))}}},interfaces_:function(){return[]},getClass:function(){return Je}}),Je.area2=function(t,e,n){return(e.x-t.x)*(n.y-t.y)-(n.x-t.x)*(e.y-t.y)},Je.centroid3=function(t,e,n,i){return i.x=t.x+e.x+n.x,i.y=t.y+e.y+n.y,null},Je.getCentroid=function(t){return new Je(t).getCentroid()},e($e.prototype,{interfaces_:function(){return[]},getClass:function(){return $e}}),$e.orientationIndex=function(t,e,n){return z.orientationIndex(t,e,n)},$e.signedArea=function(){if(arguments[0]instanceof Array){var t=arguments[0];if(t.length<3)return 0;for(var e=0,n=t[0].x,i=1;i<t.length-1;i++){var r=t[i].x-n,s=t[i+1].y;e+=r*(t[i-1].y-s)}return e/2}if(L(arguments[0],H)){var o=arguments[0],a=o.size();if(a<3)return 0;var u=new x,l=new x,h=new x;o.getCoordinate(0,l),o.getCoordinate(1,h);n=l.x;h.x-=n;for(e=0,i=1;i<a-1;i++)u.y=l.y,l.x=h.x,l.y=h.y,o.getCoordinate(i+1,h),h.x-=n,e+=l.x*(u.y-h.y);return e/2}},$e.distanceLineLine=function(t,e,n,i){if(t.equals(e))return $e.distancePointLine(t,n,i);if(n.equals(i))return $e.distancePointLine(i,t,e);var r=!1;if(M.intersects(t,e,n,i)){var s=(e.x-t.x)*(i.y-n.y)-(e.y-t.y)*(i.x-n.x);if(0===s)r=!0;else{var o=(t.y-n.y)*(i.x-n.x)-(t.x-n.x)*(i.y-n.y),a=((t.y-n.y)*(e.x-t.x)-(t.x-n.x)*(e.y-t.y))/s,u=o/s;(u<0||1<u||a<0||1<a)&&(r=!0)}}else r=!0;return r?U.min($e.distancePointLine(t,n,i),$e.distancePointLine(e,n,i),$e.distancePointLine(n,t,e),$e.distancePointLine(i,t,e)):0},$e.isPointInRing=function(t,e){return $e.locatePointInRing(t,e)!==ve.EXTERIOR},$e.computeLength=function(t){var e=t.size();if(e<=1)return 0;var n=0,i=new x;t.getCoordinate(0,i);for(var r=i.x,s=i.y,o=1;o<e;o++){t.getCoordinate(o,i);var a=i.x,u=i.y,l=a-r,h=u-s;n+=Math.sqrt(l*l+h*h),r=a,s=u}return n},$e.isCCW=function(t){var e=t.length-1;if(e<3)throw new c("Ring has fewer than 4 points, so orientation cannot be determined");for(var n=t[0],i=0,r=1;r<=e;r++){var s=t[r];s.y>n.y&&(n=s,i=r)}for(var o=i;(o-=1)<0&&(o=e),t[o].equals2D(n)&&o!==i;);for(var a=i;t[a=(a+1)%e].equals2D(n)&&a!==i;);var u=t[o],l=t[a];if(u.equals2D(n)||l.equals2D(n)||u.equals2D(l))return!1;var h=$e.computeOrientation(u,n,l);return 0===h?u.x>l.x:0<h},$e.locatePointInRing=function(t,e){return Be.locatePointInRing(t,e)},$e.distancePointLinePerpendicular=function(t,e,n){var i=(n.x-e.x)*(n.x-e.x)+(n.y-e.y)*(n.y-e.y),r=((e.y-t.y)*(n.x-e.x)-(e.x-t.x)*(n.y-e.y))/i;return Math.abs(r)*Math.sqrt(i)},$e.computeOrientation=function(t,e,n){return $e.orientationIndex(t,e,n)},$e.distancePointLine=function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];if(0===e.length)throw new c("Line array must contain at least one vertex");for(var n=t.distance(e[0]),i=0;i<e.length-1;i++){var r=$e.distancePointLine(t,e[i],e[i+1]);r<n&&(n=r)}return n}if(3===arguments.length){var s=arguments[0],o=arguments[1],a=arguments[2];if(o.x===a.x&&o.y===a.y)return s.distance(o);var u=(a.x-o.x)*(a.x-o.x)+(a.y-o.y)*(a.y-o.y),l=((s.x-o.x)*(a.x-o.x)+(s.y-o.y)*(a.y-o.y))/u;if(l<=0)return s.distance(o);if(1<=l)return s.distance(a);var h=((o.y-s.y)*(a.x-o.x)-(o.x-s.x)*(a.y-o.y))/u;return Math.abs(h)*Math.sqrt(u)}},$e.isOnLine=function(t,e){for(var n=new pe,i=1;i<e.length;i++){var r=e[i-1],s=e[i];if(n.computeIntersection(t,r,s),n.hasIntersection())return!0}return!1},$e.RIGHT=$e.CLOCKWISE=-1,$e.LEFT=$e.COUNTERCLOCKWISE=1,$e.STRAIGHT=$e.COLLINEAR=0,(tn.prototype=new Error).name="EmptyStackException",(en.prototype=new w).add=function(t){return this.array_.push(t),!0},en.prototype.get=function(t){if(t<0||t>=this.size())throw new IndexOutOfBoundsException;return this.array_[t]},en.prototype.push=function(t){return this.array_.push(t),t},en.prototype.pop=function(t){if(0===this.array_.length)throw new tn;return this.array_.pop()},en.prototype.peek=function(){if(0===this.array_.length)throw new tn;return this.array_[this.array_.length-1]},en.prototype.empty=function(){return 0===this.array_.length},en.prototype.isEmpty=function(){return this.empty()},en.prototype.search=function(t){return this.array_.indexOf(t)},en.prototype.size=function(){return this.array_.length},en.prototype.toArray=function(){for(var t=[],e=0,n=this.array_.length;e<n;e++)t.push(this.array_[e]);return t},e(nn.prototype,{filter:function(t){this.treeSet.contains(t)||(this.list.add(t),this.treeSet.add(t))},getCoordinates:function(){var t=new Array(this.list.size()).fill(null);return this.list.toArray(t)},interfaces_:function(){return[Z]},getClass:function(){return nn}}),nn.filterCoordinates=function(t){for(var e=new nn,n=0;n<t.length;n++)e.filter(t[n]);return e.getCoordinates()},e(rn.prototype,{preSort:function(t){for(var e=null,n=1;n<t.length;n++)(t[n].y<t[0].y||t[n].y===t[0].y&&t[n].x<t[0].x)&&(e=t[0],t[0]=t[n],t[n]=e);return It.sort(t,1,t.length,new sn(t[0])),t},computeOctRing:function(t){var e=this.computeOctPts(t),n=new b;return n.add(e,!1),n.size()<3?null:(n.closeRing(),n.toCoordinateArray())},lineOrPolygon:function(t){if(3===(t=this.cleanRing(t)).length)return this._geomFactory.createLineString([t[0],t[1]]);var e=this._geomFactory.createLinearRing(t);return this._geomFactory.createPolygon(e)},cleanRing:function(t){y.equals(t[0],t[t.length-1]);for(var e=new P,n=null,i=0;i<=t.length-2;i++){var r=t[i],s=t[i+1];r.equals(s)||(null!==n&&this.isBetween(n,r,s)||(e.add(r),n=r))}e.add(t[t.length-1]);var o=new Array(e.size()).fill(null);return e.toArray(o)},isBetween:function(t,e,n){if(0!==V.index(t,e,n))return!1;if(t.x!==n.x){if(t.x<=e.x&&e.x<=n.x)return!0;if(n.x<=e.x&&e.x<=t.x)return!0}if(t.y!==n.y){if(t.y<=e.y&&e.y<=n.y)return!0;if(n.y<=e.y&&e.y<=t.y)return!0}return!1},reduce:function(t){var e=this.computeOctRing(t);if(null===e)return t;for(var n=new yt,i=0;i<e.length;i++)n.add(e[i]);for(i=0;i<t.length;i++)ke.isInRing(t[i],e)||n.add(t[i]);var r=nt.toCoordinateArray(n);return r.length<3?this.padArray3(r):r},getConvexHull:function(){if(0===this._inputPts.length)return this._geomFactory.createGeometryCollection();if(1===this._inputPts.length)return this._geomFactory.createPoint(this._inputPts[0]);if(2===this._inputPts.length)return this._geomFactory.createLineString(this._inputPts);var t=this._inputPts;50<this._inputPts.length&&(t=this.reduce(this._inputPts));var e=this.preSort(t),n=this.grahamScan(e),i=this.toCoordinateArray(n);return this.lineOrPolygon(i)},padArray3:function(t){for(var e=new Array(3).fill(null),n=0;n<e.length;n++)n<t.length?e[n]=t[n]:e[n]=t[0];return e},computeOctPts:function(t){for(var e=new Array(8).fill(null),n=0;n<e.length;n++)e[n]=t[0];for(var i=1;i<t.length;i++)t[i].x<e[0].x&&(e[0]=t[i]),t[i].x-t[i].y<e[1].x-e[1].y&&(e[1]=t[i]),t[i].y>e[2].y&&(e[2]=t[i]),t[i].x+t[i].y>e[3].x+e[3].y&&(e[3]=t[i]),t[i].x>e[4].x&&(e[4]=t[i]),t[i].x-t[i].y>e[5].x-e[5].y&&(e[5]=t[i]),t[i].y<e[6].y&&(e[6]=t[i]),t[i].x+t[i].y<e[7].x+e[7].y&&(e[7]=t[i]);return e},toCoordinateArray:function(t){for(var e=new Array(t.size()).fill(null),n=0;n<t.size();n++){var i=t.get(n);e[n]=i}return e},grahamScan:function(t){var e=null,n=new en;n.push(t[0]),n.push(t[1]),n.push(t[2]);for(var i=3;i<t.length;i++){for(e=n.pop();!n.empty()&&0<V.index(n.peek(),e,t[i]);)e=n.pop();n.push(e),n.push(t[i])}return n.push(t[0]),n},interfaces_:function(){return[]},getClass:function(){return rn}}),rn.extractCoordinates=function(t){var e=new nn;return t.apply(e),e.getCoordinates()},e(sn.prototype,{compare:function(t,e){var n=t,i=e;return sn.polarCompare(this._origin,n,i)},interfaces_:function(){return[u]},getClass:function(){return sn}}),sn.polarCompare=function(t,e,n){var i=e.x-t.x,r=e.y-t.y,s=n.x-t.x,o=n.y-t.y,a=V.index(t,e,n);if(a===V.COUNTERCLOCKWISE)return 1;if(a===V.CLOCKWISE)return-1;var u=i*i+r*r,l=s*s+o*o;return u<l?-1:l<u?1:0},rn.RadialComparator=sn,e(on.prototype,{addPolygon:function(t){if(t.isEmpty())return null;var e=null,n=null,i=this.horizontalBisector(t);if(0===i.getLength())n=0,e=i.getCoordinate();else{var r=i.intersection(t),s=this.widestGeometry(r);n=s.getEnvelopeInternal().getWidth(),e=on.centre(s.getEnvelopeInternal())}(null===this._interiorPoint||n>this._maxWidth)&&(this._interiorPoint=e,this._maxWidth=n)},getInteriorPoint:function(){return this._interiorPoint},widestGeometry:function(){if(arguments[0]instanceof Lt){var t=arguments[0];if(t.isEmpty())return t;for(var e=t.getGeometryN(0),n=1;n<t.getNumGeometries();n++)t.getGeometryN(n).getEnvelopeInternal().getWidth()>e.getEnvelopeInternal().getWidth()&&(e=t.getGeometryN(n));return e}if(arguments[0]instanceof K){var i=arguments[0];return i instanceof Lt?this.widestGeometry(i):i}},horizontalBisector:function(t){var e=t.getEnvelopeInternal(),n=an.getBisectorY(t);return this._factory.createLineString([new x(e.getMinX(),n),new x(e.getMaxX(),n)])},add:function(t){if(t instanceof Ut)this.addPolygon(t);else if(t instanceof Lt)for(var e=t,n=0;n<e.getNumGeometries();n++)this.add(e.getGeometryN(n))},interfaces_:function(){return[]},getClass:function(){return on}}),on.centre=function(t){return new x(on.avg(t.getMinX(),t.getMaxX()),on.avg(t.getMinY(),t.getMaxY()))},on.avg=function(t,e){return(t+e)/2},e(an.prototype,{updateInterval:function(t){t<=this._centreY?t>this._loY&&(this._loY=t):t>this._centreY&&t<this._hiY&&(this._hiY=t)},getBisectorY:function(){this.process(this._poly.getExteriorRing());for(var t=0;t<this._poly.getNumInteriorRing();t++)this.process(this._poly.getInteriorRingN(t));return on.avg(this._hiY,this._loY)},process:function(t){for(var e=t.getCoordinateSequence(),n=0;n<e.size();n++){var i=e.getY(n);this.updateInterval(i)}},interfaces_:function(){return[]},getClass:function(){return an}}),an.getBisectorY=function(t){return new an(t).getBisectorY()},on.SafeBisectorFinder=an,e(un.prototype,{addEndpoints:function(){if(arguments[0]instanceof K){var t=arguments[0];if(t instanceof Bt)this.addEndpoints(t.getCoordinates());else if(t instanceof Lt)for(var e=t,n=0;n<e.getNumGeometries();n++)this.addEndpoints(e.getGeometryN(n))}else if(arguments[0]instanceof Array){var i=arguments[0];this.add(i[0]),this.add(i[i.length-1])}},getInteriorPoint:function(){return this._interiorPoint},addInterior:function(){if(arguments[0]instanceof K){var t=arguments[0];if(t instanceof Bt)this.addInterior(t.getCoordinates());else if(t instanceof Lt)for(var e=t,n=0;n<e.getNumGeometries();n++)this.addInterior(e.getGeometryN(n))}else if(arguments[0]instanceof Array){var i=arguments[0];for(n=1;n<i.length-1;n++)this.add(i[n])}},add:function(t){var e=t.distance(this._centroid);e<this._minDistance&&(this._interiorPoint=new x(t),this._minDistance=e)},interfaces_:function(){return[]},getClass:function(){return un}}),e(ln.prototype,{getInteriorPoint:function(){return this._interiorPoint},add:function(){if(arguments[0]instanceof K){var t=arguments[0];if(t instanceof Vt)this.add(t.getCoordinate());else if(t instanceof Lt)for(var e=t,n=0;n<e.getNumGeometries();n++)this.add(e.getGeometryN(n))}else if(arguments[0]instanceof x){var i=arguments[0],r=i.distance(this._centroid);r<this._minDistance&&(this._interiorPoint=new x(i),this._minDistance=r)}},interfaces_:function(){return[]},getClass:function(){return ln}}),e(hn.prototype,{select:function(){if(1===arguments.length);else if(2===arguments.length){var t=arguments[0],e=arguments[1];t.getLineSegment(e,this.selectedSegment),this.select(this.selectedSegment)}},interfaces_:function(){return[]},getClass:function(){return hn}}),e(cn.prototype,{hasChildren:function(){for(var t=0;t<2;t++)if(null!==this._subnode[t])return!0;return!1},isPrunable:function(){return!(this.hasChildren()||this.hasItems())},addAllItems:function(t){t.addAll(this._items);for(var e=0;e<2;e++)null!==this._subnode[e]&&this._subnode[e].addAllItems(t);return t},size:function(){for(var t=0,e=0;e<2;e++)null!==this._subnode[e]&&(t+=this._subnode[e].size());return t+this._items.size()},addAllItemsFromOverlapping:function(t,e){if(null!==t&&!this.isSearchMatch(t))return null;e.addAll(this._items),null!==this._subnode[0]&&this._subnode[0].addAllItemsFromOverlapping(t,e),null!==this._subnode[1]&&this._subnode[1].addAllItemsFromOverlapping(t,e)},hasItems:function(){return!this._items.isEmpty()},remove:function(t,e){if(!this.isSearchMatch(t))return!1;for(var n=!1,i=0;i<2;i++)if(null!==this._subnode[i]&&(n=this._subnode[i].remove(t,e))){this._subnode[i].isPrunable()&&(this._subnode[i]=null);break}return n||(n=this._items.remove(e))},getItems:function(){return this._items},depth:function(){for(var t=0,e=0;e<2;e++)if(null!==this._subnode[e]){var n=this._subnode[e].depth();t<n&&(t=n)}return t+1},nodeSize:function(){for(var t=0,e=0;e<2;e++)null!==this._subnode[e]&&(t+=this._subnode[e].nodeSize());return t+1},add:function(t){this._items.add(t)},interfaces_:function(){return[]},getClass:function(){return cn}}),cn.getSubnodeIndex=function(t,e){var n=-1;return t.min>=e&&(n=1),t.max<=e&&(n=0),n},e(fn.prototype,{expandToInclude:function(t){t.max>this.max&&(this.max=t.max),t.min<this.min&&(this.min=t.min)},getWidth:function(){return this.max-this.min},overlaps:function(){if(1===arguments.length){var t=arguments[0];return this.overlaps(t.min,t.max)}if(2===arguments.length){var e=arguments[0],n=arguments[1];return!(this.min>n||this.max<e)}},getMin:function(){return this.min},toString:function(){return"["+this.min+", "+this.max+"]"},contains:function(){if(1===arguments.length){if(arguments[0]instanceof fn){var t=arguments[0];return this.contains(t.min,t.max)}if("number"==typeof arguments[0]){var e=arguments[0];return e>=this.min&&e<=this.max}}else if(2===arguments.length){var n=arguments[0],i=arguments[1];return n>=this.min&&i<=this.max}},init:function(t,e){this.min=t,(this.max=e)<t&&(this.min=e,this.max=t)},getMax:function(){return this.max},interfaces_:function(){return[]},getClass:function(){return fn}}),gn.exponent=function(t){return function(t,e){var n,i,r,s,o={32:8,64:11}[t];s||(n=e<0||1/e<0,isFinite(e)||(s={32:{d:127,c:128,b:0,a:0},64:{d:32752,c:0,b:0,a:0}}[t],n&&(s.d+=1<<t/4-1),i=Math.pow(2,o)-1,r=0));if(!s){for(i={32:127,64:1023}[t],r=Math.abs(e);2<=r;)i++,r/=2;for(;r<1&&0<i;)i--,r*=2;i<=0&&(r/=2),32===t&&254<i&&(s={d:n?255:127,c:128,b:0,a:0},i=Math.pow(2,o)-1,r=0)}return i}(64,t)-1023},gn.powerOf2=function(t){return Math.pow(2,t)},e(dn.prototype,{getInterval:function(){return this._interval},getLevel:function(){return this._level},computeKey:function(t){for(this._level=dn.computeLevel(t),this._interval=new fn,this.computeInterval(this._level,t);!this._interval.contains(t);)this._level+=1,this.computeInterval(this._level,t)},computeInterval:function(t,e){var n=gn.powerOf2(t);this._pt=Math.floor(e.getMin()/n)*n,this._interval.init(this._pt,this._pt+n)},getPoint:function(){return this._pt},interfaces_:function(){return[]},getClass:function(){return dn}}),dn.computeLevel=function(t){var e=t.getWidth();return gn.exponent(e)+1},p(_n,cn),e(_n.prototype,{getInterval:function(){return this._interval},find:function(t){var e=cn.getSubnodeIndex(t,this._centre);return-1===e?this:null!==this._subnode[e]?this._subnode[e].find(t):this},insert:function(t){y.isTrue(null===this._interval||this._interval.contains(t._interval));var e=cn.getSubnodeIndex(t._interval,this._centre);if(t._level===this._level-1)this._subnode[e]=t;else{var n=this.createSubnode(e);n.insert(t),this._subnode[e]=n}},isSearchMatch:function(t){return t.overlaps(this._interval)},getSubnode:function(t){return null===this._subnode[t]&&(this._subnode[t]=this.createSubnode(t)),this._subnode[t]},getNode:function(t){var e=cn.getSubnodeIndex(t,this._centre);return-1!==e?this.getSubnode(e).getNode(t):this},createSubnode:function(t){var e=0,n=0;switch(t){case 0:e=this._interval.getMin(),n=this._centre;break;case 1:e=this._centre,n=this._interval.getMax()}return new _n(new fn(e,n),this._level-1)},interfaces_:function(){return[]},getClass:function(){return _n}}),_n.createNode=function(t){var e=new dn(t);return new _n(e.getInterval(),e.getLevel())},_n.createExpanded=function(t,e){var n=new fn(e);null!==t&&n.expandToInclude(t._interval);var i=_n.createNode(n);return null!==t&&i.insert(t),i},e(pn.prototype,{interfaces_:function(){return[]},getClass:function(){return pn}}),pn.isZeroWidth=function(t,e){var n=e-t;if(0===n)return!0;var i=Math.max(Math.abs(t),Math.abs(e));return gn.exponent(n/i)<=pn.MIN_BINARY_EXPONENT},pn.MIN_BINARY_EXPONENT=-50,p(mn,cn),e(mn.prototype,{insert:function(t,e){var n=cn.getSubnodeIndex(t,mn.origin);if(-1===n)return this.add(e),null;var i=this._subnode[n];if(null===i||!i.getInterval().contains(t)){var r=_n.createExpanded(i,t);this._subnode[n]=r}this.insertContained(this._subnode[n],t,e)},isSearchMatch:function(t){return!0},insertContained:function(t,e,n){y.isTrue(t.getInterval().contains(e));(pn.isZeroWidth(e.getMin(),e.getMax())?t.find(e):t.getNode(e)).add(n)},interfaces_:function(){return[]},getClass:function(){return mn}}),mn.origin=0,e(vn.prototype,{size:function(){return null!==this._root?this._root.size():0},insert:function(t,e){this.collectStats(t);var n=vn.ensureExtent(t,this._minExtent);this._root.insert(n,e)},query:function(){if(1===arguments.length){if("number"==typeof arguments[0]){var t=arguments[0];return this.query(new fn(t,t))}if(arguments[0]instanceof fn){var e=arguments[0],n=new P;return this.query(e,n),n}}else if(2===arguments.length){var i=arguments[0],r=arguments[1];this._root.addAllItemsFromOverlapping(i,r)}},iterator:function(){var t=new P;return this._root.addAllItems(t),t.iterator()},remove:function(t,e){var n=vn.ensureExtent(t,this._minExtent);return this._root.remove(n,e)},collectStats:function(t){var e=t.getWidth();e<this._minExtent&&0<e&&(this._minExtent=e)},depth:function(){return null!==this._root?this._root.depth():0},nodeSize:function(){return null!==this._root?this._root.nodeSize():0},interfaces_:function(){return[]},getClass:function(){return vn}}),vn.ensureExtent=function(t,e){var n=t.getMin(),i=t.getMax();return n!==i?t:(n===i&&(i=(n-=e/2)+e/2),new fn(n,i))},e(yn.prototype,{getLineSegment:function(t,e){e.p0=this._pts[t],e.p1=this._pts[t+1]},computeSelect:function(t,e,n,i){var r=this._pts[e],s=this._pts[n];if(n-e==1)return i.select(this,e),null;if(!t.intersects(r,s))return null;var o=Math.trunc((e+n)/2);e<o&&this.computeSelect(t,e,o,i),o<n&&this.computeSelect(t,o,n,i)},getCoordinates:function(){for(var t=new Array(this._end-this._start+1).fill(null),e=0,n=this._start;n<=this._end;n++)t[e++]=this._pts[n];return t},computeOverlaps:function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];this.computeOverlaps(this._start,this._end,t,t._start,t._end,e)}else if(6===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2],s=arguments[3],o=arguments[4],a=arguments[5];if(i-n==1&&o-s==1)return a.overlap(this,n,r,s),null;if(!this.overlaps(n,i,r,s,o))return null;var u=Math.trunc((n+i)/2),l=Math.trunc((s+o)/2);n<u&&(s<l&&this.computeOverlaps(n,u,r,s,l,a),l<o&&this.computeOverlaps(n,u,r,l,o,a)),u<i&&(s<l&&this.computeOverlaps(u,i,r,s,l,a),l<o&&this.computeOverlaps(u,i,r,l,o,a))}},setId:function(t){this._id=t},select:function(t,e){this.computeSelect(t,this._start,this._end,e)},getEnvelope:function(){if(null===this._env){var t=this._pts[this._start],e=this._pts[this._end];this._env=new M(t,e)}return this._env},overlaps:function(t,e,n,i,r){return M.intersects(this._pts[t],this._pts[e],n._pts[i],n._pts[r])},getEndIndex:function(){return this._end},getStartIndex:function(){return this._start},getContext:function(){return this._context},getId:function(){return this._id},interfaces_:function(){return[]},getClass:function(){return yn}}),e(xn.prototype,{interfaces_:function(){return[]},getClass:function(){return xn}}),xn.isNorthern=function(t){return t===xn.NE||t===xn.NW},xn.isOpposite=function(t,e){return t!==e&&2===(t-e+4)%4},xn.commonHalfPlane=function(t,e){if(t===e)return t;if(2===(t-e+4)%4)return-1;var n=t<e?t:e;return 0===n&&3===(e<t?t:e)?3:n},xn.isInHalfPlane=function(t,e){return e===xn.SE?t===xn.SE||t===xn.SW:t===e||t===e+1},xn.quadrant=function(){if("number"==typeof arguments[0]&&"number"==typeof arguments[1]){var t=arguments[0],e=arguments[1];if(0===t&&0===e)throw new c("Cannot compute the quadrant for point ( "+t+", "+e+" )");return 0<=t?0<=e?xn.NE:xn.SE:0<=e?xn.NW:xn.SW}if(arguments[0]instanceof x&&arguments[1]instanceof x){var n=arguments[0],i=arguments[1];if(i.x===n.x&&i.y===n.y)throw new c("Cannot compute the quadrant for two identical points "+n);return i.x>=n.x?i.y>=n.y?xn.NE:xn.SE:i.y>=n.y?xn.NW:xn.SW}},xn.NE=0,xn.NW=1,xn.SW=2,xn.SE=3,e(En.prototype,{interfaces_:function(){return[]},getClass:function(){return En}}),En.getChainStartIndices=function(t){var e=0,n=new P;n.add(new G(e));do{var i=En.findChainEnd(t,e);n.add(new G(i)),e=i}while(e<t.length-1);return En.toIntArray(n)},En.findChainEnd=function(t,e){for(var n=e;n<t.length-1&&t[n].equals2D(t[n+1]);)n++;if(n>=t.length-1)return t.length-1;for(var i=xn.quadrant(t[n],t[n+1]),r=e+1;r<t.length;){if(!t[r-1].equals2D(t[r]))if(xn.quadrant(t[r-1],t[r])!==i)break;r++}return r-1},En.getChains=function(){if(1===arguments.length){var t=arguments[0];return En.getChains(t,null)}if(2===arguments.length){for(var e=arguments[0],n=arguments[1],i=new P,r=En.getChainStartIndices(e),s=0;s<r.length-1;s++){var o=new yn(e,r[s],r[s+1],n);i.add(o)}return i}},En.toIntArray=function(t){for(var e=new Array(t.size()).fill(null),n=0;n<e.length;n++)e[n]=t.get(n).intValue();return e},e(In.prototype,{interfaces_:function(){return[]},getClass:function(){return In}}),In.orientationIndex=function(t,e,n){var i=e.x-t.x,r=e.y-t.y,s=n.x-e.x,o=n.y-e.y;return In.signOfDet2x2(i,r,s,o)},In.signOfDet2x2=function(t,e,n,i){var r=null,s=null,o=null;if(r=1,0===t||0===i)return 0===e||0===n?0:0<e?0<n?-r:r:0<n?r:-r;if(0===e||0===n)return 0<i?0<t?r:-r:0<t?-r:r;if(0<e?0<i?e<=i||(r=-r,s=t,t=n,n=s,s=e,e=i,i=s):e<=-i?(r=-r,n=-n,i=-i):(s=t,t=-n,n=s,s=e,e=-i,i=s):0<i?-e<=i?(r=-r,t=-t,e=-e):(s=-t,t=n,n=s,s=-e,e=i,i=s):i<=e?(t=-t,e=-e,n=-n,i=-i):(r=-r,s=-t,t=-n,n=s,s=-e,e=-i,i=s),0<t){if(!(0<n))return r;if(!(t<=n))return r}else{if(0<n)return-r;if(!(n<=t))return-r;r=-r,t=-t,n=-n}for(;;){if((i-=(o=Math.floor(n/t))*e)<0)return-r;if(e<i)return r;if((n-=o*t)+n<t){if(e<i+i)return r}else{if(i+i<e)return-r;n=t-n,i=e-i,r=-r}if(0===i)return 0===n?0:-r;if(0===n)return r;if((e-=(o=Math.floor(t/n))*i)<0)return r;if(i<e)return-r;if((t-=o*n)+t<n){if(i<e+e)return-r}else{if(e+e<i)return r;t=n-t,e=i-e,r=-r}if(0===e)return 0===t?0:r;if(0===t)return-r}},e(Nn.prototype,{isInside:function(t){},interfaces_:function(){return[]},getClass:function(){return Nn}}),e(Cn.prototype,{testLineSegment:function(t,e){var n,i,r,s,o=e.p0,a=e.p1;n=o.x-t.x,i=o.y-t.y,r=a.x-t.x,s=a.y-t.y,(0<i&&s<=0||0<s&&i<=0)&&0<In.signOfDet2x2(n,i,r,s)/(s-i)&&this._crossings++},buildIndex:function(){this._tree=new vn;for(var t=nt.removeRepeatedPoints(this._ring.getCoordinates()),e=En.getChains(t),n=0;n<e.size();n++){var i=e.get(n),r=i.getEnvelope();this._interval.min=r.getMinY(),this._interval.max=r.getMaxY(),this._tree.insert(this._interval,i)}},testMonotoneChain:function(t,e,n){n.select(t,e)},isInside:function(t){this._crossings=0;var e=new M(S.NEGATIVE_INFINITY,S.POSITIVE_INFINITY,t.y,t.y);this._interval.min=t.y,this._interval.max=t.y;for(var n=this._tree.query(this._interval),i=new Sn(this,t),r=n.iterator();r.hasNext();){var s=r.next();this.testMonotoneChain(e,i,s)}return this._crossings%2==1},interfaces_:function(){return[Nn]},getClass:function(){return Cn}}),p(Sn,hn),e(Sn.prototype,{select:function(){if(1!==arguments.length)return hn.prototype.select.apply(this,arguments);var t=arguments[0];this.mcp.testLineSegment(this.p,t)},interfaces_:function(){return[]},getClass:function(){return Sn}}),Cn.MCSelecter=Sn,e(Ln.prototype,{getRadius:function(){return this.compute(),this._radius},getDiameter:function(){switch(this.compute(),this._extremalPts.length){case 0:return this._input.getFactory().createLineString();case 1:return this._input.getFactory().createPoint(this._centre)}var t=this._extremalPts[0],e=this._extremalPts[1];return this._input.getFactory().createLineString([t,e])},getExtremalPoints:function(){return this.compute(),this._extremalPts},computeCirclePoints:function(){if(this._input.isEmpty())return this._extremalPts=new Array(0).fill(null),null;if(1===this._input.getNumPoints()){var t=this._input.getCoordinates();return this._extremalPts=[new x(t[0])],null}var e=this._input.convexHull().getCoordinates();t=e;if(e[0].equals2D(e[e.length-1])&&(t=new Array(e.length-1).fill(null),nt.copyDeep(e,0,t,0,e.length-1)),t.length<=2)return this._extremalPts=nt.copyDeep(t),null;for(var n=Ln.lowestPoint(t),i=Ln.pointWitMinAngleWithX(t,n),r=0;r<t.length;r++){var s=Ln.pointWithMinAngleWithSegment(t,n,i);if(xe.isObtuse(n,s,i))return this._extremalPts=[new x(n),new x(i)],null;if(xe.isObtuse(s,n,i))n=s;else{if(!xe.isObtuse(s,i,n))return this._extremalPts=[new x(n),new x(i),new x(s)],null;i=s}}y.shouldNeverReachHere("Logic failure in Minimum Bounding Circle algorithm!")},compute:function(){if(null!==this._extremalPts)return null;this.computeCirclePoints(),this.computeCentre(),null!==this._centre&&(this._radius=this._centre.distance(this._extremalPts[0]))},getFarthestPoints:function(){switch(this.compute(),this._extremalPts.length){case 0:return this._input.getFactory().createLineString();case 1:return this._input.getFactory().createPoint(this._centre)}var t=this._extremalPts[0],e=this._extremalPts[this._extremalPts.length-1];return this._input.getFactory().createLineString([t,e])},getCircle:function(){if(this.compute(),null===this._centre)return this._input.getFactory().createPolygon();var t=this._input.getFactory().createPoint(this._centre);return 0===this._radius?t:t.buffer(this._radius)},getCentre:function(){return this.compute(),this._centre},computeCentre:function(){switch(this._extremalPts.length){case 0:this._centre=null;break;case 1:this._centre=this._extremalPts[0];break;case 2:this._centre=new x((this._extremalPts[0].x+this._extremalPts[1].x)/2,(this._extremalPts[0].y+this._extremalPts[1].y)/2);break;case 3:this._centre=Ee.circumcentre(this._extremalPts[0],this._extremalPts[1],this._extremalPts[2])}},interfaces_:function(){return[]},getClass:function(){return Ln}}),Ln.pointWitMinAngleWithX=function(t,e){for(var n=S.MAX_VALUE,i=null,r=0;r<t.length;r++){var s=t[r];if(s!==e){var o=s.x-e.x,a=s.y-e.y;a<0&&(a=-a);var u=a/Math.sqrt(o*o+a*a);u<n&&(n=u,i=s)}}return i},Ln.lowestPoint=function(t){for(var e=t[0],n=1;n<t.length;n++)t[n].y<e.y&&(e=t[n]);return e},Ln.pointWithMinAngleWithSegment=function(t,e,n){for(var i=S.MAX_VALUE,r=null,s=0;s<t.length;s++){var o=t[s];if(o!==e&&o!==n){var a=xe.angleBetween(e,o,n);a<i&&(i=a,r=o)}}return r},e(wn.prototype,{getWidthCoordinate:function(){return this.computeMinimumDiameter(),this._minWidthPt},getSupportingSegment:function(){return this.computeMinimumDiameter(),this._inputGeom.getFactory().createLineString([this._minBaseSeg.p0,this._minBaseSeg.p1])},getDiameter:function(){if(this.computeMinimumDiameter(),null===this._minWidthPt)return this._inputGeom.getFactory().createLineString();var t=this._minBaseSeg.project(this._minWidthPt);return this._inputGeom.getFactory().createLineString([t,this._minWidthPt])},computeWidthConvex:function(t){this._convexHullPts=t instanceof Ut?t.getExteriorRing().getCoordinates():t.getCoordinates(),0===this._convexHullPts.length?(this._minWidth=0,this._minWidthPt=null,this._minBaseSeg=null):1===this._convexHullPts.length?(this._minWidth=0,this._minWidthPt=this._convexHullPts[0],this._minBaseSeg.p0=this._convexHullPts[0],this._minBaseSeg.p1=this._convexHullPts[0]):2===this._convexHullPts.length||3===this._convexHullPts.length?(this._minWidth=0,this._minWidthPt=this._convexHullPts[0],this._minBaseSeg.p0=this._convexHullPts[0],this._minBaseSeg.p1=this._convexHullPts[1]):this.computeConvexRingMinDiameter(this._convexHullPts)},computeConvexRingMinDiameter:function(t){this._minWidth=S.MAX_VALUE;for(var e=1,n=new me,i=0;i<t.length-1;i++)n.p0=t[i],n.p1=t[i+1],e=this.findMaxPerpDistance(t,n,e)},computeMinimumDiameter:function(){if(null!==this._minWidthPt)return null;if(this._isConvex)this.computeWidthConvex(this._inputGeom);else{var t=new rn(this._inputGeom).getConvexHull();this.computeWidthConvex(t)}},getLength:function(){return this.computeMinimumDiameter(),this._minWidth},findMaxPerpDistance:function(t,e,n){for(var i=e.distancePerpendicular(t[n]),r=i,s=n,o=s;i<=r;)i=r,o=wn.nextIndex(t,s=o),r=e.distancePerpendicular(t[o]);return i<this._minWidth&&(this._minPtIndex=s,this._minWidth=i,this._minWidthPt=t[this._minPtIndex],this._minBaseSeg=new me(e)),s},getMinimumRectangle:function(){if(this.computeMinimumDiameter(),0===this._minWidth)return this._minBaseSeg.p0.equals2D(this._minBaseSeg.p1)?this._inputGeom.getFactory().createPoint(this._minBaseSeg.p0):this._minBaseSeg.toGeometry(this._inputGeom.getFactory());for(var t=this._minBaseSeg.p1.x-this._minBaseSeg.p0.x,e=this._minBaseSeg.p1.y-this._minBaseSeg.p0.y,n=S.MAX_VALUE,i=-S.MAX_VALUE,r=S.MAX_VALUE,s=-S.MAX_VALUE,o=0;o<this._convexHullPts.length;o++){var a=wn.computeC(t,e,this._convexHullPts[o]);i<a&&(i=a),a<n&&(n=a);var u=wn.computeC(-e,t,this._convexHullPts[o]);s<u&&(s=u),u<r&&(r=u)}var l=wn.computeSegmentForLine(-t,-e,s),h=wn.computeSegmentForLine(-t,-e,r),c=wn.computeSegmentForLine(-e,t,i),f=wn.computeSegmentForLine(-e,t,n),g=c.lineIntersection(l),d=f.lineIntersection(l),_=f.lineIntersection(h),p=c.lineIntersection(h),m=this._inputGeom.getFactory().createLinearRing([g,d,_,p,g]);return this._inputGeom.getFactory().createPolygon(m)},interfaces_:function(){return[]},getClass:function(){return wn}}),wn.nextIndex=function(t,e){return++e>=t.length&&(e=0),e},wn.computeC=function(t,e,n){return t*n.y-e*n.x},wn.getMinimumDiameter=function(t){return new wn(t).getDiameter()},wn.getMinimumRectangle=function(t){return new wn(t).getMinimumRectangle()},wn.computeSegmentForLine=function(t,e,n){var i=null,r=null;return Math.abs(e)>Math.abs(t)?(i=new x(0,n/e),r=new x(1,n/e-t/e)):(i=new x(n/t,0),r=new x(n/t-e/t,1)),new me(i,r)};var Rn=Object.freeze({distance:Re,locate:He,match:Qe,Angle:xe,Centroid:Je,CGAlgorithms:$e,ConvexHull:rn,Distance:X,InteriorPointArea:on,InteriorPointLine:un,InteriorPointPoint:ln,RobustLineIntersector:pe,MCPointInRing:Cn,MinimumBoundingCircle:Ln,MinimumDiameter:wn});function Tn(){this._inputGeom=null,this._factory=null,this._pruneEmptyGeometry=!0,this._preserveGeometryCollectionType=!0,this._preserveCollections=!1,this._preserveType=!1}function Pn(){this._inputGeom=null,this._distanceTolerance=null;var t=arguments[0];this._inputGeom=t}function On(){Tn.apply(this),this.distanceTolerance=null;var t=arguments[0];this.distanceTolerance=t}e(Tn.prototype,{transformPoint:function(t,e){return this._factory.createPoint(this.transformCoordinates(t.getCoordinateSequence(),t))},transformPolygon:function(t,e){var n=!0,i=this.transformLinearRing(t.getExteriorRing(),t);null!==i&&i instanceof Ht&&!i.isEmpty()||(n=!1);for(var r=new P,s=0;s<t.getNumInteriorRing();s++){var o=this.transformLinearRing(t.getInteriorRingN(s),t);null===o||o.isEmpty()||(o instanceof Ht||(n=!1),r.add(o))}if(n)return this._factory.createPolygon(i,r.toArray([]));var a=new P;return null!==i&&a.add(i),a.addAll(r),this._factory.buildGeometry(a)},createCoordinateSequence:function(t){return this._factory.getCoordinateSequenceFactory().create(t)},getInputGeometry:function(){return this._inputGeom},transformMultiLineString:function(t,e){for(var n=new P,i=0;i<t.getNumGeometries();i++){var r=this.transformLineString(t.getGeometryN(i),t);null!==r&&(r.isEmpty()||n.add(r))}return this._factory.buildGeometry(n)},transformCoordinates:function(t,e){return this.copy(t)},transformLineString:function(t,e){return this._factory.createLineString(this.transformCoordinates(t.getCoordinateSequence(),t))},transformMultiPoint:function(t,e){for(var n=new P,i=0;i<t.getNumGeometries();i++){var r=this.transformPoint(t.getGeometryN(i),t);null!==r&&(r.isEmpty()||n.add(r))}return this._factory.buildGeometry(n)},transformMultiPolygon:function(t,e){for(var n=new P,i=0;i<t.getNumGeometries();i++){var r=this.transformPolygon(t.getGeometryN(i),t);null!==r&&(r.isEmpty()||n.add(r))}return this._factory.buildGeometry(n)},copy:function(t){return t.copy()},transformGeometryCollection:function(t,e){for(var n=new P,i=0;i<t.getNumGeometries();i++){var r=this.transform(t.getGeometryN(i));null!==r&&(this._pruneEmptyGeometry&&r.isEmpty()||n.add(r))}return this._preserveGeometryCollectionType?this._factory.createGeometryCollection(le.toGeometryArray(n)):this._factory.buildGeometry(n)},transform:function(t){if(this._inputGeom=t,this._factory=t.getFactory(),t instanceof Vt)return this.transformPoint(t,null);if(t instanceof Xt)return this.transformMultiPoint(t,null);if(t instanceof Ht)return this.transformLinearRing(t,null);if(t instanceof Bt)return this.transformLineString(t,null);if(t instanceof wt)return this.transformMultiLineString(t,null);if(t instanceof Ut)return this.transformPolygon(t,null);if(t instanceof Wt)return this.transformMultiPolygon(t,null);if(t instanceof Lt)return this.transformGeometryCollection(t,null);throw new c("Unknown Geometry subtype: "+t.getClass().getName())},transformLinearRing:function(t,e){var n=this.transformCoordinates(t.getCoordinateSequence(),t);if(null===n)return this._factory.createLinearRing(null);var i=n.size();return 0<i&&i<4&&!this._preserveType?this._factory.createLineString(n):this._factory.createLinearRing(n)},interfaces_:function(){return[]},getClass:function(){return Tn}}),e(Pn.prototype,{getResultGeometry:function(){return new On(this._distanceTolerance).transform(this._inputGeom)},setDistanceTolerance:function(t){if(t<=0)throw new c("Tolerance must be positive");this._distanceTolerance=t},interfaces_:function(){return[]},getClass:function(){return Pn}}),Pn.densifyPoints=function(t,e,n){for(var i=new me,r=new b,s=0;s<t.length-1;s++){i.p0=t[s],i.p1=t[s+1],r.add(i.p0,!1);var o=i.getLength(),a=Math.trunc(o/e)+1;if(1<a)for(var u=o/a,l=1;l<a;l++){var h=l*u/o,c=i.pointAlong(h);n.makePrecise(c),r.add(c,!1)}}return r.add(t[t.length-1],!1),r.toCoordinateArray()},Pn.densify=function(t,e){var n=new Pn(t);return n.setDistanceTolerance(e),n.getResultGeometry()},p(On,Tn),e(On.prototype,{transformMultiPolygon:function(t,e){var n=Tn.prototype.transformMultiPolygon.call(this,t,e);return this.createValidArea(n)},transformPolygon:function(t,e){var n=Tn.prototype.transformPolygon.call(this,t,e);return e instanceof Wt?n:this.createValidArea(n)},transformCoordinates:function(t,e){var n=t.toCoordinateArray(),i=Pn.densifyPoints(n,this.distanceTolerance,e.getPrecisionModel());return e instanceof Bt&&1===i.length&&(i=new Array(0).fill(null)),this._factory.getCoordinateSequenceFactory().create(i)},createValidArea:function(t){return t.buffer(0)},interfaces_:function(){return[]},getClass:function(){return On}}),Pn.DensifyTransformer=On;var bn=Object.freeze({Densifier:Pn});function Mn(){this._orig=null,this._sym=null,this._next=null;var t=arguments[0];this._orig=t}function Dn(){this._isMarked=!1;var t=arguments[0];Mn.call(this,t)}function An(){this._vertexMap=new oe}function Fn(){this._isStart=!1;var t=arguments[0];Dn.call(this,t)}function Gn(){An.apply(this)}function qn(){this._result=null,this._factory=null,this._graph=null,this._lines=new P,this._nodeEdgeStack=new en,this._ringStartEdge=null,this._graph=new Gn}e(Mn.prototype,{find:function(t){var e=this;do{if(null===e)return null;if(e.dest().equals2D(t))return e;e=e.oNext()}while(e!==this);return null},dest:function(){return this._sym._orig},oNext:function(){return this._sym._next},insert:function(t){if(this.oNext()===this)return this.insertAfter(t),null;var e=this.compareTo(t),n=this;do{var i=n.oNext();if(i.compareTo(t)!==e||i===this)return n.insertAfter(t),null;n=i}while(n!==this);y.shouldNeverReachHere()},insertAfter:function(t){y.equals(this._orig,t.orig());var e=this.oNext();this._sym.setNext(t),t.sym().setNext(e)},degree:function(){for(var t=0,e=this;t++,(e=e.oNext())!==this;);return t},equals:function(){if(2===arguments.length&&arguments[1]instanceof x&&arguments[0]instanceof x){var t=arguments[0],e=arguments[1];return this._orig.equals2D(t)&&this._sym._orig.equals(e)}},deltaY:function(){return this._sym._orig.y-this._orig.y},sym:function(){return this._sym},prev:function(){return this._sym.next()._sym},compareAngularDirection:function(t){var e=this.deltaX(),n=this.deltaY(),i=t.deltaX(),r=t.deltaY();if(e===i&&n===r)return 0;var s=xn.quadrant(e,n),o=xn.quadrant(i,r);return o<s?1:s<o?-1:V.index(t._orig,t.dest(),this.dest())},prevNode:function(){for(var t=this;2===t.degree();)if((t=t.prev())===this)return null;return t},compareTo:function(t){var e=t;return this.compareAngularDirection(e)},next:function(){return this._next},setSym:function(t){this._sym=t},orig:function(){return this._orig},toString:function(){return"HE("+this._orig.x+" "+this._orig.y+", "+this._sym._orig.x+" "+this._sym._orig.y+")"},setNext:function(t){this._next=t},init:function(t){this.setSym(t),t.setSym(this),this.setNext(t),t.setNext(this)},deltaX:function(){return this._sym._orig.x-this._orig.x},interfaces_:function(){return[]},getClass:function(){return Mn}}),Mn.init=function(t,e){if(null!==t._sym||null!==e._sym||null!==t._next||null!==e._next)throw new IllegalStateException("Edges are already initialized");return t.init(e),t},Mn.create=function(t,e){var n=new Mn(t),i=new Mn(e);return n.init(i),n},p(Dn,Mn),e(Dn.prototype,{mark:function(){this._isMarked=!0},setMark:function(t){this._isMarked=t},isMarked:function(){return this._isMarked},interfaces_:function(){return[]},getClass:function(){return Dn}}),Dn.setMarkBoth=function(t,e){t.setMark(e),t.sym().setMark(e)},Dn.isMarked=function(t){return t.isMarked()},Dn.setMark=function(t,e){t.setMark(e)},Dn.markBoth=function(t){t.mark(),t.sym().mark()},Dn.mark=function(t){t.mark()},e(An.prototype,{insert:function(t,e,n){var i=this.create(t,e);null!==n?n.insert(i):this._vertexMap.put(t,i);var r=this._vertexMap.get(e);return null!==r?r.insert(i.sym()):this._vertexMap.put(e,i.sym()),i},create:function(t,e){var n=this.createEdge(t),i=this.createEdge(e);return Mn.init(n,i),n},createEdge:function(t){return new Mn(t)},addEdge:function(t,e){if(!An.isValidEdge(t,e))return null;var n=this._vertexMap.get(t),i=null;return null!==n&&(i=n.find(e)),null!==i?i:this.insert(t,e,n)},getVertexEdges:function(){return this._vertexMap.values()},findEdge:function(t,e){var n=this._vertexMap.get(t);return null===n?null:n.find(e)},interfaces_:function(){return[]},getClass:function(){return An}}),An.isValidEdge=function(t,e){return 0!==e.compareTo(t)},p(Fn,Dn),e(Fn.prototype,{setStart:function(){this._isStart=!0},isStart:function(){return this._isStart},interfaces_:function(){return[]},getClass:function(){return Fn}}),p(Gn,An),e(Gn.prototype,{createEdge:function(t){return new Fn(t)},interfaces_:function(){return[]},getClass:function(){return Gn}}),e(qn.prototype,{addLine:function(t){this._lines.add(this._factory.createLineString(t.toCoordinateArray()))},updateRingStartEdge:function(t){return t.isStart()||(t=t.sym()).isStart()?null===this._ringStartEdge?(this._ringStartEdge=t,null):void(t.orig().compareTo(this._ringStartEdge.orig())<0&&(this._ringStartEdge=t)):null},getResult:function(){return null===this._result&&this.computeResult(),this._result},process:function(t){var e=t.prevNode();null===e&&(e=t),this.stackEdges(e),this.buildLines()},buildRing:function(t){var e=new b,n=t;for(e.add(n.orig().copy(),!1);2===n.sym().degree();){var i=n.next();if(i===t)break;e.add(i.orig().copy(),!1),n=i}e.add(n.dest().copy(),!1),this.addLine(e)},buildLine:function(t){var e=new b,n=t;for(this._ringStartEdge=null,Dn.markBoth(n),e.add(n.orig().copy(),!1);2===n.sym().degree();){this.updateRingStartEdge(n);var i=n.next();if(i===t)return this.buildRing(this._ringStartEdge),null;e.add(i.orig().copy(),!1),Dn.markBoth(n=i)}e.add(n.dest().clone(),!1),this.stackEdges(n.sym()),this.addLine(e)},stackEdges:function(t){for(var e=t;Dn.isMarked(e)||this._nodeEdgeStack.add(e),(e=e.oNext())!==t;);},computeResult:function(){for(var t=this._graph.getVertexEdges().iterator();t.hasNext();){var e=t.next();Dn.isMarked(e)||this.process(e)}this._result=this._factory.buildGeometry(this._lines)},buildLines:function(){for(;!this._nodeEdgeStack.empty();){var t=this._nodeEdgeStack.pop();Dn.isMarked(t)||this.buildLine(t)}},add:function(){if(arguments[0]instanceof K)arguments[0].apply({interfaces_:function(){return[j]},filter:function(t){t instanceof Bt&&this.add(t)}});else if(L(arguments[0],N))for(var t=arguments[0].iterator();t.hasNext();){var e=t.next();this.add(e)}else if(arguments[0]instanceof Bt){var n=arguments[0];null===this._factory&&(this._factory=n.getFactory());var i=n.getCoordinateSequence(),r=!1;for(t=1;t<i.size();t++){var s=this._graph.addEdge(i.getCoordinate(t-1),i.getCoordinate(t));null!==s&&(r||(s.setStart(),r=!0))}}},interfaces_:function(){return[]},getClass:function(){return qn}}),qn.dissolve=function(t){var e=new qn;return e.add(t),e.getResult()};var Bn=Object.freeze({LineDissolver:qn});function zn(){if(this._boundaryRule=Q.OGC_SFS_BOUNDARY_RULE,this._isIn=null,this._numBoundaries=null,0===arguments.length);else if(1===arguments.length){var t=arguments[0];if(null===t)throw new c("Rule must be non-null");this._boundaryRule=t}}function Vn(){}function Yn(){this.mce=null,this.chainIndex=null;var t=arguments[0],e=arguments[1];this.mce=t,this.chainIndex=e}function kn(){if(this._label=null,this._xValue=null,this._eventType=null,this._insertEvent=null,this._deleteEventIndex=null,this._obj=null,2===arguments.length){var t=arguments[0],e=arguments[1];this._eventType=kn.DELETE,this._xValue=t,this._insertEvent=e}else if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];this._eventType=kn.INSERT,this._label=n,this._xValue=i,this._obj=r}}function Un(){}function Xn(){this._hasIntersection=!1,this._hasProper=!1,this._hasProperInterior=!1,this._properIntersectionPoint=null,this._li=null,this._includeProper=null,this._recordIsolated=null,this._isSelfIntersection=null,this._numIntersections=0,this.numTests=0,this._bdyNodes=null,this._isDone=!1,this._isDoneWhenProperInt=!1;var t=arguments[0],e=arguments[1],n=arguments[2];this._li=t,this._includeProper=e,this._recordIsolated=n}function Hn(){Un.apply(this),this.events=new P,this.nOverlaps=null}function Wn(){if(this.location=null,1===arguments.length){if(arguments[0]instanceof Array){var t=arguments[0];this.init(t.length)}else if(Number.isInteger(arguments[0])){var e=arguments[0];this.init(1),this.location[Vn.ON]=e}else if(arguments[0]instanceof Wn){var n=arguments[0];if(this.init(n.location.length),null!==n)for(var i=0;i<this.location.length;i++)this.location[i]=n.location[i]}}else if(3===arguments.length){var r=arguments[0],s=arguments[1],o=arguments[2];this.init(3),this.location[Vn.ON]=r,this.location[Vn.LEFT]=s,this.location[Vn.RIGHT]=o}}function jn(){if(this.elt=new Array(2).fill(null),1===arguments.length){if(Number.isInteger(arguments[0])){var t=arguments[0];this.elt[0]=new Wn(t),this.elt[1]=new Wn(t)}else if(arguments[0]instanceof jn){var e=arguments[0];this.elt[0]=new Wn(e.elt[0]),this.elt[1]=new Wn(e.elt[1])}}else if(2===arguments.length){var n=arguments[0],i=arguments[1];this.elt[0]=new Wn(ve.NONE),this.elt[1]=new Wn(ve.NONE),this.elt[n].setLocation(i)}else if(3===arguments.length){var r=arguments[0],s=arguments[1],o=arguments[2];this.elt[0]=new Wn(r,s,o),this.elt[1]=new Wn(r,s,o)}else if(4===arguments.length){var a=arguments[0],u=arguments[1],l=arguments[2],h=arguments[3];this.elt[0]=new Wn(ve.NONE,ve.NONE,ve.NONE),this.elt[1]=new Wn(ve.NONE,ve.NONE,ve.NONE),this.elt[a].setLocations(u,l,h)}}function Kn(){this.coord=null,this.segmentIndex=null,this.dist=null;var t=arguments[0],e=arguments[1],n=arguments[2];this.coord=new x(t),this.segmentIndex=e,this.dist=n}function Zn(){this._nodeMap=new pt,this.edge=null;var t=arguments[0];this.edge=t}function Qn(){}function Jn(){this.e=null,this.pts=null,this.startIndex=null;var t=arguments[0];this.e=t,this.pts=t.getCoordinates();var e=new Qn;this.startIndex=e.getChainStartIndices(this.pts)}function $n(){this._depth=Array(2).fill().map(function(){return Array(3)});for(var t=0;t<2;t++)for(var e=0;e<3;e++)this._depth[t][e]=$n.NULL_VALUE}function ti(){if(this._label=null,this._isInResult=!1,this._isCovered=!1,this._isCoveredSet=!1,this._isVisited=!1,0===arguments.length);else if(1===arguments.length){var t=arguments[0];this._label=t}}function ei(){if(ti.apply(this),this.pts=null,this._env=null,this.eiList=new Zn(this),this._name=null,this._mce=null,this._isIsolated=!0,this._depth=new $n,this._depthDelta=0,1===arguments.length){var t=arguments[0];ei.call(this,t,null)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this.pts=e,this._label=n}}function ni(){ti.apply(this),this._coord=null,this._edges=null;var t=arguments[0],e=arguments[1];this._coord=t,this._edges=e,this._label=new jn(0,ve.NONE)}function ii(){this.nodeMap=new pt,this.nodeFact=null;var t=arguments[0];this.nodeFact=t}function ri(){if(this._edge=null,this._label=null,this._node=null,this._p0=null,this._p1=null,this._dx=null,this._dy=null,this._quadrant=null,1===arguments.length){var t=arguments[0];this._edge=t}else if(3===arguments.length){var e=arguments[0],n=arguments[1],i=arguments[2];ri.call(this,e,n,i,null)}else if(4===arguments.length){var r=arguments[0],s=arguments[1],o=arguments[2],a=arguments[3];ri.call(this,r),this.init(s,o),this._label=a}}function si(){if(this.pt=null,1===arguments.length){var t=arguments[0];v.call(this,t)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];v.call(this,si.msgWithCoord(e,n)),this.name="TopologyException",this.pt=new x(n)}}function oi(){this._isForward=null,this._isInResult=!1,this._isVisited=!1,this._sym=null,this._next=null,this._nextMin=null,this._edgeRing=null,this._minEdgeRing=null,this._depth=[0,-999,-999];var t=arguments[0],e=arguments[1];if(ri.call(this,t),this._isForward=e)this.init(t.getCoordinate(0),t.getCoordinate(1));else{var n=t.getNumPoints()-1;this.init(t.getCoordinate(n),t.getCoordinate(n-1))}this.computeDirectedLabel()}function ai(){}function ui(){if(this._edges=new P,this._nodes=null,this._edgeEndList=new P,0===arguments.length)this._nodes=new ii(new ai);else if(1===arguments.length){var t=arguments[0];this._nodes=new ii(t)}}function li(){if(ui.apply(this),this._parentGeom=null,this._lineEdgeMap=new oe,this._boundaryNodeRule=null,this._useBoundaryDeterminationRule=!0,this._argIndex=null,this._boundaryNodes=null,this._hasTooFewPoints=!1,this._invalidPoint=null,this._areaPtLocator=null,this._ptLocator=new zn,2===arguments.length){var t=arguments[0],e=arguments[1];li.call(this,t,e,Q.OGC_SFS_BOUNDARY_RULE)}else if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];this._argIndex=n,this._parentGeom=i,this._boundaryNodeRule=r,null!==i&&this.add(i)}}e(zn.prototype,{locateInPolygonRing:function(t,e){return e.getEnvelopeInternal().intersects(t)?ke.locateInRing(t,e.getCoordinates()):ve.EXTERIOR},intersects:function(t,e){return this.locate(t,e)!==ve.EXTERIOR},updateLocationInfo:function(t){t===ve.INTERIOR&&(this._isIn=!0),t===ve.BOUNDARY&&this._numBoundaries++},computeLocation:function(t,e){if(e instanceof Vt&&this.updateLocationInfo(this.locateOnPoint(t,e)),e instanceof Bt)this.updateLocationInfo(this.locateOnLineString(t,e));else if(e instanceof Ut)this.updateLocationInfo(this.locateInPolygon(t,e));else if(e instanceof wt)for(var n=e,i=0;i<n.getNumGeometries();i++){var r=n.getGeometryN(i);this.updateLocationInfo(this.locateOnLineString(t,r))}else if(e instanceof Wt){var s=e;for(i=0;i<s.getNumGeometries();i++){var o=s.getGeometryN(i);this.updateLocationInfo(this.locateInPolygon(t,o))}}else if(e instanceof Lt)for(var a=new Ue(e);a.hasNext();){var u=a.next();u!==e&&this.computeLocation(t,u)}},locateOnPoint:function(t,e){return e.getCoordinate().equals2D(t)?ve.INTERIOR:ve.EXTERIOR},locateOnLineString:function(t,e){if(!e.getEnvelopeInternal().intersects(t))return ve.EXTERIOR;var n=e.getCoordinateSequence();return e.isClosed()||!t.equals(n.getCoordinate(0))&&!t.equals(n.getCoordinate(n.size()-1))?ke.isOnLine(t,n)?ve.INTERIOR:ve.EXTERIOR:ve.BOUNDARY},locateInPolygon:function(t,e){if(e.isEmpty())return ve.EXTERIOR;var n=e.getExteriorRing(),i=this.locateInPolygonRing(t,n);if(i===ve.EXTERIOR)return ve.EXTERIOR;if(i===ve.BOUNDARY)return ve.BOUNDARY;for(var r=0;r<e.getNumInteriorRing();r++){var s=e.getInteriorRingN(r),o=this.locateInPolygonRing(t,s);if(o===ve.INTERIOR)return ve.EXTERIOR;if(o===ve.BOUNDARY)return ve.BOUNDARY}return ve.INTERIOR},locate:function(t,e){return e.isEmpty()?ve.EXTERIOR:e instanceof Bt?this.locateOnLineString(t,e):e instanceof Ut?this.locateInPolygon(t,e):(this._isIn=!1,this._numBoundaries=0,this.computeLocation(t,e),this._boundaryRule.isInBoundary(this._numBoundaries)?ve.BOUNDARY:0<this._numBoundaries||this._isIn?ve.INTERIOR:ve.EXTERIOR)},interfaces_:function(){return[]},getClass:function(){return zn}}),e(Vn.prototype,{interfaces_:function(){return[]},getClass:function(){return Vn}}),Vn.opposite=function(t){return t===Vn.LEFT?Vn.RIGHT:t===Vn.RIGHT?Vn.LEFT:t},Vn.ON=0,Vn.LEFT=1,Vn.RIGHT=2,e(Yn.prototype,{computeIntersections:function(t,e){this.mce.computeIntersectsForChain(this.chainIndex,t.mce,t.chainIndex,e)},interfaces_:function(){return[]},getClass:function(){return Yn}}),e(kn.prototype,{isDelete:function(){return this._eventType===kn.DELETE},setDeleteEventIndex:function(t){this._deleteEventIndex=t},getObject:function(){return this._obj},compareTo:function(t){var e=t;return this._xValue<e._xValue?-1:this._xValue>e._xValue?1:this._eventType<e._eventType?-1:this._eventType>e._eventType?1:0},getInsertEvent:function(){return this._insertEvent},isInsert:function(){return this._eventType===kn.INSERT},isSameLabel:function(t){return null!==this._label&&this._label===t._label},getDeleteEventIndex:function(){return this._deleteEventIndex},interfaces_:function(){return[n]},getClass:function(){return kn}}),kn.INSERT=1,kn.DELETE=2,e(Un.prototype,{interfaces_:function(){return[]},getClass:function(){return Un}}),e(Xn.prototype,{isTrivialIntersection:function(t,e,n,i){if(t===n&&1===this._li.getIntersectionNum()){if(Xn.isAdjacentSegments(e,i))return!0;if(t.isClosed()){var r=t.getNumPoints()-1;if(0===e&&i===r||0===i&&e===r)return!0}}return!1},getProperIntersectionPoint:function(){return this._properIntersectionPoint},setIsDoneIfProperInt:function(t){this._isDoneWhenProperInt=t},hasProperInteriorIntersection:function(){return this._hasProperInterior},isBoundaryPointInternal:function(t,e){for(var n=e.iterator();n.hasNext();){var i=n.next().getCoordinate();if(t.isIntersection(i))return!0}return!1},hasProperIntersection:function(){return this._hasProper},hasIntersection:function(){return this._hasIntersection},isDone:function(){return this._isDone},isBoundaryPoint:function(t,e){return null!==e&&(!!this.isBoundaryPointInternal(t,e[0])||!!this.isBoundaryPointInternal(t,e[1]))},setBoundaryNodes:function(t,e){this._bdyNodes=new Array(2).fill(null),this._bdyNodes[0]=t,this._bdyNodes[1]=e},addIntersections:function(t,e,n,i){if(t===n&&e===i)return null;this.numTests++;var r=t.getCoordinates()[e],s=t.getCoordinates()[e+1],o=n.getCoordinates()[i],a=n.getCoordinates()[i+1];this._li.computeIntersection(r,s,o,a),this._li.hasIntersection()&&(this._recordIsolated&&(t.setIsolated(!1),n.setIsolated(!1)),this._numIntersections++,this.isTrivialIntersection(t,e,n,i)||(this._hasIntersection=!0,!this._includeProper&&this._li.isProper()||(t.addIntersections(this._li,e,0),n.addIntersections(this._li,i,1)),this._li.isProper()&&(this._properIntersectionPoint=this._li.getIntersection(0).copy(),this._hasProper=!0,this._isDoneWhenProperInt&&(this._isDone=!0),this.isBoundaryPoint(this._li,this._bdyNodes)||(this._hasProperInterior=!0))))},interfaces_:function(){return[]},getClass:function(){return Xn}}),Xn.isAdjacentSegments=function(t,e){return 1===Math.abs(t-e)},p(Hn,Un),e(Hn.prototype,{prepareEvents:function(){De.sort(this.events);for(var t=0;t<this.events.size();t++){var e=this.events.get(t);e.isDelete()&&e.getInsertEvent().setDeleteEventIndex(t)}},computeIntersections:function(){if(1===arguments.length){var t=arguments[0];this.nOverlaps=0,this.prepareEvents();for(var e=0;e<this.events.size();e++){var n=this.events.get(e);if(n.isInsert()&&this.processOverlaps(e,n.getDeleteEventIndex(),n,t),t.isDone())break}}else if(3===arguments.length)if(arguments[2]instanceof Xn&&L(arguments[0],w)&&L(arguments[1],w)){var i=arguments[0],r=arguments[1],s=arguments[2];this.addEdges(i,i),this.addEdges(r,r),this.computeIntersections(s)}else if("boolean"==typeof arguments[2]&&L(arguments[0],w)&&arguments[1]instanceof Xn){var o=arguments[0],a=arguments[1];arguments[2]?this.addEdges(o,null):this.addEdges(o),this.computeIntersections(a)}},addEdge:function(t,e){for(var n=t.getMonotoneChainEdge(),i=n.getStartIndexes(),r=0;r<i.length-1;r++){var s=new Yn(n,r),o=new kn(e,n.getMinX(r),s);this.events.add(o),this.events.add(new kn(n.getMaxX(r),o))}},processOverlaps:function(t,e,n,i){for(var r=n.getObject(),s=t;s<e;s++){var o=this.events.get(s);if(o.isInsert()){var a=o.getObject();n.isSameLabel(o)||(r.computeIntersections(a,i),this.nOverlaps++)}}},addEdges:function(){if(1===arguments.length)for(var t=arguments[0].iterator();t.hasNext();){var e=t.next();this.addEdge(e,e)}else if(2===arguments.length){var n=arguments[0],i=arguments[1];for(t=n.iterator();t.hasNext();){e=t.next();this.addEdge(e,i)}}},interfaces_:function(){return[]},getClass:function(){return Hn}}),e(Wn.prototype,{setAllLocations:function(t){for(var e=0;e<this.location.length;e++)this.location[e]=t},isNull:function(){for(var t=0;t<this.location.length;t++)if(this.location[t]!==ve.NONE)return!1;return!0},setAllLocationsIfNull:function(t){for(var e=0;e<this.location.length;e++)this.location[e]===ve.NONE&&(this.location[e]=t)},isLine:function(){return 1===this.location.length},merge:function(t){if(t.location.length>this.location.length){var e=new Array(3).fill(null);e[Vn.ON]=this.location[Vn.ON],e[Vn.LEFT]=ve.NONE,e[Vn.RIGHT]=ve.NONE,this.location=e}for(var n=0;n<this.location.length;n++)this.location[n]===ve.NONE&&n<t.location.length&&(this.location[n]=t.location[n])},getLocations:function(){return this.location},flip:function(){if(this.location.length<=1)return null;var t=this.location[Vn.LEFT];this.location[Vn.LEFT]=this.location[Vn.RIGHT],this.location[Vn.RIGHT]=t},toString:function(){var t=new F;return 1<this.location.length&&t.append(ve.toLocationSymbol(this.location[Vn.LEFT])),t.append(ve.toLocationSymbol(this.location[Vn.ON])),1<this.location.length&&t.append(ve.toLocationSymbol(this.location[Vn.RIGHT])),t.toString()},setLocations:function(t,e,n){this.location[Vn.ON]=t,this.location[Vn.LEFT]=e,this.location[Vn.RIGHT]=n},get:function(t){return t<this.location.length?this.location[t]:ve.NONE},isArea:function(){return 1<this.location.length},isAnyNull:function(){for(var t=0;t<this.location.length;t++)if(this.location[t]===ve.NONE)return!0;return!1},setLocation:function(){if(1===arguments.length){var t=arguments[0];this.setLocation(Vn.ON,t)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this.location[e]=n}},init:function(t){this.location=new Array(t).fill(null),this.setAllLocations(ve.NONE)},isEqualOnSide:function(t,e){return this.location[e]===t.location[e]},allPositionsEqual:function(t){for(var e=0;e<this.location.length;e++)if(this.location[e]!==t)return!1;return!0},interfaces_:function(){return[]},getClass:function(){return Wn}}),e(jn.prototype,{getGeometryCount:function(){var t=0;return this.elt[0].isNull()||t++,this.elt[1].isNull()||t++,t},setAllLocations:function(t,e){this.elt[t].setAllLocations(e)},isNull:function(t){return this.elt[t].isNull()},setAllLocationsIfNull:function(){if(1===arguments.length){var t=arguments[0];this.setAllLocationsIfNull(0,t),this.setAllLocationsIfNull(1,t)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this.elt[e].setAllLocationsIfNull(n)}},isLine:function(t){return this.elt[t].isLine()},merge:function(t){for(var e=0;e<2;e++)null===this.elt[e]&&null!==t.elt[e]?this.elt[e]=new Wn(t.elt[e]):this.elt[e].merge(t.elt[e])},flip:function(){this.elt[0].flip(),this.elt[1].flip()},getLocation:function(){if(1===arguments.length){var t=arguments[0];return this.elt[t].get(Vn.ON)}if(2===arguments.length){var e=arguments[0],n=arguments[1];return this.elt[e].get(n)}},toString:function(){var t=new F;return null!==this.elt[0]&&(t.append("A:"),t.append(this.elt[0].toString())),null!==this.elt[1]&&(t.append(" B:"),t.append(this.elt[1].toString())),t.toString()},isArea:function(){if(0===arguments.length)return this.elt[0].isArea()||this.elt[1].isArea();if(1===arguments.length){var t=arguments[0];return this.elt[t].isArea()}},isAnyNull:function(t){return this.elt[t].isAnyNull()},setLocation:function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];this.elt[t].setLocation(Vn.ON,e)}else if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];this.elt[n].setLocation(i,r)}},isEqualOnSide:function(t,e){return this.elt[0].isEqualOnSide(t.elt[0],e)&&this.elt[1].isEqualOnSide(t.elt[1],e)},allPositionsEqual:function(t,e){return this.elt[t].allPositionsEqual(e)},toLine:function(t){this.elt[t].isArea()&&(this.elt[t]=new Wn(this.elt[t].location[0]))},interfaces_:function(){return[]},getClass:function(){return jn}}),jn.toLineLabel=function(t){for(var e=new jn(ve.NONE),n=0;n<2;n++)e.setLocation(n,t.getLocation(n));return e},e(Kn.prototype,{getSegmentIndex:function(){return this.segmentIndex},getCoordinate:function(){return this.coord},print:function(t){t.print(this.coord),t.print(" seg # = "+this.segmentIndex),t.println(" dist = "+this.dist)},compareTo:function(t){var e=t;return this.compare(e.segmentIndex,e.dist)},isEndPoint:function(t){return 0===this.segmentIndex&&0===this.dist||this.segmentIndex===t},toString:function(){return this.coord+" seg # = "+this.segmentIndex+" dist = "+this.dist},getDistance:function(){return this.dist},compare:function(t,e){return this.segmentIndex<t?-1:this.segmentIndex>t?1:this.dist<e?-1:this.dist>e?1:0},interfaces_:function(){return[n]},getClass:function(){return Kn}}),e(Zn.prototype,{print:function(t){t.println("Intersections:");for(var e=this.iterator();e.hasNext();){e.next().print(t)}},iterator:function(){return this._nodeMap.values().iterator()},addSplitEdges:function(t){this.addEndpoints();for(var e=this.iterator(),n=e.next();e.hasNext();){var i=e.next(),r=this.createSplitEdge(n,i);t.add(r),n=i}},addEndpoints:function(){var t=this.edge.pts.length-1;this.add(this.edge.pts[0],0,0),this.add(this.edge.pts[t],t,0)},createSplitEdge:function(t,e){var n=e.segmentIndex-t.segmentIndex+2,i=this.edge.pts[e.segmentIndex],r=0<e.dist||!e.coord.equals2D(i);r||n--;var s=new Array(n).fill(null),o=0;s[o++]=new x(t.coord);for(var a=t.segmentIndex+1;a<=e.segmentIndex;a++)s[o++]=this.edge.pts[a];return r&&(s[o]=e.coord),new ei(s,new jn(this.edge._label))},add:function(t,e,n){var i=new Kn(t,e,n),r=this._nodeMap.get(i);return null!==r?r:(this._nodeMap.put(i,i),i)},isIntersection:function(t){for(var e=this.iterator();e.hasNext();){if(e.next().coord.equals(t))return!0}return!1},interfaces_:function(){return[]},getClass:function(){return Zn}}),e(Qn.prototype,{getChainStartIndices:function(t){var e=0,n=new P;n.add(new G(e));do{var i=this.findChainEnd(t,e);n.add(new G(i)),e=i}while(e<t.length-1);return Qn.toIntArray(n)},findChainEnd:function(t,e){for(var n=xn.quadrant(t[e],t[e+1]),i=e+1;i<t.length;){if(xn.quadrant(t[i-1],t[i])!==n)break;i++}return i-1},interfaces_:function(){return[]},getClass:function(){return Qn}}),Qn.toIntArray=function(t){for(var e=new Array(t.size()).fill(null),n=0;n<e.length;n++)e[n]=t.get(n).intValue();return e},e(Jn.prototype,{getCoordinates:function(){return this.pts},getMaxX:function(t){var e=this.pts[this.startIndex[t]].x,n=this.pts[this.startIndex[t+1]].x;return n<e?e:n},getMinX:function(t){var e=this.pts[this.startIndex[t]].x,n=this.pts[this.startIndex[t+1]].x;return e<n?e:n},computeIntersectsForChain:function(){if(4===arguments.length){var t=arguments[0],e=arguments[1],n=arguments[2],i=arguments[3];this.computeIntersectsForChain(this.startIndex[t],this.startIndex[t+1],e,e.startIndex[n],e.startIndex[n+1],i)}else if(6===arguments.length){var r=arguments[0],s=arguments[1],o=arguments[2],a=arguments[3],u=arguments[4],l=arguments[5];if(s-r==1&&u-a==1)return l.addIntersections(this.e,r,o.e,a),null;if(!this.overlaps(r,s,o,a,u))return null;var h=Math.trunc((r+s)/2),c=Math.trunc((a+u)/2);r<h&&(a<c&&this.computeIntersectsForChain(r,h,o,a,c,l),c<u&&this.computeIntersectsForChain(r,h,o,c,u,l)),h<s&&(a<c&&this.computeIntersectsForChain(h,s,o,a,c,l),c<u&&this.computeIntersectsForChain(h,s,o,c,u,l))}},overlaps:function(t,e,n,i,r){return M.intersects(this.pts[t],this.pts[e],n.pts[i],n.pts[r])},getStartIndexes:function(){return this.startIndex},computeIntersects:function(t,e){for(var n=0;n<this.startIndex.length-1;n++)for(var i=0;i<t.startIndex.length-1;i++)this.computeIntersectsForChain(n,t,i,e)},interfaces_:function(){return[]},getClass:function(){return Jn}}),e($n.prototype,{getDepth:function(t,e){return this._depth[t][e]},setDepth:function(t,e,n){this._depth[t][e]=n},isNull:function(){if(0===arguments.length){for(var t=0;t<2;t++)for(var e=0;e<3;e++)if(this._depth[t][e]!==$n.NULL_VALUE)return!1;return!0}if(1===arguments.length){var n=arguments[0];return this._depth[n][1]===$n.NULL_VALUE}if(2===arguments.length){var i=arguments[0],r=arguments[1];return this._depth[i][r]===$n.NULL_VALUE}},normalize:function(){for(var t=0;t<2;t++)if(!this.isNull(t)){var e=this._depth[t][1];this._depth[t][2]<e&&(e=this._depth[t][2]),e<0&&(e=0);for(var n=1;n<3;n++){var i=0;this._depth[t][n]>e&&(i=1),this._depth[t][n]=i}}},getDelta:function(t){return this._depth[t][Vn.RIGHT]-this._depth[t][Vn.LEFT]},getLocation:function(t,e){return this._depth[t][e]<=0?ve.EXTERIOR:ve.INTERIOR},toString:function(){return"A: "+this._depth[0][1]+","+this._depth[0][2]+" B: "+this._depth[1][1]+","+this._depth[1][2]},add:function(){if(1===arguments.length)for(var t=arguments[0],e=0;e<2;e++)for(var n=1;n<3;n++){var i=t.getLocation(e,n);i!==ve.EXTERIOR&&i!==ve.INTERIOR||(this.isNull(e,n)?this._depth[e][n]=$n.depthAtLocation(i):this._depth[e][n]+=$n.depthAtLocation(i))}else if(3===arguments.length){var r=arguments[0],s=arguments[1];arguments[2]===ve.INTERIOR&&this._depth[r][s]++}},interfaces_:function(){return[]},getClass:function(){return $n}}),$n.depthAtLocation=function(t){return t===ve.EXTERIOR?0:t===ve.INTERIOR?1:$n.NULL_VALUE},$n.NULL_VALUE=-1,e(ti.prototype,{setVisited:function(t){this._isVisited=t},setInResult:function(t){this._isInResult=t},isCovered:function(){return this._isCovered},isCoveredSet:function(){return this._isCoveredSet},setLabel:function(t){this._label=t},getLabel:function(){return this._label},setCovered:function(t){this._isCovered=t,this._isCoveredSet=!0},updateIM:function(t){y.isTrue(2<=this._label.getGeometryCount(),"found partial label"),this.computeIM(t)},isInResult:function(){return this._isInResult},isVisited:function(){return this._isVisited},interfaces_:function(){return[]},getClass:function(){return ti}}),p(ei,ti),e(ei.prototype,{getDepth:function(){return this._depth},getCollapsedEdge:function(){var t=new Array(2).fill(null);return t[0]=this.pts[0],t[1]=this.pts[1],new ei(t,jn.toLineLabel(this._label))},isIsolated:function(){return this._isIsolated},getCoordinates:function(){return this.pts},setIsolated:function(t){this._isIsolated=t},setName:function(t){this._name=t},equals:function(t){if(!(t instanceof ei))return!1;var e=t;if(this.pts.length!==e.pts.length)return!1;for(var n=!0,i=!0,r=this.pts.length,s=0;s<this.pts.length;s++)if(this.pts[s].equals2D(e.pts[s])||(n=!1),this.pts[s].equals2D(e.pts[--r])||(i=!1),!n&&!i)return!1;return!0},getCoordinate:function(){if(0===arguments.length)return 0<this.pts.length?this.pts[0]:null;if(1===arguments.length){var t=arguments[0];return this.pts[t]}},print:function(t){t.print("edge "+this._name+": "),t.print("LINESTRING (");for(var e=0;e<this.pts.length;e++)0<e&&t.print(","),t.print(this.pts[e].x+" "+this.pts[e].y);t.print(")  "+this._label+" "+this._depthDelta)},computeIM:function(t){ei.updateIM(this._label,t)},isCollapsed:function(){return!!this._label.isArea()&&(3===this.pts.length&&!!this.pts[0].equals(this.pts[2]))},isClosed:function(){return this.pts[0].equals(this.pts[this.pts.length-1])},getMaximumSegmentIndex:function(){return this.pts.length-1},getDepthDelta:function(){return this._depthDelta},getNumPoints:function(){return this.pts.length},printReverse:function(t){t.print("edge "+this._name+": ");for(var e=this.pts.length-1;0<=e;e--)t.print(this.pts[e]+" ");t.println("")},getMonotoneChainEdge:function(){return null===this._mce&&(this._mce=new Jn(this)),this._mce},getEnvelope:function(){if(null===this._env){this._env=new M;for(var t=0;t<this.pts.length;t++)this._env.expandToInclude(this.pts[t])}return this._env},addIntersection:function(t,e,n,i){var r=new x(t.getIntersection(i)),s=e,o=t.getEdgeDistance(n,i),a=s+1;if(a<this.pts.length){var u=this.pts[a];r.equals2D(u)&&(s=a,o=0)}this.eiList.add(r,s,o)},toString:function(){var t=new Gt;t.append("edge "+this._name+": "),t.append("LINESTRING (");for(var e=0;e<this.pts.length;e++)0<e&&t.append(","),t.append(this.pts[e].x+" "+this.pts[e].y);return t.append(")  "+this._label+" "+this._depthDelta),t.toString()},isPointwiseEqual:function(t){if(this.pts.length!==t.pts.length)return!1;for(var e=0;e<this.pts.length;e++)if(!this.pts[e].equals2D(t.pts[e]))return!1;return!0},setDepthDelta:function(t){this._depthDelta=t},getEdgeIntersectionList:function(){return this.eiList},addIntersections:function(t,e,n){for(var i=0;i<t.getIntersectionNum();i++)this.addIntersection(t,e,n,i)},interfaces_:function(){return[]},getClass:function(){return ei}}),ei.updateIM=function(){if(!(2===arguments.length&&arguments[1]instanceof ye&&arguments[0]instanceof jn))return ti.prototype.updateIM.apply(this,arguments);var t=arguments[0],e=arguments[1];e.setAtLeastIfValid(t.getLocation(0,Vn.ON),t.getLocation(1,Vn.ON),1),t.isArea()&&(e.setAtLeastIfValid(t.getLocation(0,Vn.LEFT),t.getLocation(1,Vn.LEFT),2),e.setAtLeastIfValid(t.getLocation(0,Vn.RIGHT),t.getLocation(1,Vn.RIGHT),2))},p(ni,ti),e(ni.prototype,{isIncidentEdgeInResult:function(){for(var t=this.getEdges().getEdges().iterator();t.hasNext();){if(t.next().getEdge().isInResult())return!0}return!1},isIsolated:function(){return 1===this._label.getGeometryCount()},getCoordinate:function(){return this._coord},print:function(t){t.println("node "+this._coord+" lbl: "+this._label)},computeIM:function(t){},computeMergedLocation:function(t,e){var n=ve.NONE;if(n=this._label.getLocation(e),!t.isNull(e)){var i=t.getLocation(e);n!==ve.BOUNDARY&&(n=i)}return n},setLabel:function(){if(2!==arguments.length||!Number.isInteger(arguments[1])||!Number.isInteger(arguments[0]))return ti.prototype.setLabel.apply(this,arguments);var t=arguments[0],e=arguments[1];null===this._label?this._label=new jn(t,e):this._label.setLocation(t,e)},getEdges:function(){return this._edges},mergeLabel:function(){if(arguments[0]instanceof ni){var t=arguments[0];this.mergeLabel(t._label)}else if(arguments[0]instanceof jn)for(var e=arguments[0],n=0;n<2;n++){var i=this.computeMergedLocation(e,n);this._label.getLocation(n)===ve.NONE&&this._label.setLocation(n,i)}},add:function(t){this._edges.insert(t),t.setNode(this)},setLabelBoundary:function(t){if(null===this._label)return null;var e=ve.NONE;null!==this._label&&(e=this._label.getLocation(t));var n=null;switch(e){case ve.BOUNDARY:n=ve.INTERIOR;break;case ve.INTERIOR:default:n=ve.BOUNDARY}this._label.setLocation(t,n)},interfaces_:function(){return[]},getClass:function(){return ni}}),e(ii.prototype,{find:function(t){return this.nodeMap.get(t)},addNode:function(){if(arguments[0]instanceof x){var t=arguments[0];return null===(e=this.nodeMap.get(t))&&(e=this.nodeFact.createNode(t),this.nodeMap.put(t,e)),e}if(arguments[0]instanceof ni){var e,n=arguments[0];return null===(e=this.nodeMap.get(n.getCoordinate()))?(this.nodeMap.put(n.getCoordinate(),n),n):(e.mergeLabel(n),e)}},print:function(t){for(var e=this.iterator();e.hasNext();){e.next().print(t)}},iterator:function(){return this.nodeMap.values().iterator()},values:function(){return this.nodeMap.values()},getBoundaryNodes:function(t){for(var e=new P,n=this.iterator();n.hasNext();){var i=n.next();i.getLabel().getLocation(t)===ve.BOUNDARY&&e.add(i)}return e},add:function(t){var e=t.getCoordinate();this.addNode(e).add(t)},interfaces_:function(){return[]},getClass:function(){return ii}}),e(ri.prototype,{compareDirection:function(t){return this._dx===t._dx&&this._dy===t._dy?0:this._quadrant>t._quadrant?1:this._quadrant<t._quadrant?-1:V.index(t._p0,t._p1,this._p1)},getDy:function(){return this._dy},getCoordinate:function(){return this._p0},setNode:function(t){this._node=t},print:function(t){var e=Math.atan2(this._dy,this._dx),n=this.getClass().getName(),i=n.lastIndexOf("."),r=n.substring(i+1);t.print("  "+r+": "+this._p0+" - "+this._p1+" "+this._quadrant+":"+e+"   "+this._label)},compareTo:function(t){var e=t;return this.compareDirection(e)},getDirectedCoordinate:function(){return this._p1},getDx:function(){return this._dx},getLabel:function(){return this._label},getEdge:function(){return this._edge},getQuadrant:function(){return this._quadrant},getNode:function(){return this._node},toString:function(){var t=Math.atan2(this._dy,this._dx),e=this.getClass().getName(),n=e.lastIndexOf(".");return"  "+e.substring(n+1)+": "+this._p0+" - "+this._p1+" "+this._quadrant+":"+t+"   "+this._label},computeLabel:function(t){},init:function(t,e){this._p0=t,this._p1=e,this._dx=e.x-t.x,this._dy=e.y-t.y,this._quadrant=xn.quadrant(this._dx,this._dy),y.isTrue(!(0===this._dx&&0===this._dy),"EdgeEnd with identical endpoints found")},interfaces_:function(){return[n]},getClass:function(){return ri}}),p(si,v),e(si.prototype,{getCoordinate:function(){return this.pt},interfaces_:function(){return[]},getClass:function(){return si}}),si.msgWithCoord=function(t,e){return null!==e?t+" [ "+e+" ]":t},p(oi,ri),e(oi.prototype,{getNextMin:function(){return this._nextMin},getDepth:function(t){return this._depth[t]},setVisited:function(t){this._isVisited=t},computeDirectedLabel:function(){this._label=new jn(this._edge.getLabel()),this._isForward||this._label.flip()},getNext:function(){return this._next},setDepth:function(t,e){if(-999!==this._depth[t]&&this._depth[t]!==e)throw new si("assigned depths do not match",this.getCoordinate());this._depth[t]=e},isInteriorAreaEdge:function(){for(var t=!0,e=0;e<2;e++)this._label.isArea(e)&&this._label.getLocation(e,Vn.LEFT)===ve.INTERIOR&&this._label.getLocation(e,Vn.RIGHT)===ve.INTERIOR||(t=!1);return t},setNextMin:function(t){this._nextMin=t},print:function(t){ri.prototype.print.call(this,t),t.print(" "+this._depth[Vn.LEFT]+"/"+this._depth[Vn.RIGHT]),t.print(" ("+this.getDepthDelta()+")"),this._isInResult&&t.print(" inResult")},setMinEdgeRing:function(t){this._minEdgeRing=t},isLineEdge:function(){var t=this._label.isLine(0)||this._label.isLine(1),e=!this._label.isArea(0)||this._label.allPositionsEqual(0,ve.EXTERIOR),n=!this._label.isArea(1)||this._label.allPositionsEqual(1,ve.EXTERIOR);return t&&e&&n},setEdgeRing:function(t){this._edgeRing=t},getMinEdgeRing:function(){return this._minEdgeRing},getDepthDelta:function(){var t=this._edge.getDepthDelta();return this._isForward||(t=-t),t},setInResult:function(t){this._isInResult=t},getSym:function(){return this._sym},isForward:function(){return this._isForward},getEdge:function(){return this._edge},printEdge:function(t){this.print(t),t.print(" "),this._isForward?this._edge.print(t):this._edge.printReverse(t)},setSym:function(t){this._sym=t},setVisitedEdge:function(t){this.setVisited(t),this._sym.setVisited(t)},setEdgeDepths:function(t,e){var n=this.getEdge().getDepthDelta();this._isForward||(n=-n);var i=1;t===Vn.LEFT&&(i=-1);var r=Vn.opposite(t),s=e+n*i;this.setDepth(t,e),this.setDepth(r,s)},getEdgeRing:function(){return this._edgeRing},isInResult:function(){return this._isInResult},setNext:function(t){this._next=t},isVisited:function(){return this._isVisited},interfaces_:function(){return[]},getClass:function(){return oi}}),oi.depthFactor=function(t,e){return t===ve.EXTERIOR&&e===ve.INTERIOR?1:t===ve.INTERIOR&&e===ve.EXTERIOR?-1:0},e(ai.prototype,{createNode:function(t){return new ni(t,null)},interfaces_:function(){return[]},getClass:function(){return ai}}),e(ui.prototype,{printEdges:function(t){t.println("Edges:");for(var e=0;e<this._edges.size();e++){t.println("edge "+e+":");var n=this._edges.get(e);n.print(t),n.eiList.print(t)}},find:function(t){return this._nodes.find(t)},addNode:function(){if(arguments[0]instanceof ni){var t=arguments[0];return this._nodes.addNode(t)}if(arguments[0]instanceof x){var e=arguments[0];return this._nodes.addNode(e)}},getNodeIterator:function(){return this._nodes.iterator()},linkResultDirectedEdges:function(){for(var t=this._nodes.iterator();t.hasNext();){t.next().getEdges().linkResultDirectedEdges()}},debugPrintln:function(t){Y.out.println(t)},isBoundaryNode:function(t,e){var n=this._nodes.find(e);if(null===n)return!1;var i=n.getLabel();return null!==i&&i.getLocation(t)===ve.BOUNDARY},linkAllDirectedEdges:function(){for(var t=this._nodes.iterator();t.hasNext();){t.next().getEdges().linkAllDirectedEdges()}},matchInSameDirection:function(t,e,n,i){return!!t.equals(n)&&(V.index(t,e,i)===V.COLLINEAR&&xn.quadrant(t,e)===xn.quadrant(n,i))},getEdgeEnds:function(){return this._edgeEndList},debugPrint:function(t){Y.out.print(t)},getEdgeIterator:function(){return this._edges.iterator()},findEdgeInSameDirection:function(t,e){for(var n=0;n<this._edges.size();n++){var i=this._edges.get(n),r=i.getCoordinates();if(this.matchInSameDirection(t,e,r[0],r[1]))return i;if(this.matchInSameDirection(t,e,r[r.length-1],r[r.length-2]))return i}return null},insertEdge:function(t){this._edges.add(t)},findEdgeEnd:function(t){for(var e=this.getEdgeEnds().iterator();e.hasNext();){var n=e.next();if(n.getEdge()===t)return n}return null},addEdges:function(t){for(var e=t.iterator();e.hasNext();){var n=e.next();this._edges.add(n);var i=new oi(n,!0),r=new oi(n,!1);i.setSym(r),r.setSym(i),this.add(i),this.add(r)}},add:function(t){this._nodes.add(t),this._edgeEndList.add(t)},getNodes:function(){return this._nodes.values()},findEdge:function(t,e){for(var n=0;n<this._edges.size();n++){var i=this._edges.get(n),r=i.getCoordinates();if(t.equals(r[0])&&e.equals(r[1]))return i}return null},interfaces_:function(){return[]},getClass:function(){return ui}}),ui.linkResultDirectedEdges=function(t){for(var e=t.iterator();e.hasNext();){e.next().getEdges().linkResultDirectedEdges()}},p(li,ui),e(li.prototype,{insertBoundaryPoint:function(t,e){var n=this._nodes.addNode(e).getLabel(),i=1;n.getLocation(t,Vn.ON)===ve.BOUNDARY&&i++;var r=li.determineBoundary(this._boundaryNodeRule,i);n.setLocation(t,r)},computeSelfNodes:function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];return this.computeSelfNodes(t,e,!1)}if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2],s=new Xn(n,!0,!1);s.setIsDoneIfProperInt(r);var o=this.createEdgeSetIntersector(),a=this._parentGeom instanceof Ht||this._parentGeom instanceof Ut||this._parentGeom instanceof Wt,u=i||!a;return o.computeIntersections(this._edges,s,u),this.addSelfIntersectionNodes(this._argIndex),s}},computeSplitEdges:function(t){for(var e=this._edges.iterator();e.hasNext();){e.next().eiList.addSplitEdges(t)}},computeEdgeIntersections:function(t,e,n){var i=new Xn(e,n,!0);return i.setBoundaryNodes(this.getBoundaryNodes(),t.getBoundaryNodes()),this.createEdgeSetIntersector().computeIntersections(this._edges,t._edges,i),i},getGeometry:function(){return this._parentGeom},getBoundaryNodeRule:function(){return this._boundaryNodeRule},hasTooFewPoints:function(){return this._hasTooFewPoints},addPoint:function(){if(arguments[0]instanceof Vt){var t=arguments[0].getCoordinate();this.insertPoint(this._argIndex,t,ve.INTERIOR)}else if(arguments[0]instanceof x){var e=arguments[0];this.insertPoint(this._argIndex,e,ve.INTERIOR)}},addPolygon:function(t){this.addPolygonRing(t.getExteriorRing(),ve.EXTERIOR,ve.INTERIOR);for(var e=0;e<t.getNumInteriorRing();e++){var n=t.getInteriorRingN(e);this.addPolygonRing(n,ve.INTERIOR,ve.EXTERIOR)}},addEdge:function(t){this.insertEdge(t);var e=t.getCoordinates();this.insertPoint(this._argIndex,e[0],ve.BOUNDARY),this.insertPoint(this._argIndex,e[e.length-1],ve.BOUNDARY)},addLineString:function(t){var e=nt.removeRepeatedPoints(t.getCoordinates());if(e.length<2)return this._hasTooFewPoints=!0,this._invalidPoint=e[0],null;var n=new ei(e,new jn(this._argIndex,ve.INTERIOR));this._lineEdgeMap.put(t,n),this.insertEdge(n),y.isTrue(2<=e.length,"found LineString with single point"),this.insertBoundaryPoint(this._argIndex,e[0]),this.insertBoundaryPoint(this._argIndex,e[e.length-1])},getInvalidPoint:function(){return this._invalidPoint},getBoundaryPoints:function(){for(var t=this.getBoundaryNodes(),e=new Array(t.size()).fill(null),n=0,i=t.iterator();i.hasNext();){var r=i.next();e[n++]=r.getCoordinate().copy()}return e},getBoundaryNodes:function(){return null===this._boundaryNodes&&(this._boundaryNodes=this._nodes.getBoundaryNodes(this._argIndex)),this._boundaryNodes},addSelfIntersectionNode:function(t,e,n){if(this.isBoundaryNode(t,e))return null;n===ve.BOUNDARY&&this._useBoundaryDeterminationRule?this.insertBoundaryPoint(t,e):this.insertPoint(t,e,n)},addPolygonRing:function(t,e,n){if(t.isEmpty())return null;var i=nt.removeRepeatedPoints(t.getCoordinates());if(i.length<4)return this._hasTooFewPoints=!0,this._invalidPoint=i[0],null;var r=e,s=n;V.isCCW(i)&&(r=n,s=e);var o=new ei(i,new jn(this._argIndex,ve.BOUNDARY,r,s));this._lineEdgeMap.put(t,o),this.insertEdge(o),this.insertPoint(this._argIndex,i[0],ve.BOUNDARY)},insertPoint:function(t,e,n){var i=this._nodes.addNode(e),r=i.getLabel();null===r?i._label=new jn(t,n):r.setLocation(t,n)},createEdgeSetIntersector:function(){return new Hn},addSelfIntersectionNodes:function(t){for(var e=this._edges.iterator();e.hasNext();)for(var n=e.next(),i=n.getLabel().getLocation(t),r=n.eiList.iterator();r.hasNext();){var s=r.next();this.addSelfIntersectionNode(t,s.coord,i)}},add:function(){if(!(1===arguments.length&&arguments[0]instanceof K))return ui.prototype.add.apply(this,arguments);var t=arguments[0];if(t.isEmpty())return null;if(t instanceof Wt&&(this._useBoundaryDeterminationRule=!1),t instanceof Ut)this.addPolygon(t);else if(t instanceof Bt)this.addLineString(t);else if(t instanceof Vt)this.addPoint(t);else if(t instanceof Xt)this.addCollection(t);else if(t instanceof wt)this.addCollection(t);else if(t instanceof Wt)this.addCollection(t);else{if(!(t instanceof Lt))throw new UnsupportedOperationException(t.getClass().getName());this.addCollection(t)}},addCollection:function(t){for(var e=0;e<t.getNumGeometries();e++){var n=t.getGeometryN(e);this.add(n)}},locate:function(t){return L(this._parentGeom,kt)&&50<this._parentGeom.getNumGeometries()?(null===this._areaPtLocator&&(this._areaPtLocator=new ze(this._parentGeom)),this._areaPtLocator.locate(t)):this._ptLocator.locate(t,this._parentGeom)},findEdge:function(){if(1===arguments.length&&arguments[0]instanceof Bt){var t=arguments[0];return this._lineEdgeMap.get(t)}return ui.prototype.findEdge.apply(this,arguments)},interfaces_:function(){return[]},getClass:function(){return li}}),li.determineBoundary=function(t,e){return t.isInBoundary(e)?ve.BOUNDARY:ve.INTERIOR};var hi=Object.freeze({GeometryGraph:li});function ci(){}function fi(){if(this._p=null,this._data=null,this._left=null,this._right=null,this._count=null,2===arguments.length){var t=arguments[0],e=arguments[1];this._p=new x(t),this._left=null,this._right=null,this._count=1,this._data=e}else if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];this._p=new x(n,i),this._left=null,this._right=null,this._count=1,this._data=r}}function gi(){if(this._root=null,this._numberOfNodes=null,this._tolerance=null,0===arguments.length)gi.call(this,0);else if(1===arguments.length){var t=arguments[0];this._tolerance=t}}function di(){this._tolerance=null,this._matchNode=null,this._matchDist=0,this._p=null;var t=arguments[0],e=arguments[1];this._p=t,this._tolerance=e}e(ci.prototype,{visit:function(t){},interfaces_:function(){return[]},getClass:function(){return ci}}),e(fi.prototype,{isRepeated:function(){return 1<this._count},getRight:function(){return this._right},getCoordinate:function(){return this._p},setLeft:function(t){this._left=t},getX:function(){return this._p.x},getData:function(){return this._data},getCount:function(){return this._count},getLeft:function(){return this._left},getY:function(){return this._p.y},increment:function(){this._count=this._count+1},setRight:function(t){this._right=t},interfaces_:function(){return[]},getClass:function(){return fi}}),e(gi.prototype,{insert:function(){if(1===arguments.length){var t=arguments[0];return this.insert(t,null)}if(2===arguments.length){var e=arguments[0],n=arguments[1];if(null===this._root)return this._root=new fi(e,n),this._root;if(0<this._tolerance){var i=this.findBestMatchNode(e);if(null!==i)return i.increment(),i}return this.insertExact(e,n)}},query:function(){if(1===arguments.length){var t=arguments[0],e=new P;return this.query(t,e),e}if(2===arguments.length)if(arguments[0]instanceof M&&L(arguments[1],w)){var n=arguments[0],i=arguments[1];this.queryNode(this._root,n,!0,{interfaces_:function(){return[ci]},visit:function(t){i.add(t)}})}else if(arguments[0]instanceof M&&L(arguments[1],ci)){var r=arguments[0],s=arguments[1];this.queryNode(this._root,r,!0,s)}},queryNode:function(t,e,n,i){if(null===t)return null;var r=null,s=null,o=null;n?(r=e.getMinX(),s=e.getMaxX(),o=t.getX()):(r=e.getMinY(),s=e.getMaxY(),o=t.getY());var a=o<=s;r<o&&this.queryNode(t.getLeft(),e,!n,i),e.contains(t.getCoordinate())&&i.visit(t),a&&this.queryNode(t.getRight(),e,!n,i)},findBestMatchNode:function(t){var e=new di(t,this._tolerance);return this.query(e.queryEnvelope(),e),e.getNode()},isEmpty:function(){return null===this._root},insertExact:function(t,e){for(var n=this._root,i=this._root,r=!0,s=!0;null!==n;){if(null!==n)if(t.distance(n.getCoordinate())<=this._tolerance)return n.increment(),n;s=r?t.x<n.getX():t.y<n.getY(),i=n,n=s?n.getLeft():n.getRight(),r=!r}this._numberOfNodes=this._numberOfNodes+1;var o=new fi(t,e);return s?i.setLeft(o):i.setRight(o),o},interfaces_:function(){return[]},getClass:function(){return gi}}),gi.toCoordinates=function(){if(1===arguments.length){var t=arguments[0];return gi.toCoordinates(t,!1)}if(2===arguments.length){for(var e=arguments[0],n=arguments[1],i=new b,r=e.iterator();r.hasNext();)for(var s=r.next(),o=n?s.getCount():1,a=0;a<o;a++)i.add(s.getCoordinate(),!0);return i.toCoordinateArray()}},e(di.prototype,{visit:function(t){var e=this._p.distance(t.getCoordinate());if(!(e<=this._tolerance))return null;var n=!1;(null===this._matchNode||e<this._matchDist||null!==this._matchNode&&e===this._matchDist&&t.getCoordinate().compareTo(this._matchNode.getCoordinate())<1)&&(n=!0),n&&(this._matchNode=t,this._matchDist=e)},queryEnvelope:function(){var t=new M(this._p);return t.expandBy(this._tolerance),t},getNode:function(){return this._matchNode},interfaces_:function(){return[ci]},getClass:function(){return di}}),gi.BestMatchVisitor=di;var _i=Object.freeze({KdTree:gi});function pi(){this._items=new P,this._subnode=new Array(4).fill(null)}function mi(){this._pt=new x,this._level=0,this._env=null;var t=arguments[0];this.computeKey(t)}function vi(){pi.apply(this),this._env=null,this._centrex=null,this._centrey=null,this._level=null;var t=arguments[0],e=arguments[1];this._env=t,this._level=e,this._centrex=(t.getMinX()+t.getMaxX())/2,this._centrey=(t.getMinY()+t.getMaxY())/2}function yi(){pi.apply(this)}function xi(){}function Ei(){this._root=null,this._minExtent=1,this._root=new yi}e(pi.prototype,{hasChildren:function(){for(var t=0;t<4;t++)if(null!==this._subnode[t])return!0;return!1},isPrunable:function(){return!(this.hasChildren()||this.hasItems())},addAllItems:function(t){t.addAll(this._items);for(var e=0;e<4;e++)null!==this._subnode[e]&&this._subnode[e].addAllItems(t);return t},getNodeCount:function(){for(var t=0,e=0;e<4;e++)null!==this._subnode[e]&&(t+=this._subnode[e].size());return t+1},size:function(){for(var t=0,e=0;e<4;e++)null!==this._subnode[e]&&(t+=this._subnode[e].size());return t+this._items.size()},addAllItemsFromOverlapping:function(t,e){if(!this.isSearchMatch(t))return null;e.addAll(this._items);for(var n=0;n<4;n++)null!==this._subnode[n]&&this._subnode[n].addAllItemsFromOverlapping(t,e)},visitItems:function(t,e){for(var n=this._items.iterator();n.hasNext();)e.visitItem(n.next())},hasItems:function(){return!this._items.isEmpty()},remove:function(t,e){if(!this.isSearchMatch(t))return!1;for(var n=!1,i=0;i<4;i++)if(null!==this._subnode[i]&&(n=this._subnode[i].remove(t,e))){this._subnode[i].isPrunable()&&(this._subnode[i]=null);break}return n||(n=this._items.remove(e))},visit:function(t,e){if(!this.isSearchMatch(t))return null;this.visitItems(t,e);for(var n=0;n<4;n++)null!==this._subnode[n]&&this._subnode[n].visit(t,e)},getItems:function(){return this._items},depth:function(){for(var t=0,e=0;e<4;e++)if(null!==this._subnode[e]){var n=this._subnode[e].depth();t<n&&(t=n)}return t+1},isEmpty:function(){var t=!0;if(this._items.isEmpty()){for(var e=0;e<4;e++)if(null!==this._subnode[e]&&!this._subnode[e].isEmpty()){t=!1;break}}else t=!1;return t},add:function(t){this._items.add(t)},interfaces_:function(){return[l]},getClass:function(){return pi}}),pi.getSubnodeIndex=function(t,e,n){var i=-1;return t.getMinX()>=e&&(t.getMinY()>=n&&(i=3),t.getMaxY()<=n&&(i=1)),t.getMaxX()<=e&&(t.getMinY()>=n&&(i=2),t.getMaxY()<=n&&(i=0)),i},e(mi.prototype,{getLevel:function(){return this._level},computeKey:function(){if(1===arguments.length){var t=arguments[0];for(this._level=mi.computeQuadLevel(t),this._env=new M,this.computeKey(this._level,t);!this._env.contains(t);)this._level+=1,this.computeKey(this._level,t)}else if(2===arguments.length){var e=arguments[0],n=arguments[1],i=gn.powerOf2(e);this._pt.x=Math.floor(n.getMinX()/i)*i,this._pt.y=Math.floor(n.getMinY()/i)*i,this._env.init(this._pt.x,this._pt.x+i,this._pt.y,this._pt.y+i)}},getEnvelope:function(){return this._env},getCentre:function(){return new x((this._env.getMinX()+this._env.getMaxX())/2,(this._env.getMinY()+this._env.getMaxY())/2)},getPoint:function(){return this._pt},interfaces_:function(){return[]},getClass:function(){return mi}}),mi.computeQuadLevel=function(t){var e=t.getWidth(),n=t.getHeight();return gn.exponent(n<e?e:n)+1},p(vi,pi),e(vi.prototype,{find:function(t){var e=pi.getSubnodeIndex(t,this._centrex,this._centrey);return-1===e?this:null!==this._subnode[e]?this._subnode[e].find(t):this},isSearchMatch:function(t){return null!==t&&this._env.intersects(t)},getSubnode:function(t){return null===this._subnode[t]&&(this._subnode[t]=this.createSubnode(t)),this._subnode[t]},getEnvelope:function(){return this._env},getNode:function(t){var e=pi.getSubnodeIndex(t,this._centrex,this._centrey);return-1!==e?this.getSubnode(e).getNode(t):this},createSubnode:function(t){var e=0,n=0,i=0,r=0;switch(t){case 0:e=this._env.getMinX(),n=this._centrex,i=this._env.getMinY(),r=this._centrey;break;case 1:e=this._centrex,n=this._env.getMaxX(),i=this._env.getMinY(),r=this._centrey;break;case 2:e=this._env.getMinX(),n=this._centrex,i=this._centrey,r=this._env.getMaxY();break;case 3:e=this._centrex,n=this._env.getMaxX(),i=this._centrey,r=this._env.getMaxY()}return new vi(new M(e,n,i,r),this._level-1)},insertNode:function(t){y.isTrue(null===this._env||this._env.contains(t._env));var e=pi.getSubnodeIndex(t._env,this._centrex,this._centrey);if(t._level===this._level-1)this._subnode[e]=t;else{var n=this.createSubnode(e);n.insertNode(t),this._subnode[e]=n}},interfaces_:function(){return[]},getClass:function(){return vi}}),vi.createNode=function(t){var e=new mi(t);return new vi(e.getEnvelope(),e.getLevel())},vi.createExpanded=function(t,e){var n=new M(e);null!==t&&n.expandToInclude(t._env);var i=vi.createNode(n);return null!==t&&i.insertNode(t),i},p(yi,pi),e(yi.prototype,{insert:function(t,e){var n=pi.getSubnodeIndex(t,yi.origin.x,yi.origin.y);if(-1===n)return this.add(e),null;var i=this._subnode[n];if(null===i||!i.getEnvelope().contains(t)){var r=vi.createExpanded(i,t);this._subnode[n]=r}this.insertContained(this._subnode[n],t,e)},isSearchMatch:function(t){return!0},insertContained:function(t,e,n){y.isTrue(t.getEnvelope().contains(e));var i=pn.isZeroWidth(e.getMinX(),e.getMaxX()),r=pn.isZeroWidth(e.getMinY(),e.getMaxY());(i||r?t.find(e):t.getNode(e)).add(n)},interfaces_:function(){return[]},getClass:function(){return yi}}),yi.origin=new x(0,0),e(xi.prototype,{insert:function(t,e){},remove:function(t,e){},query:function(){},interfaces_:function(){return[]},getClass:function(){return xi}}),e(Ei.prototype,{size:function(){return null!==this._root?this._root.size():0},insert:function(t,e){this.collectStats(t);var n=Ei.ensureExtent(t,this._minExtent);this._root.insert(n,e)},query:function(){if(1===arguments.length){var t=arguments[0],e=new qe;return this.query(t,e),e.getItems()}if(2===arguments.length){var n=arguments[0],i=arguments[1];this._root.visit(n,i)}},queryAll:function(){var t=new P;return this._root.addAllItems(t),t},remove:function(t,e){var n=Ei.ensureExtent(t,this._minExtent);return this._root.remove(n,e)},collectStats:function(t){var e=t.getWidth();e<this._minExtent&&0<e&&(this._minExtent=e);var n=t.getHeight();n<this._minExtent&&0<n&&(this._minExtent=n)},depth:function(){return null!==this._root?this._root.depth():0},isEmpty:function(){return null===this._root||this._root.isEmpty()},interfaces_:function(){return[xi,l]},getClass:function(){return Ei}}),Ei.ensureExtent=function(t,e){var n=t.getMinX(),i=t.getMaxX(),r=t.getMinY(),s=t.getMaxY();return n!==i&&r!==s?t:(n===i&&(i=(n-=e/2)+e/2),r===s&&(s=(r-=e/2)+e/2),new M(n,i,r,s))},Ei.serialVersionUID=-0x678b60c967a25400;var Ii=Object.freeze({Quadtree:Ei});function Ni(){}function Ci(){this._bounds=null,this._item=null;var t=arguments[0],e=arguments[1];this._bounds=t,this._item=e}function Si(){this._size=null,this._items=null,this._size=0,this._items=new P,this._items.add(null)}function Li(){if(this._childBoundables=new P,this._bounds=null,this._level=null,0===arguments.length);else if(1===arguments.length){var t=arguments[0];this._level=t}}function wi(){this._boundable1=null,this._boundable2=null,this._distance=null,this._itemDistance=null;var t=arguments[0],e=arguments[1],n=arguments[2];this._boundable1=t,this._boundable2=e,this._itemDistance=n,this._distance=this.distance()}function Ri(){if(this._root=null,this._built=!1,this._itemBoundables=new P,this._nodeCapacity=null,0===arguments.length)Ri.call(this,Ri.DEFAULT_NODE_CAPACITY);else if(1===arguments.length){var t=arguments[0];y.isTrue(1<t,"Node capacity must be greater than 1"),this._nodeCapacity=t}}function Ti(){}function Pi(){if(0===arguments.length)Pi.call(this,Pi.DEFAULT_NODE_CAPACITY);else if(1===arguments.length){var t=arguments[0];Ri.call(this,t)}}function Oi(){var t=arguments[0];Li.call(this,t)}e(Ni.prototype,{getBounds:function(){},interfaces_:function(){return[]},getClass:function(){return Ni}}),e(Ci.prototype,{getItem:function(){return this._item},getBounds:function(){return this._bounds},interfaces_:function(){return[Ni,l]},getClass:function(){return Ci}}),e(Si.prototype,{poll:function(){if(this.isEmpty())return null;var t=this._items.get(1);return this._items.set(1,this._items.get(this._size)),this._size-=1,this.reorder(1),t},size:function(){return this._size},reorder:function(t){for(var e=null,n=this._items.get(t);2*t<=this._size&&((e=2*t)!==this._size&&this._items.get(e+1).compareTo(this._items.get(e))<0&&e++,this._items.get(e).compareTo(n)<0);t=e)this._items.set(t,this._items.get(e));this._items.set(t,n)},clear:function(){this._size=0,this._items.clear()},peek:function(){return this.isEmpty()?null:this._items.get(1)},isEmpty:function(){return 0===this._size},add:function(t){this._items.add(null),this._size+=1;var e=this._size;for(this._items.set(0,t);t.compareTo(this._items.get(Math.trunc(e/2)))<0;e/=2)this._items.set(e,this._items.get(Math.trunc(e/2)));this._items.set(e,t)},interfaces_:function(){return[]},getClass:function(){return Si}}),e(Li.prototype,{getLevel:function(){return this._level},size:function(){return this._childBoundables.size()},getChildBoundables:function(){return this._childBoundables},addChildBoundable:function(t){y.isTrue(null===this._bounds),this._childBoundables.add(t)},isEmpty:function(){return this._childBoundables.isEmpty()},getBounds:function(){return null===this._bounds&&(this._bounds=this.computeBounds()),this._bounds},interfaces_:function(){return[Ni,l]},getClass:function(){return Li}}),Li.serialVersionUID=0x5a1e55ec41369800,e(wi.prototype,{expandToQueue:function(t,e){var n=wi.isComposite(this._boundable1),i=wi.isComposite(this._boundable2);if(n&&i)return wi.area(this._boundable1)>wi.area(this._boundable2)?this.expand(this._boundable1,this._boundable2,t,e):this.expand(this._boundable2,this._boundable1,t,e),null;if(n)return this.expand(this._boundable1,this._boundable2,t,e),null;if(i)return this.expand(this._boundable2,this._boundable1,t,e),null;throw new c("neither boundable is composite")},isLeaves:function(){return!(wi.isComposite(this._boundable1)||wi.isComposite(this._boundable2))},compareTo:function(t){var e=t;return this._distance<e._distance?-1:this._distance>e._distance?1:0},expand:function(t,e,n,i){for(var r=t.getChildBoundables().iterator();r.hasNext();){var s=new wi(r.next(),e,this._itemDistance);s.getDistance()<i&&n.add(s)}},getBoundable:function(t){return 0===t?this._boundable1:this._boundable2},getDistance:function(){return this._distance},distance:function(){return this.isLeaves()?this._itemDistance.distance(this._boundable1,this._boundable2):this._boundable1.getBounds().distance(this._boundable2.getBounds())},interfaces_:function(){return[n]},getClass:function(){return wi}}),wi.area=function(t){return t.getBounds().getArea()},wi.isComposite=function(t){return t instanceof Li},e(Ri.prototype,{queryInternal:function(){if(L(arguments[2],Te)&&arguments[0]instanceof Object&&arguments[1]instanceof Li)for(var t=arguments[0],e=arguments[1],n=arguments[2],i=e.getChildBoundables(),r=0;r<i.size();r++){var s=i.get(r);this.getIntersectsOp().intersects(s.getBounds(),t)&&(s instanceof Li?this.queryInternal(t,s,n):s instanceof Ci?n.visitItem(s.getItem()):y.shouldNeverReachHere())}else if(L(arguments[2],w)&&arguments[0]instanceof Object&&arguments[1]instanceof Li){var o=arguments[0],a=arguments[1],u=arguments[2];for(i=a.getChildBoundables(),r=0;r<i.size();r++){s=i.get(r);this.getIntersectsOp().intersects(s.getBounds(),o)&&(s instanceof Li?this.queryInternal(o,s,u):s instanceof Ci?u.add(s.getItem()):y.shouldNeverReachHere())}}},getNodeCapacity:function(){return this._nodeCapacity},lastNode:function(t){return t.get(t.size()-1)},size:function(){if(0===arguments.length)return this.isEmpty()?0:(this.build(),this.size(this._root));if(1===arguments.length){for(var t=0,e=arguments[0].getChildBoundables().iterator();e.hasNext();){var n=e.next();n instanceof Li?t+=this.size(n):n instanceof Ci&&(t+=1)}return t}},removeItem:function(t,e){for(var n=null,i=t.getChildBoundables().iterator();i.hasNext();){var r=i.next();r instanceof Ci&&r.getItem()===e&&(n=r)}return null!==n&&(t.getChildBoundables().remove(n),!0)},itemsTree:function(){if(0===arguments.length){this.build();var t=this.itemsTree(this._root);return null===t?new P:t}if(1===arguments.length){for(var e=arguments[0],n=new P,i=e.getChildBoundables().iterator();i.hasNext();){var r=i.next();if(r instanceof Li){var s=this.itemsTree(r);null!==s&&n.add(s)}else r instanceof Ci?n.add(r.getItem()):y.shouldNeverReachHere()}return n.size()<=0?null:n}},insert:function(t,e){y.isTrue(!this._built,"Cannot insert items into an STR packed R-tree after it has been built."),this._itemBoundables.add(new Ci(t,e))},boundablesAtLevel:function(){if(1===arguments.length){var t=arguments[0],e=new P;return this.boundablesAtLevel(t,this._root,e),e}if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];if(y.isTrue(-2<n),i.getLevel()===n)return r.add(i),null;for(var s=i.getChildBoundables().iterator();s.hasNext();){var o=s.next();o instanceof Li?this.boundablesAtLevel(n,o,r):(y.isTrue(o instanceof Ci),-1===n&&r.add(o))}return null}},query:function(){if(1===arguments.length){var t=arguments[0];this.build();var e=new P;return this.isEmpty()||this.getIntersectsOp().intersects(this._root.getBounds(),t)&&this.queryInternal(t,this._root,e),e}if(2===arguments.length){var n=arguments[0],i=arguments[1];if(this.build(),this.isEmpty())return null;this.getIntersectsOp().intersects(this._root.getBounds(),n)&&this.queryInternal(n,this._root,i)}},build:function(){if(this._built)return null;this._root=this._itemBoundables.isEmpty()?this.createNode(0):this.createHigherLevels(this._itemBoundables,-1),this._itemBoundables=null,this._built=!0},getRoot:function(){return this.build(),this._root},remove:function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];return this.build(),!!this.getIntersectsOp().intersects(this._root.getBounds(),t)&&this.remove(t,this._root,e)}if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2],s=this.removeItem(i,r);if(s)return!0;for(var o=null,a=i.getChildBoundables().iterator();a.hasNext();){var u=a.next();if(this.getIntersectsOp().intersects(u.getBounds(),n)&&(u instanceof Li&&(s=this.remove(n,u,r)))){o=u;break}}return null!==o&&o.getChildBoundables().isEmpty()&&i.getChildBoundables().remove(o),s}},createHigherLevels:function(t,e){y.isTrue(!t.isEmpty());var n=this.createParentBoundables(t,e+1);return 1===n.size()?n.get(0):this.createHigherLevels(n,e+1)},depth:function(){if(0===arguments.length)return this.isEmpty()?0:(this.build(),this.depth(this._root));if(1===arguments.length){for(var t=0,e=arguments[0].getChildBoundables().iterator();e.hasNext();){var n=e.next();if(n instanceof Li){var i=this.depth(n);t<i&&(t=i)}}return t+1}},createParentBoundables:function(t,e){y.isTrue(!t.isEmpty());var n=new P;n.add(this.createNode(e));var i=new P(t);De.sort(i,this.getComparator());for(var r=i.iterator();r.hasNext();){var s=r.next();this.lastNode(n).getChildBoundables().size()===this.getNodeCapacity()&&n.add(this.createNode(e)),this.lastNode(n).addChildBoundable(s)}return n},isEmpty:function(){return this._built?this._root.isEmpty():this._itemBoundables.isEmpty()},interfaces_:function(){return[l]},getClass:function(){return Ri}}),Ri.compareDoubles=function(t,e){return e<t?1:t<e?-1:0},Ri.IntersectsOp=function(){},Ri.serialVersionUID=-0x35ef64c82d4c5400,Ri.DEFAULT_NODE_CAPACITY=10,e(Ti.prototype,{distance:function(t,e){},interfaces_:function(){return[]},getClass:function(){return Ti}}),p(Pi,Ri),e(Pi.prototype,{createParentBoundablesFromVerticalSlices:function(t,e){y.isTrue(0<t.length);for(var n=new P,i=0;i<t.length;i++)n.addAll(this.createParentBoundablesFromVerticalSlice(t[i],e));return n},createNode:function(t){return new Oi(t)},size:function(){return 0===arguments.length?Ri.prototype.size.call(this):Ri.prototype.size.apply(this,arguments)},insert:function(){if(!(2===arguments.length&&arguments[1]instanceof Object&&arguments[0]instanceof M))return Ri.prototype.insert.apply(this,arguments);var t=arguments[0],e=arguments[1];if(t.isNull())return null;Ri.prototype.insert.call(this,t,e)},getIntersectsOp:function(){return Pi.intersectsOp},verticalSlices:function(t,e){for(var n=Math.trunc(Math.ceil(t.size()/e)),i=new Array(e).fill(null),r=t.iterator(),s=0;s<e;s++){i[s]=new P;for(var o=0;r.hasNext()&&o<n;){var a=r.next();i[s].add(a),o++}}return i},query:function(){if(1===arguments.length){var t=arguments[0];return Ri.prototype.query.call(this,t)}if(2===arguments.length){var e=arguments[0],n=arguments[1];Ri.prototype.query.call(this,e,n)}},getComparator:function(){return Pi.yComparator},createParentBoundablesFromVerticalSlice:function(t,e){return Ri.prototype.createParentBoundables.call(this,t,e)},remove:function(){if(2===arguments.length&&arguments[1]instanceof Object&&arguments[0]instanceof M){var t=arguments[0],e=arguments[1];return Ri.prototype.remove.call(this,t,e)}return Ri.prototype.remove.apply(this,arguments)},depth:function(){return 0===arguments.length?Ri.prototype.depth.call(this):Ri.prototype.depth.apply(this,arguments)},createParentBoundables:function(t,e){y.isTrue(!t.isEmpty());var n=Math.trunc(Math.ceil(t.size()/this.getNodeCapacity())),i=new P(t);De.sort(i,Pi.xComparator);var r=this.verticalSlices(i,Math.trunc(Math.ceil(Math.sqrt(n))));return this.createParentBoundablesFromVerticalSlices(r,e)},nearestNeighbour:function(){if(1===arguments.length){if(L(arguments[0],Ti)){var t=arguments[0],e=new wi(this.getRoot(),this.getRoot(),t);return this.nearestNeighbour(e)}if(arguments[0]instanceof wi){var n=arguments[0];return this.nearestNeighbour(n,S.POSITIVE_INFINITY)}}else if(2===arguments.length){if(arguments[0]instanceof Pi&&L(arguments[1],Ti)){var i=arguments[0],r=arguments[1];e=new wi(this.getRoot(),i.getRoot(),r);return this.nearestNeighbour(e)}if(arguments[0]instanceof wi&&"number"==typeof arguments[1]){var s=arguments[0],o=arguments[1],a=null;for((d=new Si).add(s);!d.isEmpty()&&0<o;){if(o<=(x=(y=d.poll()).getDistance()))break;y.isLeaves()?(o=x,a=y):y.expandToQueue(d,o)}return[a.getBoundable(0).getItem(),a.getBoundable(1).getItem()]}if(arguments[0]instanceof wi&&Number.isInteger(arguments[1])){var u=arguments[0],l=arguments[1];return this.nearestNeighbour(u,S.POSITIVE_INFINITY,l)}}else if(3===arguments.length){if(L(arguments[2],Ti)&&arguments[0]instanceof M&&arguments[1]instanceof Object){var h=arguments[0],c=arguments[1],f=arguments[2],g=new Ci(h,c);e=new wi(this.getRoot(),g,f);return this.nearestNeighbour(e)[0]}if(Number.isInteger(arguments[2])&&arguments[0]instanceof wi&&"number"==typeof arguments[1]){var d,_=arguments[0],p=arguments[1],m=arguments[2];o=p;(d=new Si).add(_);for(var v=new Si;!d.isEmpty()&&0<=o;){var y,x;if(o<=(x=(y=d.poll()).getDistance()))break;if(y.isLeaves())if(v.size()<m)v.add(y);else v.peek().getDistance()>x&&(v.poll(),v.add(y)),o=v.peek().getDistance();else y.expandToQueue(d,o)}return Pi.getItems(v)}}else if(4===arguments.length){var E=arguments[0],I=arguments[1],N=arguments[2],C=arguments[3];g=new Ci(E,I),e=new wi(this.getRoot(),g,N);return this.nearestNeighbour(e,C)}},interfaces_:function(){return[xi,l]},getClass:function(){return Pi}}),Pi.centreX=function(t){return Pi.avg(t.getMinX(),t.getMaxX())},Pi.avg=function(t,e){return(t+e)/2},Pi.getItems=function(t){for(var e=new Array(t.size()).fill(null),n=0;!t.isEmpty();){var i=t.poll();e[n]=i.getBoundable(0).getItem(),n++}return e},Pi.centreY=function(t){return Pi.avg(t.getMinY(),t.getMaxY())},p(Oi,Li),e(Oi.prototype,{computeBounds:function(){for(var t=null,e=this.getChildBoundables().iterator();e.hasNext();){var n=e.next();null===t?t=new M(n.getBounds()):t.expandToInclude(n.getBounds())}return t},interfaces_:function(){return[]},getClass:function(){return Oi}}),Pi.STRtreeNode=Oi,Pi.serialVersionUID=0x39920f7d5f261e0,Pi.xComparator={interfaces_:function(){return[u]},compare:function(t,e){return Ri.compareDoubles(Pi.centreX(t.getBounds()),Pi.centreX(e.getBounds()))}},Pi.yComparator={interfaces_:function(){return[u]},compare:function(t,e){return Ri.compareDoubles(Pi.centreY(t.getBounds()),Pi.centreY(e.getBounds()))}},Pi.intersectsOp={interfaces_:function(){return[IntersectsOp]},intersects:function(t,e){return t.intersects(e)}},Pi.DEFAULT_NODE_CAPACITY=10;var bi=Object.freeze({STRtree:Pi}),Mi=Object.freeze({kdtree:_i,quadtree:Ii,strtree:bi}),Di=["Point","MultiPoint","LineString","MultiLineString","Polygon","MultiPolygon"];function Ai(t){this.geometryFactory=t||new le}e(Ai.prototype,{read:function(t){var e=void 0,n=(e="string"==typeof t?JSON.parse(t):t).type;if(!Fi[n])throw new Error("Unknown GeoJSON type: "+e.type);return-1!==Di.indexOf(n)?Fi[n].call(this,e.coordinates):"GeometryCollection"===n?Fi[n].call(this,e.geometries):Fi[n].call(this,e)},write:function(t){var e=t.getGeometryType();if(!Gi[e])throw new Error("Geometry is not supported");return Gi[e].call(this,t)}});var Fi={Feature:function(t){var e={};for(var n in t)e[n]=t[n];if(t.geometry){var i=t.geometry.type;if(!Fi[i])throw new Error("Unknown GeoJSON type: "+t.type);e.geometry=this.read(t.geometry)}return t.bbox&&(e.bbox=Fi.bbox.call(this,t.bbox)),e},FeatureCollection:function(t){var e={};if(t.features){e.features=[];for(var n=0;n<t.features.length;++n)e.features.push(this.read(t.features[n]))}return t.bbox&&(e.bbox=this.parse.bbox.call(this,t.bbox)),e},coordinates:function(t){for(var e=[],n=0;n<t.length;++n){var i=t[n];e.push(new x(i[0],i[1]))}return e},bbox:function(t){return this.geometryFactory.createLinearRing([new x(t[0],t[1]),new x(t[2],t[1]),new x(t[2],t[3]),new x(t[0],t[3]),new x(t[0],t[1])])},Point:function(t){var e=new x(t[0],t[1]);return this.geometryFactory.createPoint(e)},MultiPoint:function(t){for(var e=[],n=0;n<t.length;++n)e.push(Fi.Point.call(this,t[n]));return this.geometryFactory.createMultiPoint(e)},LineString:function(t){var e=Fi.coordinates.call(this,t);return this.geometryFactory.createLineString(e)},MultiLineString:function(t){for(var e=[],n=0;n<t.length;++n)e.push(Fi.LineString.call(this,t[n]));return this.geometryFactory.createMultiLineString(e)},Polygon:function(t){for(var e=Fi.coordinates.call(this,t[0]),n=this.geometryFactory.createLinearRing(e),i=[],r=1;r<t.length;++r){var s=t[r],o=Fi.coordinates.call(this,s),a=this.geometryFactory.createLinearRing(o);i.push(a)}return this.geometryFactory.createPolygon(n,i)},MultiPolygon:function(t){for(var e=[],n=0;n<t.length;++n){var i=t[n];e.push(Fi.Polygon.call(this,i))}return this.geometryFactory.createMultiPolygon(e)},GeometryCollection:function(t){for(var e=[],n=0;n<t.length;++n){var i=t[n];e.push(this.read(i))}return this.geometryFactory.createGeometryCollection(e)}},Gi={coordinate:function(t){return[t.x,t.y]},Point:function(t){return{type:"Point",coordinates:Gi.coordinate.call(this,t.getCoordinate())}},MultiPoint:function(t){for(var e=[],n=0;n<t._geometries.length;++n){var i=t._geometries[n],r=Gi.Point.call(this,i);e.push(r.coordinates)}return{type:"MultiPoint",coordinates:e}},LineString:function(t){for(var e=[],n=t.getCoordinates(),i=0;i<n.length;++i){var r=n[i];e.push(Gi.coordinate.call(this,r))}return{type:"LineString",coordinates:e}},MultiLineString:function(t){for(var e=[],n=0;n<t._geometries.length;++n){var i=t._geometries[n],r=Gi.LineString.call(this,i);e.push(r.coordinates)}return{type:"MultiLineString",coordinates:e}},Polygon:function(t){var e=[],n=Gi.LineString.call(this,t._shell);e.push(n.coordinates);for(var i=0;i<t._holes.length;++i){var r=t._holes[i],s=Gi.LineString.call(this,r);e.push(s.coordinates)}return{type:"Polygon",coordinates:e}},MultiPolygon:function(t){for(var e=[],n=0;n<t._geometries.length;++n){var i=t._geometries[n],r=Gi.Polygon.call(this,i);e.push(r.coordinates)}return{type:"MultiPolygon",coordinates:e}},GeometryCollection:function(t){for(var e=[],n=0;n<t._geometries.length;++n){var i=t._geometries[n],r=i.getGeometryType();e.push(Gi[r].call(this,i))}return{type:"GeometryCollection",geometries:e}}};function qi(t){this.parser=new Ai(t||new le)}function Bi(){this.parser=new Ai(this.geometryFactory)}function zi(t){this.parser=new ce(t||new le)}function Vi(t){return[t.x,t.y]}function Yi(t,e){this.geometryFactory=t||new le,this.ol=e||"undefined"!=typeof ol&&ol}e(qi.prototype,{read:function(t){return this.parser.read(t)}}),e(Bi.prototype,{write:function(t){return this.parser.write(t)}}),e(zi.prototype,{read:function(t){return this.parser.read(t)}}),e(Yi.prototype,{inject:function(t,e,n,i,r,s,o,a){this.ol={geom:{Point:t,LineString:e,LinearRing:n,Polygon:i,MultiPoint:r,MultiLineString:s,MultiPolygon:o,GeometryCollection:a}}},read:function(t){var e=this.ol;return t instanceof e.geom.Point?this.convertFromPoint(t):t instanceof e.geom.LineString?this.convertFromLineString(t):t instanceof e.geom.LinearRing?this.convertFromLinearRing(t):t instanceof e.geom.Polygon?this.convertFromPolygon(t):t instanceof e.geom.MultiPoint?this.convertFromMultiPoint(t):t instanceof e.geom.MultiLineString?this.convertFromMultiLineString(t):t instanceof e.geom.MultiPolygon?this.convertFromMultiPolygon(t):t instanceof e.geom.GeometryCollection?this.convertFromCollection(t):void 0},convertFromPoint:function(t){var e=t.getCoordinates();return this.geometryFactory.createPoint(new x(e[0],e[1]))},convertFromLineString:function(t){return this.geometryFactory.createLineString(t.getCoordinates().map(function(t){return new x(t[0],t[1])}))},convertFromLinearRing:function(t){return this.geometryFactory.createLinearRing(t.getCoordinates().map(function(t){return new x(t[0],t[1])}))},convertFromPolygon:function(t){for(var e=t.getLinearRings(),n=null,i=[],r=0;r<e.length;r++){var s=this.convertFromLinearRing(e[r]);0===r?n=s:i.push(s)}return this.geometryFactory.createPolygon(n,i)},convertFromMultiPoint:function(t){var e=t.getPoints().map(function(t){return this.convertFromPoint(t)},this);return this.geometryFactory.createMultiPoint(e)},convertFromMultiLineString:function(t){var e=t.getLineStrings().map(function(t){return this.convertFromLineString(t)},this);return this.geometryFactory.createMultiLineString(e)},convertFromMultiPolygon:function(t){var e=t.getPolygons().map(function(t){return this.convertFromPolygon(t)},this);return this.geometryFactory.createMultiPolygon(e)},convertFromCollection:function(t){var e=t.getGeometries().map(function(t){return this.read(t)},this);return this.geometryFactory.createGeometryCollection(e)},write:function(t){return"Point"===t.getGeometryType()?this.convertToPoint(t.getCoordinate()):"LineString"===t.getGeometryType()?this.convertToLineString(t):"LinearRing"===t.getGeometryType()?this.convertToLinearRing(t):"Polygon"===t.getGeometryType()?this.convertToPolygon(t):"MultiPoint"===t.getGeometryType()?this.convertToMultiPoint(t):"MultiLineString"===t.getGeometryType()?this.convertToMultiLineString(t):"MultiPolygon"===t.getGeometryType()?this.convertToMultiPolygon(t):"GeometryCollection"===t.getGeometryType()?this.convertToCollection(t):void 0},convertToPoint:function(t){return new this.ol.geom.Point([t.x,t.y])},convertToLineString:function(t){var e=t._points._coordinates.map(Vi);return new this.ol.geom.LineString(e)},convertToLinearRing:function(t){var e=t._points._coordinates.map(Vi);return new this.ol.geom.LinearRing(e)},convertToPolygon:function(t){for(var e=[t._shell._points._coordinates.map(Vi)],n=0;n<t._holes.length;n++)e.push(t._holes[n]._points._coordinates.map(Vi));return new this.ol.geom.Polygon(e)},convertToMultiPoint:function(t){return new this.ol.geom.MultiPoint(t.getCoordinates().map(Vi))},convertToMultiLineString:function(t){for(var e=[],n=0;n<t._geometries.length;n++)e.push(this.convertToLineString(t._geometries[n]).getCoordinates());return new this.ol.geom.MultiLineString(e)},convertToMultiPolygon:function(t){for(var e=[],n=0;n<t._geometries.length;n++)e.push(this.convertToPolygon(t._geometries[n]).getCoordinates());return new this.ol.geom.MultiPolygon(e)},convertToCollection:function(t){for(var e=[],n=0;n<t._geometries.length;n++){var i=t._geometries[n];e.push(this.write(i))}return new this.ol.geom.GeometryCollection(e)}});var ki=Object.freeze({GeoJSONReader:qi,GeoJSONWriter:Bi,OL3Parser:Yi,WKTReader:zi,WKTWriter:de});function Ui(){}function Xi(){this._segString=null,this.coord=null,this.segmentIndex=null,this._segmentOctant=null,this._isInterior=null;var t=arguments[0],e=arguments[1],n=arguments[2],i=arguments[3];this._segString=t,this.coord=new x(e),this.segmentIndex=n,this._segmentOctant=i,this._isInterior=!e.equals2D(t.getCoordinate(n))}function Hi(){this._nodeMap=new pt,this._edge=null;var t=arguments[0];this._edge=t}function Wi(){this._nodeList=null,this._edge=null,this._nodeIt=null,this._currNode=null,this._nextNode=null;var t=arguments[this._currSegIndex=0];this._nodeList=t,this._edge=t.getEdge(),this._nodeIt=t.iterator(),this.readNextNode()}function ji(){}function Ki(){}function Zi(){}function Qi(){this._nodeList=new Hi(this),this._pts=null,this._data=null;var t=arguments[0],e=arguments[1];this._pts=t,this._data=e}function Ji(){this._overlapSeg1=new me,this._overlapSeg2=new me}function $i(){}function tr(){if(this._segInt=null,0===arguments.length);else if(1===arguments.length){var t=arguments[0];this.setSegmentIntersector(t)}}function er(){if(this._monoChains=new P,this._index=new Pi,this._idCounter=0,this._nodedSegStrings=null,(this._nOverlaps=0)===arguments.length);else if(1===arguments.length){var t=arguments[0];tr.call(this,t)}}function nr(){Ji.apply(this),this._si=null;var t=arguments[0];this._si=t}function ir(){if(this._noder=null,this._scaleFactor=null,this._offsetX=null,this._offsetY=null,this._isScaled=!1,2===arguments.length){var t=arguments[0],e=arguments[1];ir.call(this,t,e,0,0)}else if(4===arguments.length){var n=arguments[0],i=arguments[1];this._noder=n,this._scaleFactor=i,this._isScaled=!this.isIntegerPrecision()}}e(Ui.prototype,{interfaces_:function(){return[]},getClass:function(){return Ui}}),Ui.relativeSign=function(t,e){return t<e?-1:e<t?1:0},Ui.compare=function(t,e,n){if(e.equals2D(n))return 0;var i=Ui.relativeSign(e.x,n.x),r=Ui.relativeSign(e.y,n.y);switch(t){case 0:return Ui.compareValue(i,r);case 1:return Ui.compareValue(r,i);case 2:return Ui.compareValue(r,-i);case 3:return Ui.compareValue(-i,r);case 4:return Ui.compareValue(-i,-r);case 5:return Ui.compareValue(-r,-i);case 6:return Ui.compareValue(-r,i);case 7:return Ui.compareValue(i,-r)}return y.shouldNeverReachHere("invalid octant value"),0},Ui.compareValue=function(t,e){return t<0?-1:0<t?1:e<0?-1:0<e?1:0},e(Xi.prototype,{getCoordinate:function(){return this.coord},print:function(t){t.print(this.coord),t.print(" seg # = "+this.segmentIndex)},compareTo:function(t){var e=t;return this.segmentIndex<e.segmentIndex?-1:this.segmentIndex>e.segmentIndex?1:this.coord.equals2D(e.coord)?0:Ui.compare(this._segmentOctant,this.coord,e.coord)},isEndPoint:function(t){return 0===this.segmentIndex&&!this._isInterior||this.segmentIndex===t},isInterior:function(){return this._isInterior},interfaces_:function(){return[n]},getClass:function(){return Xi}}),e(Hi.prototype,{getSplitCoordinates:function(){var t=new b;this.addEndpoints();for(var e=this.iterator(),n=e.next();e.hasNext();){var i=e.next();this.addEdgeCoordinates(n,i,t),n=i}return t.toCoordinateArray()},addCollapsedNodes:function(){var t=new P;this.findCollapsesFromInsertedNodes(t),this.findCollapsesFromExistingVertices(t);for(var e=t.iterator();e.hasNext();){var n=e.next().intValue();this.add(this._edge.getCoordinate(n),n)}},print:function(t){t.println("Intersections:");for(var e=this.iterator();e.hasNext();){e.next().print(t)}},findCollapsesFromExistingVertices:function(t){for(var e=0;e<this._edge.size()-2;e++){var n=this._edge.getCoordinate(e),i=(this._edge.getCoordinate(e+1),this._edge.getCoordinate(e+2));n.equals2D(i)&&t.add(new G(e+1))}},addEdgeCoordinates:function(t,e,n){e.segmentIndex,t.segmentIndex;var i=this._edge.getCoordinate(e.segmentIndex),r=e.isInterior()||!e.coord.equals2D(i);n.add(new x(t.coord),!1);for(var s=t.segmentIndex+1;s<=e.segmentIndex;s++)n.add(this._edge.getCoordinate(s));r&&n.add(new x(e.coord))},iterator:function(){return this._nodeMap.values().iterator()},addSplitEdges:function(t){this.addEndpoints(),this.addCollapsedNodes();for(var e=this.iterator(),n=e.next();e.hasNext();){var i=e.next(),r=this.createSplitEdge(n,i);t.add(r),n=i}},findCollapseIndex:function(t,e,n){if(!t.coord.equals2D(e.coord))return!1;var i=e.segmentIndex-t.segmentIndex;return e.isInterior()||i--,1===i&&(n[0]=t.segmentIndex+1,!0)},findCollapsesFromInsertedNodes:function(t){for(var e=new Array(1).fill(null),n=this.iterator(),i=n.next();n.hasNext();){var r=n.next();this.findCollapseIndex(i,r,e)&&t.add(new G(e[0])),i=r}},getEdge:function(){return this._edge},addEndpoints:function(){var t=this._edge.size()-1;this.add(this._edge.getCoordinate(0),0),this.add(this._edge.getCoordinate(t),t)},createSplitEdge:function(t,e){var n=e.segmentIndex-t.segmentIndex+2,i=this._edge.getCoordinate(e.segmentIndex),r=e.isInterior()||!e.coord.equals2D(i);r||n--;var s=new Array(n).fill(null),o=0;s[o++]=new x(t.coord);for(var a=t.segmentIndex+1;a<=e.segmentIndex;a++)s[o++]=this._edge.getCoordinate(a);return r&&(s[o]=new x(e.coord)),new Qi(s,this._edge.getData())},add:function(t,e){var n=new Xi(this._edge,t,e,this._edge.getSegmentOctant(e)),i=this._nodeMap.get(n);return null!==i?(y.isTrue(i.coord.equals2D(t),"Found equal nodes with different coordinates"),i):(this._nodeMap.put(n,n),n)},checkSplitEdgesCorrectness:function(t){var e=this._edge.getCoordinates(),n=t.get(0).getCoordinate(0);if(!n.equals2D(e[0]))throw new v("bad split edge start point at "+n);var i=t.get(t.size()-1).getCoordinates(),r=i[i.length-1];if(!r.equals2D(e[e.length-1]))throw new v("bad split edge end point at "+r)},interfaces_:function(){return[]},getClass:function(){return Hi}}),e(Wi.prototype,{next:function(){return null===this._currNode?(this._currNode=this._nextNode,this._currSegIndex=this._currNode.segmentIndex,this.readNextNode(),this._currNode):null===this._nextNode?null:this._nextNode.segmentIndex===this._currNode.segmentIndex?(this._currNode=this._nextNode,this._currSegIndex=this._currNode.segmentIndex,this.readNextNode(),this._currNode):(this._nextNode.segmentIndex,this._currNode.segmentIndex,null)},remove:function(){throw new UnsupportedOperationException(this.getClass().getName())},hasNext:function(){return null!==this._nextNode},readNextNode:function(){this._nodeIt.hasNext()?this._nextNode=this._nodeIt.next():this._nextNode=null},interfaces_:function(){return[I]},getClass:function(){return Wi}}),e(ji.prototype,{interfaces_:function(){return[]},getClass:function(){return ji}}),ji.octant=function(){if("number"==typeof arguments[0]&&"number"==typeof arguments[1]){var t=arguments[0],e=arguments[1];if(0===t&&0===e)throw new c("Cannot compute the octant for point ( "+t+", "+e+" )");var n=Math.abs(t),i=Math.abs(e);return 0<=t?0<=e?i<=n?0:1:i<=n?7:6:0<=e?i<=n?3:2:i<=n?4:5}if(arguments[0]instanceof x&&arguments[1]instanceof x){var r=arguments[0],s=arguments[1],o=s.x-r.x,a=s.y-r.y;if(0===o&&0===a)throw new c("Cannot compute the octant for two identical points "+r);return ji.octant(o,a)}},e(Ki.prototype,{getCoordinates:function(){},size:function(){},getCoordinate:function(t){},isClosed:function(){},setData:function(t){},getData:function(){},interfaces_:function(){return[]},getClass:function(){return Ki}}),e(Zi.prototype,{addIntersection:function(t,e){},interfaces_:function(){return[Ki]},getClass:function(){return Zi}}),e(Qi.prototype,{getCoordinates:function(){return this._pts},size:function(){return this._pts.length},getCoordinate:function(t){return this._pts[t]},isClosed:function(){return this._pts[0].equals(this._pts[this._pts.length-1])},getSegmentOctant:function(t){return t===this._pts.length-1?-1:this.safeOctant(this.getCoordinate(t),this.getCoordinate(t+1))},setData:function(t){this._data=t},safeOctant:function(t,e){return t.equals2D(e)?0:ji.octant(t,e)},getData:function(){return this._data},addIntersection:function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];this.addIntersectionNode(t,e)}else if(4===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[3],s=new x(n.getIntersection(r));this.addIntersection(s,i)}},toString:function(){return de.toLineString(new $t(this._pts))},getNodeList:function(){return this._nodeList},addIntersectionNode:function(t,e){var n=e,i=n+1;if(i<this._pts.length){var r=this._pts[i];t.equals2D(r)&&(n=i)}return this._nodeList.add(t,n)},addIntersections:function(t,e,n){for(var i=0;i<t.getIntersectionNum();i++)this.addIntersection(t,e,n,i)},interfaces_:function(){return[Zi]},getClass:function(){return Qi}}),Qi.getNodedSubstrings=function(){if(1===arguments.length){var t=arguments[0],e=new P;return Qi.getNodedSubstrings(t,e),e}if(2===arguments.length)for(var n=arguments[0],i=arguments[1],r=n.iterator();r.hasNext();){r.next().getNodeList().addSplitEdges(i)}},e(Ji.prototype,{overlap:function(){if(2===arguments.length);else if(4===arguments.length){var t=arguments[0],e=arguments[1],n=arguments[2],i=arguments[3];t.getLineSegment(e,this._overlapSeg1),n.getLineSegment(i,this._overlapSeg2),this.overlap(this._overlapSeg1,this._overlapSeg2)}},interfaces_:function(){return[]},getClass:function(){return Ji}}),e($i.prototype,{computeNodes:function(t){},getNodedSubstrings:function(){},interfaces_:function(){return[]},getClass:function(){return $i}}),e(tr.prototype,{setSegmentIntersector:function(t){this._segInt=t},interfaces_:function(){return[$i]},getClass:function(){return tr}}),p(er,tr),e(er.prototype,{getMonotoneChains:function(){return this._monoChains},getNodedSubstrings:function(){return Qi.getNodedSubstrings(this._nodedSegStrings)},getIndex:function(){return this._index},add:function(t){for(var e=En.getChains(t.getCoordinates(),t).iterator();e.hasNext();){var n=e.next();n.setId(this._idCounter++),this._index.insert(n.getEnvelope(),n),this._monoChains.add(n)}},computeNodes:function(t){for(var e=(this._nodedSegStrings=t).iterator();e.hasNext();)this.add(e.next());this.intersectChains()},intersectChains:function(){for(var t=new nr(this._segInt),e=this._monoChains.iterator();e.hasNext();)for(var n=e.next(),i=this._index.query(n.getEnvelope()).iterator();i.hasNext();){var r=i.next();if(r.getId()>n.getId()&&(n.computeOverlaps(r,t),this._nOverlaps++),this._segInt.isDone())return null}},interfaces_:function(){return[]},getClass:function(){return er}}),p(nr,Ji),e(nr.prototype,{overlap:function(){if(4!==arguments.length)return Ji.prototype.overlap.apply(this,arguments);var t=arguments[0],e=arguments[1],n=arguments[2],i=arguments[3],r=t.getContext(),s=n.getContext();this._si.processIntersections(r,e,s,i)},interfaces_:function(){return[]},getClass:function(){return nr}}),er.SegmentOverlapAction=nr,e(ir.prototype,{rescale:function(){if(L(arguments[0],N))for(var t=arguments[0].iterator();t.hasNext();){var e=t.next();this.rescale(e.getCoordinates())}else if(arguments[0]instanceof Array){var n=arguments[0];2===n.length&&(new x(n[0]),new x(n[1]));for(t=0;t<n.length;t++)n[t].x=n[t].x/this._scaleFactor+this._offsetX,n[t].y=n[t].y/this._scaleFactor+this._offsetY;2===n.length&&n[0].equals2D(n[1])&&Y.out.println(n)}},scale:function(){if(L(arguments[0],N)){for(var t=arguments[0],e=new P(t.size()),n=t.iterator();n.hasNext();){var i=n.next();e.add(new Qi(this.scale(i.getCoordinates()),i.getData()))}return e}if(arguments[0]instanceof Array){var r=arguments[0],s=new Array(r.length).fill(null);for(n=0;n<r.length;n++)s[n]=new x(Math.round((r[n].x-this._offsetX)*this._scaleFactor),Math.round((r[n].y-this._offsetY)*this._scaleFactor),r[n].z);return nt.removeRepeatedPoints(s)}},isIntegerPrecision:function(){return 1===this._scaleFactor},getNodedSubstrings:function(){var t=this._noder.getNodedSubstrings();return this._isScaled&&this.rescale(t),t},computeNodes:function(t){var e=t;this._isScaled&&(e=this.scale(t)),this._noder.computeNodes(e)},interfaces_:function(){return[$i]},getClass:function(){return ir}});var rr=Object.freeze({MCIndexNoder:er,ScaledNoder:ir,SegmentString:Ki});function sr(){if(this._inputGeom=null,this._isClosedEndpointsInInterior=!0,this._nonSimpleLocation=null,1===arguments.length){var t=arguments[0];this._inputGeom=t}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this._inputGeom=e,this._isClosedEndpointsInInterior=!n.isInBoundary(2)}}function or(){this.pt=null,this.isClosed=null,this.degree=null;var t=arguments[0];this.pt=t,this.isClosed=!1,this.degree=0}function ar(){if(this._quadrantSegments=ar.DEFAULT_QUADRANT_SEGMENTS,this._endCapStyle=ar.CAP_ROUND,this._joinStyle=ar.JOIN_ROUND,this._mitreLimit=ar.DEFAULT_MITRE_LIMIT,this._isSingleSided=!1,this._simplifyFactor=ar.DEFAULT_SIMPLIFY_FACTOR,0===arguments.length);else if(1===arguments.length){var t=arguments[0];this.setQuadrantSegments(t)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this.setQuadrantSegments(e),this.setEndCapStyle(n)}else if(4===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2],o=arguments[3];this.setQuadrantSegments(i),this.setEndCapStyle(r),this.setJoinStyle(s),this.setMitreLimit(o)}}function ur(){this._minIndex=-1,this._minCoord=null,this._minDe=null,this._orientedDe=null}function lr(){this.array_=[]}function hr(){this._finder=null,this._dirEdgeList=new P,this._nodes=new P,this._rightMostCoord=null,this._env=null,this._finder=new ur}function cr(){this._startDe=null,this._maxNodeDegree=-1,this._edges=new P,this._pts=new P,this._label=new jn(ve.NONE),this._ring=null,this._isHole=null,this._shell=null,this._holes=new P,this._geometryFactory=null;var t=arguments[0],e=arguments[1];this._geometryFactory=e,this.computePoints(t),this.computeRing()}function fr(){var t=arguments[0],e=arguments[1];cr.call(this,t,e)}function gr(){var t=arguments[0],e=arguments[1];cr.call(this,t,e)}function dr(){this._geometryFactory=null,this._shellList=new P;var t=arguments[0];this._geometryFactory=t}function _r(){this._inputLine=null,this._distanceTol=null,this._isDeleted=null,this._angleOrientation=V.COUNTERCLOCKWISE;var t=arguments[0];this._inputLine=t}function pr(){this._ptList=null,this._precisionModel=null,this._minimimVertexDistance=0,this._ptList=new P}function mr(){this._maxCurveSegmentError=0,this._filletAngleQuantum=null,this._closingSegLengthFactor=1,this._segList=null,this._distance=0,this._precisionModel=null,this._bufParams=null,this._li=null,this._s0=null,this._s1=null,this._s2=null,this._seg0=new me,this._seg1=new me,this._offset0=new me,this._offset1=new me,this._side=0,this._hasNarrowConcaveAngle=!1;var t=arguments[0],e=arguments[1],n=arguments[2];this._precisionModel=t,this._bufParams=e,this._li=new pe,this._filletAngleQuantum=Math.PI/2/e.getQuadrantSegments(),8<=e.getQuadrantSegments()&&e.getJoinStyle()===ar.JOIN_ROUND&&(this._closingSegLengthFactor=mr.MAX_CLOSING_SEG_LEN_FACTOR),this.init(n)}function vr(){this._distance=0,this._precisionModel=null,this._bufParams=null;var t=arguments[0],e=arguments[1];this._precisionModel=t,this._bufParams=e}function yr(){this._subgraphs=null,this._seg=new me;var t=arguments[0];this._subgraphs=t}function xr(){this._upwardSeg=null,this._leftDepth=null;var t=arguments[0],e=arguments[1];this._upwardSeg=new me(t),this._leftDepth=e}function Er(){this._inputGeom=null,this._distance=null,this._curveBuilder=null,this._curveList=new P;var t=arguments[0],e=arguments[1],n=arguments[2];this._inputGeom=t,this._distance=e,this._curveBuilder=n}function Ir(){this._edgeMap=new pt,this._edgeList=null,this._ptInAreaLocation=[ve.NONE,ve.NONE]}function Nr(){Ir.apply(this),this._resultAreaEdgeList=null,this._label=null,this._SCANNING_FOR_INCOMING=1,this._LINKING_TO_OUTGOING=2}function Cr(){ai.apply(this)}function Sr(){this._pts=null,this._orientation=null;var t=arguments[0];this._pts=t,this._orientation=Sr.orientation(t)}function Lr(){this._edges=new P,this._ocaMap=new pt}function wr(){}function Rr(){this._hasIntersection=!1,this._hasProper=!1,this._hasProperInterior=!1,this._hasInterior=!1,this._properIntersectionPoint=null,this._li=null,this._isSelfIntersection=null,this.numIntersections=0,this.numInteriorIntersections=0,this.numProperIntersections=0;var t=arguments[this.numTests=0];this._li=t}function Tr(){this._bufParams=null,this._workingPrecisionModel=null,this._workingNoder=null,this._geomFact=null,this._graph=null,this._edgeList=new Lr;var t=arguments[0];this._bufParams=t}function Pr(){this._li=new pe,this._segStrings=null;var t=arguments[0];this._segStrings=t}function Or(){this._li=null,this._pt=null,this._originalPt=null,this._ptScaled=null,this._p0Scaled=null,this._p1Scaled=null,this._scaleFactor=null,this._minx=null,this._maxx=null,this._miny=null,this._maxy=null,this._corner=new Array(4).fill(null),this._safeEnv=null;var t=arguments[0],e=arguments[1],n=arguments[2];if(this._originalPt=t,this._pt=t,this._scaleFactor=e,this._li=n,e<=0)throw new c("Scale factor must be non-zero");1!==e&&(this._pt=new x(this.scale(t.x),this.scale(t.y)),this._p0Scaled=new x,this._p1Scaled=new x),this.initCorners(this._pt)}function br(){this._index=null;var t=arguments[0];this._index=t}function Mr(){hn.apply(this),this._hotPixel=null,this._parentEdge=null,this._hotPixelVertexIndex=null,this._isNodeAdded=!1;var t=arguments[0],e=arguments[1],n=arguments[2];this._hotPixel=t,this._parentEdge=e,this._hotPixelVertexIndex=n}function Dr(){this._li=null,this._interiorIntersections=null;var t=arguments[0];this._li=t,this._interiorIntersections=new P}function Ar(){this._pm=null,this._li=null,this._scaleFactor=null,this._noder=null,this._pointSnapper=null,this._nodedSegStrings=null;var t=arguments[0];this._pm=t,this._li=new pe,this._li.setPrecisionModel(t),this._scaleFactor=t.getScale()}function Fr(){if(this._argGeom=null,this._distance=null,this._bufParams=new ar,this._resultGeometry=null,this._saveException=null,1===arguments.length){var t=arguments[0];this._argGeom=t}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this._argGeom=e,this._bufParams=n}}e(sr.prototype,{isSimpleMultiPoint:function(t){if(t.isEmpty())return!0;for(var e=new yt,n=0;n<t.getNumGeometries();n++){var i=t.getGeometryN(n).getCoordinate();if(e.contains(i))return this._nonSimpleLocation=i,!1;e.add(i)}return!0},isSimplePolygonal:function(t){for(var e=Ge.getLines(t).iterator();e.hasNext();){var n=e.next();if(!this.isSimpleLinearGeometry(n))return!1}return!0},hasClosedEndpointIntersection:function(t){for(var e=new pt,n=t.getEdgeIterator();n.hasNext();){var i=n.next(),r=(i.getMaximumSegmentIndex(),i.isClosed()),s=i.getCoordinate(0);this.addEndpoint(e,s,r);var o=i.getCoordinate(i.getNumPoints()-1);this.addEndpoint(e,o,r)}for(n=e.values().iterator();n.hasNext();){var a=n.next();if(a.isClosed&&2!==a.degree)return this._nonSimpleLocation=a.getCoordinate(),!0}return!1},getNonSimpleLocation:function(){return this._nonSimpleLocation},isSimpleLinearGeometry:function(t){if(t.isEmpty())return!0;var e=new li(0,t),n=new pe,i=e.computeSelfNodes(n,!0);return!i.hasIntersection()||(i.hasProperIntersection()?(this._nonSimpleLocation=i.getProperIntersectionPoint(),!1):!this.hasNonEndpointIntersection(e)&&(!this._isClosedEndpointsInInterior||!this.hasClosedEndpointIntersection(e)))},hasNonEndpointIntersection:function(t){for(var e=t.getEdgeIterator();e.hasNext();)for(var n=e.next(),i=n.getMaximumSegmentIndex(),r=n.getEdgeIntersectionList().iterator();r.hasNext();){var s=r.next();if(!s.isEndPoint(i))return this._nonSimpleLocation=s.getCoordinate(),!0}return!1},addEndpoint:function(t,e,n){var i=t.get(e);null===i&&(i=new or(e),t.put(e,i)),i.addEndpoint(n)},computeSimple:function(t){return this._nonSimpleLocation=null,!!t.isEmpty()||(t instanceof Bt?this.isSimpleLinearGeometry(t):t instanceof wt?this.isSimpleLinearGeometry(t):t instanceof Xt?this.isSimpleMultiPoint(t):L(t,kt)?this.isSimplePolygonal(t):!(t instanceof Lt)||this.isSimpleGeometryCollection(t))},isSimple:function(){return this._nonSimpleLocation=null,this.computeSimple(this._inputGeom)},isSimpleGeometryCollection:function(t){for(var e=0;e<t.getNumGeometries();e++){var n=t.getGeometryN(e);if(!this.computeSimple(n))return!1}return!0},interfaces_:function(){return[]},getClass:function(){return sr}}),sr.isSimple=function(){return 1===arguments.length?new sr(arguments[0]).isSimple():2===arguments.length?new sr(arguments[0],arguments[1]).isSimple():void 0},e(or.prototype,{addEndpoint:function(t){this.degree++,this.isClosed|=t},getCoordinate:function(){return this.pt},interfaces_:function(){return[]},getClass:function(){return or}}),sr.EndpointInfo=or,e(ar.prototype,{getEndCapStyle:function(){return this._endCapStyle},isSingleSided:function(){return this._isSingleSided},setQuadrantSegments:function(t){this._quadrantSegments=t,0===this._quadrantSegments&&(this._joinStyle=ar.JOIN_BEVEL),this._quadrantSegments<0&&(this._joinStyle=ar.JOIN_MITRE,this._mitreLimit=Math.abs(this._quadrantSegments)),t<=0&&(this._quadrantSegments=1),this._joinStyle!==ar.JOIN_ROUND&&(this._quadrantSegments=ar.DEFAULT_QUADRANT_SEGMENTS)},getJoinStyle:function(){return this._joinStyle},setJoinStyle:function(t){this._joinStyle=t},setSimplifyFactor:function(t){this._simplifyFactor=t<0?0:t},getSimplifyFactor:function(){return this._simplifyFactor},getQuadrantSegments:function(){return this._quadrantSegments},setEndCapStyle:function(t){this._endCapStyle=t},getMitreLimit:function(){return this._mitreLimit},setMitreLimit:function(t){this._mitreLimit=t},setSingleSided:function(t){this._isSingleSided=t},interfaces_:function(){return[]},getClass:function(){return ar}}),ar.bufferDistanceError=function(t){var e=Math.PI/2/t;return 1-Math.cos(e/2)},ar.CAP_ROUND=1,ar.CAP_FLAT=2,ar.CAP_SQUARE=3,ar.JOIN_ROUND=1,ar.JOIN_MITRE=2,ar.JOIN_BEVEL=3,ar.DEFAULT_QUADRANT_SEGMENTS=8,ar.DEFAULT_MITRE_LIMIT=5,ar.DEFAULT_SIMPLIFY_FACTOR=.01,e(ur.prototype,{getCoordinate:function(){return this._minCoord},getRightmostSide:function(t,e){var n=this.getRightmostSideOfSegment(t,e);return n<0&&(n=this.getRightmostSideOfSegment(t,e-1)),n<0&&(this._minCoord=null,this.checkForRightmostCoordinate(t)),n},findRightmostEdgeAtVertex:function(){var t=this._minDe.getEdge().getCoordinates();y.isTrue(0<this._minIndex&&this._minIndex<t.length,"rightmost point expected to be interior vertex of edge");var e=t[this._minIndex-1],n=t[this._minIndex+1],i=V.index(this._minCoord,n,e),r=!1;e.y<this._minCoord.y&&n.y<this._minCoord.y&&i===V.COUNTERCLOCKWISE?r=!0:e.y>this._minCoord.y&&n.y>this._minCoord.y&&i===V.CLOCKWISE&&(r=!0),r&&(this._minIndex=this._minIndex-1)},getRightmostSideOfSegment:function(t,e){var n=t.getEdge().getCoordinates();if(e<0||e+1>=n.length)return-1;if(n[e].y===n[e+1].y)return-1;var i=Vn.LEFT;return n[e].y<n[e+1].y&&(i=Vn.RIGHT),i},getEdge:function(){return this._orientedDe},checkForRightmostCoordinate:function(t){for(var e=t.getEdge().getCoordinates(),n=0;n<e.length-1;n++)(null===this._minCoord||e[n].x>this._minCoord.x)&&(this._minDe=t,this._minIndex=n,this._minCoord=e[n])},findRightmostEdgeAtNode:function(){var t=this._minDe.getNode().getEdges();this._minDe=t.getRightmostEdge(),this._minDe.isForward()||(this._minDe=this._minDe.getSym(),this._minIndex=this._minDe.getEdge().getCoordinates().length-1)},findEdge:function(t){for(var e=t.iterator();e.hasNext();){var n=e.next();n.isForward()&&this.checkForRightmostCoordinate(n)}y.isTrue(0!==this._minIndex||this._minCoord.equals(this._minDe.getCoordinate()),"inconsistency in rightmost processing"),0===this._minIndex?this.findRightmostEdgeAtNode():this.findRightmostEdgeAtVertex(),this._orientedDe=this._minDe,this.getRightmostSide(this._minDe,this._minIndex)===Vn.LEFT&&(this._orientedDe=this._minDe.getSym())},interfaces_:function(){return[]},getClass:function(){return ur}}),lr.prototype.addLast=function(t){this.array_.push(t)},lr.prototype.removeFirst=function(){return this.array_.shift()},lr.prototype.isEmpty=function(){return 0===this.array_.length},e(hr.prototype,{clearVisitedEdges:function(){for(var t=this._dirEdgeList.iterator();t.hasNext();){t.next().setVisited(!1)}},getRightmostCoordinate:function(){return this._rightMostCoord},computeNodeDepth:function(t){for(var e=null,n=t.getEdges().iterator();n.hasNext();){if((i=n.next()).isVisited()||i.getSym().isVisited()){e=i;break}}if(null===e)throw new si("unable to find edge to compute depths at "+t.getCoordinate());t.getEdges().computeDepths(e);for(n=t.getEdges().iterator();n.hasNext();){var i;(i=n.next()).setVisited(!0),this.copySymDepths(i)}},computeDepth:function(t){this.clearVisitedEdges();var e=this._finder.getEdge();e.getNode(),e.getLabel();e.setEdgeDepths(Vn.RIGHT,t),this.copySymDepths(e),this.computeDepths(e)},create:function(t){this.addReachable(t),this._finder.findEdge(this._dirEdgeList),this._rightMostCoord=this._finder.getCoordinate()},findResultEdges:function(){for(var t=this._dirEdgeList.iterator();t.hasNext();){var e=t.next();1<=e.getDepth(Vn.RIGHT)&&e.getDepth(Vn.LEFT)<=0&&!e.isInteriorAreaEdge()&&e.setInResult(!0)}},computeDepths:function(t){var e=new ut,n=new lr,i=t.getNode();for(n.addLast(i),e.add(i),t.setVisited(!0);!n.isEmpty();){var r=n.removeFirst();e.add(r),this.computeNodeDepth(r);for(var s=r.getEdges().iterator();s.hasNext();){var o=s.next().getSym();if(!o.isVisited()){var a=o.getNode();e.contains(a)||(n.addLast(a),e.add(a))}}}},compareTo:function(t){var e=t;return this._rightMostCoord.x<e._rightMostCoord.x?-1:this._rightMostCoord.x>e._rightMostCoord.x?1:0},getEnvelope:function(){if(null===this._env){for(var t=new M,e=this._dirEdgeList.iterator();e.hasNext();)for(var n=e.next().getEdge().getCoordinates(),i=0;i<n.length-1;i++)t.expandToInclude(n[i]);this._env=t}return this._env},addReachable:function(t){var e=new en;for(e.add(t);!e.empty();){var n=e.pop();this.add(n,e)}},copySymDepths:function(t){var e=t.getSym();e.setDepth(Vn.LEFT,t.getDepth(Vn.RIGHT)),e.setDepth(Vn.RIGHT,t.getDepth(Vn.LEFT))},add:function(t,e){t.setVisited(!0),this._nodes.add(t);for(var n=t.getEdges().iterator();n.hasNext();){var i=n.next();this._dirEdgeList.add(i);var r=i.getSym().getNode();r.isVisited()||e.push(r)}},getNodes:function(){return this._nodes},getDirectedEdges:function(){return this._dirEdgeList},interfaces_:function(){return[n]},getClass:function(){return hr}}),e(cr.prototype,{computeRing:function(){if(null!==this._ring)return null;for(var t=new Array(this._pts.size()).fill(null),e=0;e<this._pts.size();e++)t[e]=this._pts.get(e);this._ring=this._geometryFactory.createLinearRing(t),this._isHole=V.isCCW(this._ring.getCoordinates())},isIsolated:function(){return 1===this._label.getGeometryCount()},computePoints:function(t){var e=this._startDe=t,n=!0;do{if(null===e)throw new si("Found null DirectedEdge");if(e.getEdgeRing()===this)throw new si("Directed Edge visited twice during ring-building at "+e.getCoordinate());this._edges.add(e);var i=e.getLabel();y.isTrue(i.isArea()),this.mergeLabel(i),this.addPoints(e.getEdge(),e.isForward(),n),n=!1,this.setEdgeRing(e,this),e=this.getNext(e)}while(e!==this._startDe)},getLinearRing:function(){return this._ring},getCoordinate:function(t){return this._pts.get(t)},computeMaxNodeDegree:function(){this._maxNodeDegree=0;var t=this._startDe;do{var e=t.getNode().getEdges().getOutgoingDegree(this);e>this._maxNodeDegree&&(this._maxNodeDegree=e),t=this.getNext(t)}while(t!==this._startDe);this._maxNodeDegree*=2},addPoints:function(t,e,n){var i=t.getCoordinates();if(e){var r=1;n&&(r=0);for(var s=r;s<i.length;s++)this._pts.add(i[s])}else{r=i.length-2;n&&(r=i.length-1);for(s=r;0<=s;s--)this._pts.add(i[s])}},isHole:function(){return this._isHole},setInResult:function(){for(var t=this._startDe;t.getEdge().setInResult(!0),(t=t.getNext())!==this._startDe;);},containsPoint:function(t){var e=this.getLinearRing();if(!e.getEnvelopeInternal().contains(t))return!1;if(!ke.isInRing(t,e.getCoordinates()))return!1;for(var n=this._holes.iterator();n.hasNext();){if(n.next().containsPoint(t))return!1}return!0},addHole:function(t){this._holes.add(t)},isShell:function(){return null===this._shell},getLabel:function(){return this._label},getEdges:function(){return this._edges},getMaxNodeDegree:function(){return this._maxNodeDegree<0&&this.computeMaxNodeDegree(),this._maxNodeDegree},getShell:function(){return this._shell},mergeLabel:function(){if(1===arguments.length){var t=arguments[0];this.mergeLabel(t,0),this.mergeLabel(t,1)}else if(2===arguments.length){var e=arguments[0],n=arguments[1],i=e.getLocation(n,Vn.RIGHT);if(i===ve.NONE)return null;if(this._label.getLocation(n)===ve.NONE)return this._label.setLocation(n,i),null}},setShell:function(t){null!==(this._shell=t)&&t.addHole(this)},toPolygon:function(t){for(var e=new Array(this._holes.size()).fill(null),n=0;n<this._holes.size();n++)e[n]=this._holes.get(n).getLinearRing();return t.createPolygon(this.getLinearRing(),e)},interfaces_:function(){return[]},getClass:function(){return cr}}),p(fr,cr),e(fr.prototype,{setEdgeRing:function(t,e){t.setMinEdgeRing(e)},getNext:function(t){return t.getNextMin()},interfaces_:function(){return[]},getClass:function(){return fr}}),p(gr,cr),e(gr.prototype,{buildMinimalRings:function(){var t=new P,e=this._startDe;do{if(null===e.getMinEdgeRing()){var n=new fr(e,this._geometryFactory);t.add(n)}e=e.getNext()}while(e!==this._startDe);return t},setEdgeRing:function(t,e){t.setEdgeRing(e)},linkDirectedEdgesForMinimalEdgeRings:function(){var t=this._startDe;do{t.getNode().getEdges().linkMinimalDirectedEdges(this),t=t.getNext()}while(t!==this._startDe)},getNext:function(t){return t.getNext()},interfaces_:function(){return[]},getClass:function(){return gr}}),e(dr.prototype,{sortShellsAndHoles:function(t,e,n){for(var i=t.iterator();i.hasNext();){var r=i.next();r.isHole()?n.add(r):e.add(r)}},computePolygons:function(t){for(var e=new P,n=t.iterator();n.hasNext();){var i=n.next().toPolygon(this._geometryFactory);e.add(i)}return e},placeFreeHoles:function(t,e){for(var n=e.iterator();n.hasNext();){var i=n.next();if(null===i.getShell()){var r=this.findEdgeRingContaining(i,t);if(null===r)throw new si("unable to assign hole to a shell",i.getCoordinate(0));i.setShell(r)}}},buildMinimalEdgeRings:function(t,e,n){for(var i=new P,r=t.iterator();r.hasNext();){var s=r.next();if(2<s.getMaxNodeDegree()){s.linkDirectedEdgesForMinimalEdgeRings();var o=s.buildMinimalRings(),a=this.findShell(o);null!==a?(this.placePolygonHoles(a,o),e.add(a)):n.addAll(o)}else i.add(s)}return i},containsPoint:function(t){for(var e=this._shellList.iterator();e.hasNext();){if(e.next().containsPoint(t))return!0}return!1},buildMaximalEdgeRings:function(t){for(var e=new P,n=t.iterator();n.hasNext();){var i=n.next();if(i.isInResult()&&i.getLabel().isArea()&&null===i.getEdgeRing()){var r=new gr(i,this._geometryFactory);e.add(r),r.setInResult()}}return e},placePolygonHoles:function(t,e){for(var n=e.iterator();n.hasNext();){var i=n.next();i.isHole()&&i.setShell(t)}},getPolygons:function(){return this.computePolygons(this._shellList)},findEdgeRingContaining:function(t,e){for(var n=t.getLinearRing(),i=n.getEnvelopeInternal(),r=n.getCoordinateN(0),s=null,o=null,a=e.iterator();a.hasNext();){var u=a.next(),l=u.getLinearRing(),h=l.getEnvelopeInternal();null!==s&&(o=s.getLinearRing().getEnvelopeInternal());var c=!1;h.contains(i)&&ke.isInRing(r,l.getCoordinates())&&(c=!0),c&&(null===s||o.contains(h))&&(s=u)}return s},findShell:function(t){for(var e=0,n=null,i=t.iterator();i.hasNext();){var r=i.next();r.isHole()||(n=r,e++)}return y.isTrue(e<=1,"found two shells in MinimalEdgeRing list"),n},add:function(){if(1===arguments.length){var t=arguments[0];this.add(t.getEdgeEnds(),t.getNodes())}else if(2===arguments.length){var e=arguments[0],n=arguments[1];ui.linkResultDirectedEdges(n);var i=this.buildMaximalEdgeRings(e),r=new P,s=this.buildMinimalEdgeRings(i,this._shellList,r);this.sortShellsAndHoles(s,this._shellList,r),this.placeFreeHoles(this._shellList,r)}},interfaces_:function(){return[]},getClass:function(){return dr}}),e(_r.prototype,{isDeletable:function(t,e,n,i){var r=this._inputLine[t],s=this._inputLine[e],o=this._inputLine[n];return!!this.isConcave(r,s,o)&&(!!this.isShallow(r,s,o,i)&&this.isShallowSampled(r,s,t,n,i))},deleteShallowConcavities:function(){for(var t=1,e=(this._inputLine.length,this.findNextNonDeletedIndex(t)),n=this.findNextNonDeletedIndex(e),i=!1;n<this._inputLine.length;){var r=!1;this.isDeletable(t,e,n,this._distanceTol)&&(this._isDeleted[e]=_r.DELETE,i=r=!0),t=r?n:e,e=this.findNextNonDeletedIndex(t),n=this.findNextNonDeletedIndex(e)}return i},isShallowConcavity:function(t,e,n,i){return V.index(t,e,n)===this._angleOrientation&&X.pointToSegment(e,t,n)<i},isShallowSampled:function(t,e,n,i,r){var s=Math.trunc((i-n)/_r.NUM_PTS_TO_CHECK);s<=0&&(s=1);for(var o=n;o<i;o+=s)if(!this.isShallow(t,e,this._inputLine[o],r))return!1;return!0},isConcave:function(t,e,n){var i=V.index(t,e,n)===this._angleOrientation;return i},simplify:function(t){this._distanceTol=Math.abs(t),t<0&&(this._angleOrientation=V.CLOCKWISE),this._isDeleted=new Array(this._inputLine.length).fill(null);for(;this.deleteShallowConcavities(););return this.collapseLine()},findNextNonDeletedIndex:function(t){for(var e=t+1;e<this._inputLine.length&&this._isDeleted[e]===_r.DELETE;)e++;return e},isShallow:function(t,e,n,i){return X.pointToSegment(e,t,n)<i},collapseLine:function(){for(var t=new b,e=0;e<this._inputLine.length;e++)this._isDeleted[e]!==_r.DELETE&&t.add(this._inputLine[e]);return t.toCoordinateArray()},interfaces_:function(){return[]},getClass:function(){return _r}}),_r.simplify=function(t,e){return new _r(t).simplify(e)},_r.INIT=0,_r.DELETE=1,_r.KEEP=1,_r.NUM_PTS_TO_CHECK=10,e(pr.prototype,{getCoordinates:function(){return this._ptList.toArray(pr.COORDINATE_ARRAY_TYPE)},setPrecisionModel:function(t){this._precisionModel=t},addPt:function(t){var e=new x(t);if(this._precisionModel.makePrecise(e),this.isRedundant(e))return null;this._ptList.add(e)},reverse:function(){},addPts:function(t,e){if(e)for(var n=0;n<t.length;n++)this.addPt(t[n]);else for(n=t.length-1;0<=n;n--)this.addPt(t[n])},isRedundant:function(t){if(this._ptList.size()<1)return!1;var e=this._ptList.get(this._ptList.size()-1);return t.distance(e)<this._minimimVertexDistance},toString:function(){return(new le).createLineString(this.getCoordinates()).toString()},closeRing:function(){if(this._ptList.size()<1)return null;var t=new x(this._ptList.get(0)),e=this._ptList.get(this._ptList.size()-1);if(2<=this._ptList.size()&&this._ptList.get(this._ptList.size()-2),t.equals(e))return null;this._ptList.add(t)},setMinimumVertexDistance:function(t){this._minimimVertexDistance=t},interfaces_:function(){return[]},getClass:function(){return pr}}),pr.COORDINATE_ARRAY_TYPE=new Array(0).fill(null),e(mr.prototype,{addNextSegment:function(t,e){if(this._s0=this._s1,this._s1=this._s2,this._s2=t,this._seg0.setCoordinates(this._s0,this._s1),this.computeOffsetSegment(this._seg0,this._side,this._distance,this._offset0),this._seg1.setCoordinates(this._s1,this._s2),this.computeOffsetSegment(this._seg1,this._side,this._distance,this._offset1),this._s1.equals(this._s2))return null;var n=V.index(this._s0,this._s1,this._s2),i=n===V.CLOCKWISE&&this._side===Vn.LEFT||n===V.COUNTERCLOCKWISE&&this._side===Vn.RIGHT;0===n?this.addCollinear(e):i?this.addOutsideTurn(n,e):this.addInsideTurn(n,e)},addLineEndCap:function(t,e){var n=new me(t,e),i=new me;this.computeOffsetSegment(n,Vn.LEFT,this._distance,i);var r=new me;this.computeOffsetSegment(n,Vn.RIGHT,this._distance,r);var s=e.x-t.x,o=e.y-t.y,a=Math.atan2(o,s);switch(this._bufParams.getEndCapStyle()){case ar.CAP_ROUND:this._segList.addPt(i.p1),this.addDirectedFillet(e,a+Math.PI/2,a-Math.PI/2,V.CLOCKWISE,this._distance),this._segList.addPt(r.p1);break;case ar.CAP_FLAT:this._segList.addPt(i.p1),this._segList.addPt(r.p1);break;case ar.CAP_SQUARE:var u=new x;u.x=Math.abs(this._distance)*Math.cos(a),u.y=Math.abs(this._distance)*Math.sin(a);var l=new x(i.p1.x+u.x,i.p1.y+u.y),h=new x(r.p1.x+u.x,r.p1.y+u.y);this._segList.addPt(l),this._segList.addPt(h)}},getCoordinates:function(){return this._segList.getCoordinates()},addMitreJoin:function(t,e,n,i){var r=!0,s=null;try{s=k.intersection(e.p0,e.p1,n.p0,n.p1),(i<=0?1:s.distance(t)/Math.abs(i))>this._bufParams.getMitreLimit()&&(r=!1)}catch(t){if(!(t instanceof A))throw t;s=new x(0,0),r=!1}r?this._segList.addPt(s):this.addLimitedMitreJoin(e,n,i,this._bufParams.getMitreLimit())},addOutsideTurn:function(t,e){if(this._offset0.p1.distance(this._offset1.p0)<this._distance*mr.OFFSET_SEGMENT_SEPARATION_FACTOR)return this._segList.addPt(this._offset0.p1),null;this._bufParams.getJoinStyle()===ar.JOIN_MITRE?this.addMitreJoin(this._s1,this._offset0,this._offset1,this._distance):this._bufParams.getJoinStyle()===ar.JOIN_BEVEL?this.addBevelJoin(this._offset0,this._offset1):(e&&this._segList.addPt(this._offset0.p1),this.addCornerFillet(this._s1,this._offset0.p1,this._offset1.p0,t,this._distance),this._segList.addPt(this._offset1.p0))},createSquare:function(t){this._segList.addPt(new x(t.x+this._distance,t.y+this._distance)),this._segList.addPt(new x(t.x+this._distance,t.y-this._distance)),this._segList.addPt(new x(t.x-this._distance,t.y-this._distance)),this._segList.addPt(new x(t.x-this._distance,t.y+this._distance)),this._segList.closeRing()},addSegments:function(t,e){this._segList.addPts(t,e)},addFirstSegment:function(){this._segList.addPt(this._offset1.p0)},addCornerFillet:function(t,e,n,i,r){var s=e.x-t.x,o=e.y-t.y,a=Math.atan2(o,s),u=n.x-t.x,l=n.y-t.y,h=Math.atan2(l,u);i===V.CLOCKWISE?a<=h&&(a+=2*Math.PI):h<=a&&(a-=2*Math.PI),this._segList.addPt(e),this.addDirectedFillet(t,a,h,i,r),this._segList.addPt(n)},addLastSegment:function(){this._segList.addPt(this._offset1.p1)},initSideSegments:function(t,e,n){this._s1=t,this._s2=e,this._side=n,this._seg1.setCoordinates(t,e),this.computeOffsetSegment(this._seg1,n,this._distance,this._offset1)},addLimitedMitreJoin:function(t,e,n,i){var r=this._seg0.p1,s=xe.angle(r,this._seg0.p0),o=(xe.angle(r,this._seg1.p1),xe.angleBetweenOriented(this._seg0.p0,r,this._seg1.p1)/2),a=xe.normalize(s+o),u=xe.normalize(a+Math.PI),l=i*n,h=n-l*Math.abs(Math.sin(o)),c=new me(r,new x(r.x+l*Math.cos(u),r.y+l*Math.sin(u))),f=c.pointAlongOffset(1,h),g=c.pointAlongOffset(1,-h);this._side===Vn.LEFT?(this._segList.addPt(f),this._segList.addPt(g)):(this._segList.addPt(g),this._segList.addPt(f))},addDirectedFillet:function(t,e,n,i,r){var s=i===V.CLOCKWISE?-1:1,o=Math.abs(e-n),a=Math.trunc(o/this._filletAngleQuantum+.5);if(a<1)return null;var u;u=o/a;for(var l=0,h=new x;l<o;){var c=e+s*l;h.x=t.x+r*Math.cos(c),h.y=t.y+r*Math.sin(c),this._segList.addPt(h),l+=u}},computeOffsetSegment:function(t,e,n,i){var r=e===Vn.LEFT?1:-1,s=t.p1.x-t.p0.x,o=t.p1.y-t.p0.y,a=Math.sqrt(s*s+o*o),u=r*n*s/a,l=r*n*o/a;i.p0.x=t.p0.x-l,i.p0.y=t.p0.y+u,i.p1.x=t.p1.x-l,i.p1.y=t.p1.y+u},addInsideTurn:function(t,e){if(this._li.computeIntersection(this._offset0.p0,this._offset0.p1,this._offset1.p0,this._offset1.p1),this._li.hasIntersection())this._segList.addPt(this._li.getIntersection(0));else if(this._hasNarrowConcaveAngle=!0,this._offset0.p1.distance(this._offset1.p0)<this._distance*mr.INSIDE_TURN_VERTEX_SNAP_DISTANCE_FACTOR)this._segList.addPt(this._offset0.p1);else{if(this._segList.addPt(this._offset0.p1),0<this._closingSegLengthFactor){var n=new x((this._closingSegLengthFactor*this._offset0.p1.x+this._s1.x)/(this._closingSegLengthFactor+1),(this._closingSegLengthFactor*this._offset0.p1.y+this._s1.y)/(this._closingSegLengthFactor+1));this._segList.addPt(n);var i=new x((this._closingSegLengthFactor*this._offset1.p0.x+this._s1.x)/(this._closingSegLengthFactor+1),(this._closingSegLengthFactor*this._offset1.p0.y+this._s1.y)/(this._closingSegLengthFactor+1));this._segList.addPt(i)}else this._segList.addPt(this._s1);this._segList.addPt(this._offset1.p0)}},createCircle:function(t){var e=new x(t.x+this._distance,t.y);this._segList.addPt(e),this.addDirectedFillet(t,0,2*Math.PI,-1,this._distance),this._segList.closeRing()},addBevelJoin:function(t,e){this._segList.addPt(t.p1),this._segList.addPt(e.p0)},init:function(t){this._distance=t,this._maxCurveSegmentError=t*(1-Math.cos(this._filletAngleQuantum/2)),this._segList=new pr,this._segList.setPrecisionModel(this._precisionModel),this._segList.setMinimumVertexDistance(t*mr.CURVE_VERTEX_SNAP_DISTANCE_FACTOR)},addCollinear:function(t){this._li.computeIntersection(this._s0,this._s1,this._s1,this._s2),2<=this._li.getIntersectionNum()&&(this._bufParams.getJoinStyle()===ar.JOIN_BEVEL||this._bufParams.getJoinStyle()===ar.JOIN_MITRE?(t&&this._segList.addPt(this._offset0.p1),this._segList.addPt(this._offset1.p0)):this.addCornerFillet(this._s1,this._offset0.p1,this._offset1.p0,V.CLOCKWISE,this._distance))},closeRing:function(){this._segList.closeRing()},hasNarrowConcaveAngle:function(){return this._hasNarrowConcaveAngle},interfaces_:function(){return[]},getClass:function(){return mr}}),mr.OFFSET_SEGMENT_SEPARATION_FACTOR=.001,mr.INSIDE_TURN_VERTEX_SNAP_DISTANCE_FACTOR=.001,mr.CURVE_VERTEX_SNAP_DISTANCE_FACTOR=1e-6,mr.MAX_CLOSING_SEG_LEN_FACTOR=80,e(vr.prototype,{getOffsetCurve:function(t,e){if(0===(this._distance=e))return null;var n=e<0,i=Math.abs(e),r=this.getSegGen(i);t.length<=1?this.computePointCurve(t[0],r):this.computeOffsetCurve(t,n,r);var s=r.getCoordinates();return n&&nt.reverse(s),s},computeSingleSidedBufferCurve:function(t,e,n){var i=this.simplifyTolerance(this._distance);if(e){n.addSegments(t,!0);var r=_r.simplify(t,-i),s=r.length-1;n.initSideSegments(r[s],r[s-1],Vn.LEFT),n.addFirstSegment();for(var o=s-2;0<=o;o--)n.addNextSegment(r[o],!0)}else{n.addSegments(t,!1);var a=_r.simplify(t,i),u=a.length-1;n.initSideSegments(a[0],a[1],Vn.LEFT),n.addFirstSegment();for(o=2;o<=u;o++)n.addNextSegment(a[o],!0)}n.addLastSegment(),n.closeRing()},computeRingBufferCurve:function(t,e,n){var i=this.simplifyTolerance(this._distance);e===Vn.RIGHT&&(i=-i);var r=_r.simplify(t,i),s=r.length-1;n.initSideSegments(r[s-1],r[0],e);for(var o=1;o<=s;o++){var a=1!==o;n.addNextSegment(r[o],a)}n.closeRing()},computeLineBufferCurve:function(t,e){var n=this.simplifyTolerance(this._distance),i=_r.simplify(t,n),r=i.length-1;e.initSideSegments(i[0],i[1],Vn.LEFT);for(var s=2;s<=r;s++)e.addNextSegment(i[s],!0);e.addLastSegment(),e.addLineEndCap(i[r-1],i[r]);var o=_r.simplify(t,-n),a=o.length-1;e.initSideSegments(o[a],o[a-1],Vn.LEFT);for(s=a-2;0<=s;s--)e.addNextSegment(o[s],!0);e.addLastSegment(),e.addLineEndCap(o[1],o[0]),e.closeRing()},computePointCurve:function(t,e){switch(this._bufParams.getEndCapStyle()){case ar.CAP_ROUND:e.createCircle(t);break;case ar.CAP_SQUARE:e.createSquare(t)}},getLineCurve:function(t,e){if((this._distance=e)<0&&!this._bufParams.isSingleSided())return null;if(0===e)return null;var n=Math.abs(e),i=this.getSegGen(n);if(t.length<=1)this.computePointCurve(t[0],i);else if(this._bufParams.isSingleSided()){var r=e<0;this.computeSingleSidedBufferCurve(t,r,i)}else this.computeLineBufferCurve(t,i);return i.getCoordinates()},getBufferParameters:function(){return this._bufParams},simplifyTolerance:function(t){return t*this._bufParams.getSimplifyFactor()},getRingCurve:function(t,e,n){if(this._distance=n,t.length<=2)return this.getLineCurve(t,n);if(0===n)return vr.copyCoordinates(t);var i=this.getSegGen(n);return this.computeRingBufferCurve(t,e,i),i.getCoordinates()},computeOffsetCurve:function(t,e,n){var i=this.simplifyTolerance(this._distance);if(e){var r=_r.simplify(t,-i),s=r.length-1;n.initSideSegments(r[s],r[s-1],Vn.LEFT),n.addFirstSegment();for(var o=s-2;0<=o;o--)n.addNextSegment(r[o],!0)}else{var a=_r.simplify(t,i),u=a.length-1;n.initSideSegments(a[0],a[1],Vn.LEFT),n.addFirstSegment();for(o=2;o<=u;o++)n.addNextSegment(a[o],!0)}n.addLastSegment()},getSegGen:function(t){return new mr(this._precisionModel,this._bufParams,t)},interfaces_:function(){return[]},getClass:function(){return vr}}),vr.copyCoordinates=function(t){for(var e=new Array(t.length).fill(null),n=0;n<e.length;n++)e[n]=new x(t[n]);return e},e(yr.prototype,{findStabbedSegments:function(){if(1===arguments.length){for(var t=arguments[0],e=new P,n=this._subgraphs.iterator();n.hasNext();){var i=n.next(),r=i.getEnvelope();t.y<r.getMinY()||t.y>r.getMaxY()||this.findStabbedSegments(t,i.getDirectedEdges(),e)}return e}if(3===arguments.length)if(L(arguments[2],w)&&arguments[0]instanceof x&&arguments[1]instanceof oi){var s=arguments[0],o=arguments[1],a=arguments[2],u=o.getEdge().getCoordinates();for(n=0;n<u.length-1;n++){if(this._seg.p0=u[n],this._seg.p1=u[n+1],this._seg.p0.y>this._seg.p1.y&&this._seg.reverse(),!(Math.max(this._seg.p0.x,this._seg.p1.x)<s.x||this._seg.isHorizontal()||s.y<this._seg.p0.y||s.y>this._seg.p1.y||V.index(this._seg.p0,this._seg.p1,s)===V.RIGHT)){var l=o.getDepth(Vn.LEFT);this._seg.p0.equals(u[n])||(l=o.getDepth(Vn.RIGHT));var h=new xr(this._seg,l);a.add(h)}}}else if(L(arguments[2],w)&&arguments[0]instanceof x&&L(arguments[1],w)){var c=arguments[0],f=arguments[1],g=arguments[2];for(n=f.iterator();n.hasNext();){var d=n.next();d.isForward()&&this.findStabbedSegments(c,d,g)}}},getDepth:function(t){var e=this.findStabbedSegments(t);return 0===e.size()?0:De.min(e)._leftDepth},interfaces_:function(){return[]},getClass:function(){return yr}}),e(xr.prototype,{compareTo:function(t){var e=t;if(this._upwardSeg.minX()>=e._upwardSeg.maxX())return 1;if(this._upwardSeg.maxX()<=e._upwardSeg.minX())return-1;var n=this._upwardSeg.orientationIndex(e._upwardSeg);return 0!==n?n:0!==(n=-1*e._upwardSeg.orientationIndex(this._upwardSeg))?n:this._upwardSeg.compareTo(e._upwardSeg)},compareX:function(t,e){var n=t.p0.compareTo(e.p0);return 0!==n?n:t.p1.compareTo(e.p1)},toString:function(){return this._upwardSeg.toString()},interfaces_:function(){return[n]},getClass:function(){return xr}}),yr.DepthSegment=xr,e(Er.prototype,{addPoint:function(t){if(this._distance<=0)return null;var e=t.getCoordinates(),n=this._curveBuilder.getLineCurve(e,this._distance);this.addCurve(n,ve.EXTERIOR,ve.INTERIOR)},addPolygon:function(t){var e=this._distance,n=Vn.LEFT;this._distance<0&&(e=-this._distance,n=Vn.RIGHT);var i=t.getExteriorRing(),r=nt.removeRepeatedPoints(i.getCoordinates());if(this._distance<0&&this.isErodedCompletely(i,this._distance))return null;if(this._distance<=0&&r.length<3)return null;this.addPolygonRing(r,e,n,ve.EXTERIOR,ve.INTERIOR);for(var s=0;s<t.getNumInteriorRing();s++){var o=t.getInteriorRingN(s),a=nt.removeRepeatedPoints(o.getCoordinates());0<this._distance&&this.isErodedCompletely(o,-this._distance)||this.addPolygonRing(a,e,Vn.opposite(n),ve.INTERIOR,ve.EXTERIOR)}},isTriangleErodedCompletely:function(t,e){var n=new Ee(t[0],t[1],t[2]),i=n.inCentre();return X.pointToSegment(i,n.p0,n.p1)<Math.abs(e)},addLineString:function(t){if(this._distance<=0&&!this._curveBuilder.getBufferParameters().isSingleSided())return null;var e=nt.removeRepeatedPoints(t.getCoordinates()),n=this._curveBuilder.getLineCurve(e,this._distance);this.addCurve(n,ve.EXTERIOR,ve.INTERIOR)},addCurve:function(t,e,n){if(null===t||t.length<2)return null;var i=new Qi(t,new jn(0,ve.BOUNDARY,e,n));this._curveList.add(i)},getCurves:function(){return this.add(this._inputGeom),this._curveList},addPolygonRing:function(t,e,n,i,r){if(0===e&&t.length<Ht.MINIMUM_VALID_SIZE)return null;var s=i,o=r;t.length>=Ht.MINIMUM_VALID_SIZE&&V.isCCW(t)&&(s=r,o=i,n=Vn.opposite(n));var a=this._curveBuilder.getRingCurve(t,n,e);this.addCurve(a,s,o)},add:function(t){if(t.isEmpty())return null;if(t instanceof Ut)this.addPolygon(t);else if(t instanceof Bt)this.addLineString(t);else if(t instanceof Vt)this.addPoint(t);else if(t instanceof Xt)this.addCollection(t);else if(t instanceof wt)this.addCollection(t);else if(t instanceof Wt)this.addCollection(t);else{if(!(t instanceof Lt))throw new UnsupportedOperationException(t.getClass().getName());this.addCollection(t)}},isErodedCompletely:function(t,e){var n=t.getCoordinates();if(n.length<4)return e<0;if(4===n.length)return this.isTriangleErodedCompletely(n,e);var i=t.getEnvelopeInternal(),r=Math.min(i.getHeight(),i.getWidth());return e<0&&2*Math.abs(e)>r},addCollection:function(t){for(var e=0;e<t.getNumGeometries();e++){var n=t.getGeometryN(e);this.add(n)}},interfaces_:function(){return[]},getClass:function(){return Er}}),e(Ir.prototype,{getNextCW:function(t){this.getEdges();var e=this._edgeList.indexOf(t),n=e-1;return 0===e&&(n=this._edgeList.size()-1),this._edgeList.get(n)},propagateSideLabels:function(t){for(var e=ve.NONE,n=this.iterator();n.hasNext();){(s=(r=n.next()).getLabel()).isArea(t)&&s.getLocation(t,Vn.LEFT)!==ve.NONE&&(e=s.getLocation(t,Vn.LEFT))}if(e===ve.NONE)return null;var i=e;for(n=this.iterator();n.hasNext();){var r,s;if((s=(r=n.next()).getLabel()).getLocation(t,Vn.ON)===ve.NONE&&s.setLocation(t,Vn.ON,i),s.isArea(t)){var o=s.getLocation(t,Vn.LEFT),a=s.getLocation(t,Vn.RIGHT);if(a!==ve.NONE){if(a!==i)throw new si("side location conflict",r.getCoordinate());o===ve.NONE&&y.shouldNeverReachHere("found single null side (at "+r.getCoordinate()+")"),i=o}else y.isTrue(s.getLocation(t,Vn.LEFT)===ve.NONE,"found single null side"),s.setLocation(t,Vn.RIGHT,i),s.setLocation(t,Vn.LEFT,i)}}},getCoordinate:function(){var t=this.iterator();return t.hasNext()?t.next().getCoordinate():null},print:function(t){Y.out.println("EdgeEndStar:   "+this.getCoordinate());for(var e=this.iterator();e.hasNext();){e.next().print(t)}},isAreaLabelsConsistent:function(t){return this.computeEdgeEndLabels(t.getBoundaryNodeRule()),this.checkAreaLabelsConsistent(0)},checkAreaLabelsConsistent:function(t){var e=this.getEdges();if(e.size()<=0)return!0;var n=e.size()-1,i=e.get(n).getLabel().getLocation(t,Vn.LEFT);y.isTrue(i!==ve.NONE,"Found unlabelled area edge");for(var r=i,s=this.iterator();s.hasNext();){var o=s.next().getLabel();y.isTrue(o.isArea(t),"Found non-area edge");var a=o.getLocation(t,Vn.LEFT),u=o.getLocation(t,Vn.RIGHT);if(a===u)return!1;if(u!==r)return!1;r=a}return!0},findIndex:function(t){this.iterator();for(var e=0;e<this._edgeList.size();e++){if(this._edgeList.get(e)===t)return e}return-1},iterator:function(){return this.getEdges().iterator()},getEdges:function(){return null===this._edgeList&&(this._edgeList=new P(this._edgeMap.values())),this._edgeList},getLocation:function(t,e,n){return this._ptInAreaLocation[t]===ve.NONE&&(this._ptInAreaLocation[t]=Xe.locate(e,n[t].getGeometry())),this._ptInAreaLocation[t]},toString:function(){var t=new F;t.append("EdgeEndStar:   "+this.getCoordinate()),t.append("\n");for(var e=this.iterator();e.hasNext();){var n=e.next();t.append(n),t.append("\n")}return t.toString()},computeEdgeEndLabels:function(t){for(var e=this.iterator();e.hasNext();){e.next().computeLabel(t)}},computeLabelling:function(t){this.computeEdgeEndLabels(t[0].getBoundaryNodeRule()),this.propagateSideLabels(0),this.propagateSideLabels(1);for(var e=[!1,!1],n=this.iterator();n.hasNext();)for(var i=(s=n.next()).getLabel(),r=0;r<2;r++)i.isLine(r)&&i.getLocation(r)===ve.BOUNDARY&&(e[r]=!0);for(n=this.iterator();n.hasNext();){var s;for(i=(s=n.next()).getLabel(),r=0;r<2;r++)if(i.isAnyNull(r)){var o=ve.NONE;if(e[r])o=ve.EXTERIOR;else{var a=s.getCoordinate();o=this.getLocation(r,a,t)}i.setAllLocationsIfNull(r,o)}}},getDegree:function(){return this._edgeMap.size()},insertEdgeEnd:function(t,e){this._edgeMap.put(t,e),this._edgeList=null},interfaces_:function(){return[]},getClass:function(){return Ir}}),p(Nr,Ir),e(Nr.prototype,{linkResultDirectedEdges:function(){this.getResultAreaEdges();for(var t=null,e=null,n=this._SCANNING_FOR_INCOMING,i=0;i<this._resultAreaEdgeList.size();i++){var r=this._resultAreaEdgeList.get(i),s=r.getSym();if(r.getLabel().isArea())switch(null===t&&r.isInResult()&&(t=r),n){case this._SCANNING_FOR_INCOMING:if(!s.isInResult())continue;e=s,n=this._LINKING_TO_OUTGOING;break;case this._LINKING_TO_OUTGOING:if(!r.isInResult())continue;e.setNext(r),n=this._SCANNING_FOR_INCOMING}}if(n===this._LINKING_TO_OUTGOING){if(null===t)throw new si("no outgoing dirEdge found",this.getCoordinate());y.isTrue(t.isInResult(),"unable to link last incoming dirEdge"),e.setNext(t)}},insert:function(t){var e=t;this.insertEdgeEnd(e,e)},getRightmostEdge:function(){var t=this.getEdges(),e=t.size();if(e<1)return null;var n=t.get(0);if(1===e)return n;var i=t.get(e-1),r=n.getQuadrant(),s=i.getQuadrant();return xn.isNorthern(r)&&xn.isNorthern(s)?n:xn.isNorthern(r)||xn.isNorthern(s)?0!==n.getDy()?n:0!==i.getDy()?i:(y.shouldNeverReachHere("found two horizontal edges incident on node"),null):i},print:function(t){Y.out.println("DirectedEdgeStar: "+this.getCoordinate());for(var e=this.iterator();e.hasNext();){var n=e.next();t.print("out "),n.print(t),t.println(),t.print("in "),n.getSym().print(t),t.println()}},getResultAreaEdges:function(){if(null!==this._resultAreaEdgeList)return this._resultAreaEdgeList;this._resultAreaEdgeList=new P;for(var t=this.iterator();t.hasNext();){var e=t.next();(e.isInResult()||e.getSym().isInResult())&&this._resultAreaEdgeList.add(e)}return this._resultAreaEdgeList},updateLabelling:function(t){for(var e=this.iterator();e.hasNext();){var n=e.next().getLabel();n.setAllLocationsIfNull(0,t.getLocation(0)),n.setAllLocationsIfNull(1,t.getLocation(1))}},linkAllDirectedEdges:function(){this.getEdges();for(var t=null,e=null,n=this._edgeList.size()-1;0<=n;n--){var i=this._edgeList.get(n),r=i.getSym();null===e&&(e=r),null!==t&&r.setNext(t),t=i}e.setNext(t)},computeDepths:function(){if(1===arguments.length){var t=arguments[0],e=this.findIndex(t),n=(t.getLabel(),t.getDepth(Vn.LEFT)),i=t.getDepth(Vn.RIGHT),r=this.computeDepths(e+1,this._edgeList.size(),n);if(this.computeDepths(0,e,r)!==i)throw new si("depth mismatch at "+t.getCoordinate())}else if(3===arguments.length){for(var s=arguments[0],o=arguments[1],a=arguments[2],u=s;u<o;u++){var l=this._edgeList.get(u);l.getLabel();l.setEdgeDepths(Vn.RIGHT,a),a=l.getDepth(Vn.LEFT)}return a}},mergeSymLabels:function(){for(var t=this.iterator();t.hasNext();){var e=t.next();e.getLabel().merge(e.getSym().getLabel())}},linkMinimalDirectedEdges:function(t){for(var e=null,n=null,i=this._SCANNING_FOR_INCOMING,r=this._resultAreaEdgeList.size()-1;0<=r;r--){var s=this._resultAreaEdgeList.get(r),o=s.getSym();switch(null===e&&s.getEdgeRing()===t&&(e=s),i){case this._SCANNING_FOR_INCOMING:if(o.getEdgeRing()!==t)continue;n=o,i=this._LINKING_TO_OUTGOING;break;case this._LINKING_TO_OUTGOING:if(s.getEdgeRing()!==t)continue;n.setNextMin(s),i=this._SCANNING_FOR_INCOMING}}i===this._LINKING_TO_OUTGOING&&(y.isTrue(null!==e,"found null for first outgoing dirEdge"),y.isTrue(e.getEdgeRing()===t,"unable to link last incoming dirEdge"),n.setNextMin(e))},getOutgoingDegree:function(){if(0===arguments.length){for(var t=0,e=this.iterator();e.hasNext();){e.next().isInResult()&&t++}return t}if(1===arguments.length){var n=arguments[0];for(t=0,e=this.iterator();e.hasNext();){e.next().getEdgeRing()===n&&t++}return t}},getLabel:function(){return this._label},findCoveredLineEdges:function(){for(var t=ve.NONE,e=this.iterator();e.hasNext();){var n=(r=e.next()).getSym();if(!r.isLineEdge()){if(r.isInResult()){t=ve.INTERIOR;break}if(n.isInResult()){t=ve.EXTERIOR;break}}}if(t===ve.NONE)return null;var i=t;for(e=this.iterator();e.hasNext();){var r;n=(r=e.next()).getSym();r.isLineEdge()?r.getEdge().setCovered(i===ve.INTERIOR):(r.isInResult()&&(i=ve.EXTERIOR),n.isInResult()&&(i=ve.INTERIOR))}},computeLabelling:function(t){Ir.prototype.computeLabelling.call(this,t),this._label=new jn(ve.NONE);for(var e=this.iterator();e.hasNext();)for(var n=e.next().getEdge().getLabel(),i=0;i<2;i++){var r=n.getLocation(i);r!==ve.INTERIOR&&r!==ve.BOUNDARY||this._label.setLocation(i,ve.INTERIOR)}},interfaces_:function(){return[]},getClass:function(){return Nr}}),p(Cr,ai),e(Cr.prototype,{createNode:function(t){return new ni(t,new Nr)},interfaces_:function(){return[]},getClass:function(){return Cr}}),e(Sr.prototype,{compareTo:function(t){var e=t;return Sr.compareOriented(this._pts,this._orientation,e._pts,e._orientation)},interfaces_:function(){return[n]},getClass:function(){return Sr}}),Sr.orientation=function(t){return 1===nt.increasingDirection(t)},Sr.compareOriented=function(t,e,n,i){for(var r=e?1:-1,s=i?1:-1,o=e?t.length:-1,a=i?n.length:-1,u=e?0:t.length-1,l=i?0:n.length-1;;){var h=t[u].compareTo(n[l]);if(0!==h)return h;var c=(u+=r)===o,f=(l+=s)===a;if(c&&!f)return-1;if(!c&&f)return 1;if(c&&f)return 0}},e(Lr.prototype,{print:function(t){t.print("MULTILINESTRING ( ");for(var e=0;e<this._edges.size();e++){var n=this._edges.get(e);0<e&&t.print(","),t.print("(");for(var i=n.getCoordinates(),r=0;r<i.length;r++)0<r&&t.print(","),t.print(i[r].x+" "+i[r].y);t.println(")")}t.print(")  ")},addAll:function(t){for(var e=t.iterator();e.hasNext();)this.add(e.next())},findEdgeIndex:function(t){for(var e=0;e<this._edges.size();e++)if(this._edges.get(e).equals(t))return e;return-1},iterator:function(){return this._edges.iterator()},getEdges:function(){return this._edges},get:function(t){return this._edges.get(t)},findEqualEdge:function(t){var e=new Sr(t.getCoordinates());return this._ocaMap.get(e)},add:function(t){this._edges.add(t);var e=new Sr(t.getCoordinates());this._ocaMap.put(e,t)},interfaces_:function(){return[]},getClass:function(){return Lr}}),e(wr.prototype,{processIntersections:function(t,e,n,i){},isDone:function(){},interfaces_:function(){return[]},getClass:function(){return wr}}),e(Rr.prototype,{isTrivialIntersection:function(t,e,n,i){if(t===n&&1===this._li.getIntersectionNum()){if(Rr.isAdjacentSegments(e,i))return!0;if(t.isClosed()){var r=t.size()-1;if(0===e&&i===r||0===i&&e===r)return!0}}return!1},getProperIntersectionPoint:function(){return this._properIntersectionPoint},hasProperInteriorIntersection:function(){return this._hasProperInterior},getLineIntersector:function(){return this._li},hasProperIntersection:function(){return this._hasProper},processIntersections:function(t,e,n,i){if(t===n&&e===i)return null;this.numTests++;var r=t.getCoordinates()[e],s=t.getCoordinates()[e+1],o=n.getCoordinates()[i],a=n.getCoordinates()[i+1];this._li.computeIntersection(r,s,o,a),this._li.hasIntersection()&&(this.numIntersections++,this._li.isInteriorIntersection()&&(this.numInteriorIntersections++,this._hasInterior=!0),this.isTrivialIntersection(t,e,n,i)||(this._hasIntersection=!0,t.addIntersections(this._li,e,0),n.addIntersections(this._li,i,1),this._li.isProper()&&(this.numProperIntersections++,this._hasProper=!0,this._hasProperInterior=!0)))},hasIntersection:function(){return this._hasIntersection},isDone:function(){return!1},hasInteriorIntersection:function(){return this._hasInterior},interfaces_:function(){return[wr]},getClass:function(){return Rr}}),Rr.isAdjacentSegments=function(t,e){return 1===Math.abs(t-e)},e(Tr.prototype,{setWorkingPrecisionModel:function(t){this._workingPrecisionModel=t},insertUniqueEdge:function(t){var e=this._edgeList.findEqualEdge(t);if(null!==e){var n=e.getLabel(),i=t.getLabel();e.isPointwiseEqual(t)||(i=new jn(t.getLabel())).flip(),n.merge(i);var r=Tr.depthDelta(i),s=e.getDepthDelta()+r;e.setDepthDelta(s)}else this._edgeList.add(t),t.setDepthDelta(Tr.depthDelta(t.getLabel()))},buildSubgraphs:function(t,e){for(var n=new P,i=t.iterator();i.hasNext();){var r=i.next(),s=r.getRightmostCoordinate(),o=new yr(n).getDepth(s);r.computeDepth(o),r.findResultEdges(),n.add(r),e.add(r.getDirectedEdges(),r.getNodes())}},createSubgraphs:function(t){for(var e=new P,n=t.getNodes().iterator();n.hasNext();){var i=n.next();if(!i.isVisited()){var r=new hr;r.create(i),e.add(r)}}return De.sort(e,De.reverseOrder()),e},createEmptyResultGeometry:function(){return this._geomFact.createPolygon()},getNoder:function(t){if(null!==this._workingNoder)return this._workingNoder;var e=new er,n=new pe;return n.setPrecisionModel(t),e.setSegmentIntersector(new Rr(n)),e},buffer:function(t,e){var n=this._workingPrecisionModel;null===n&&(n=t.getPrecisionModel()),this._geomFact=t.getFactory();var i=new Er(t,e,new vr(n,this._bufParams)).getCurves();if(i.size()<=0)return this.createEmptyResultGeometry();this.computeNodedEdges(i,n),this._graph=new ui(new Cr),this._graph.addEdges(this._edgeList.getEdges());var r=this.createSubgraphs(this._graph),s=new dr(this._geomFact);this.buildSubgraphs(r,s);var o=s.getPolygons();return o.size()<=0?this.createEmptyResultGeometry():this._geomFact.buildGeometry(o)},computeNodedEdges:function(t,e){var n=this.getNoder(e);n.computeNodes(t);for(var i=n.getNodedSubstrings().iterator();i.hasNext();){var r=i.next(),s=r.getCoordinates();if(2!==s.length||!s[0].equals2D(s[1])){var o=r.getData(),a=new ei(r.getCoordinates(),new jn(o));this.insertUniqueEdge(a)}}},setNoder:function(t){this._workingNoder=t},interfaces_:function(){return[]},getClass:function(){return Tr}}),Tr.depthDelta=function(t){var e=t.getLocation(0,Vn.LEFT),n=t.getLocation(0,Vn.RIGHT);return e===ve.INTERIOR&&n===ve.EXTERIOR?1:e===ve.EXTERIOR&&n===ve.INTERIOR?-1:0},Tr.convertSegStrings=function(t){for(var e=new le,n=new P;t.hasNext();){var i=t.next(),r=e.createLineString(i.getCoordinates());n.add(r)}return e.buildGeometry(n)},e(Pr.prototype,{checkEndPtVertexIntersections:function(){if(0===arguments.length)for(var t=this._segStrings.iterator();t.hasNext();){var e=t.next().getCoordinates();this.checkEndPtVertexIntersections(e[0],this._segStrings),this.checkEndPtVertexIntersections(e[e.length-1],this._segStrings)}else if(2===arguments.length){var n=arguments[0];for(t=arguments[1].iterator();t.hasNext();){e=t.next().getCoordinates();for(var i=1;i<e.length-1;i++)if(e[i].equals(n))throw new v("found endpt/interior pt intersection at index "+i+" :pt "+n)}}},checkInteriorIntersections:function(){if(0===arguments.length)for(var t=this._segStrings.iterator();t.hasNext();)for(var e=t.next(),n=this._segStrings.iterator();n.hasNext();){var i=n.next();this.checkInteriorIntersections(e,i)}else if(2===arguments.length)for(var r=arguments[0],s=arguments[1],o=r.getCoordinates(),a=s.getCoordinates(),u=0;u<o.length-1;u++)for(var l=0;l<a.length-1;l++)this.checkInteriorIntersections(r,u,s,l);else if(4===arguments.length){var h=arguments[0],c=arguments[1],f=arguments[2],g=arguments[3];if(h===f&&c===g)return null;var d=h.getCoordinates()[c],_=h.getCoordinates()[c+1],p=f.getCoordinates()[g],m=f.getCoordinates()[g+1];if(this._li.computeIntersection(d,_,p,m),this._li.hasIntersection()&&(this._li.isProper()||this.hasInteriorIntersection(this._li,d,_)||this.hasInteriorIntersection(this._li,p,m)))throw new v("found non-noded intersection at "+d+"-"+_+" and "+p+"-"+m)}},checkValid:function(){this.checkEndPtVertexIntersections(),this.checkInteriorIntersections(),this.checkCollapses()},checkCollapses:function(){if(0===arguments.length)for(var t=this._segStrings.iterator();t.hasNext();){var e=t.next();this.checkCollapses(e)}else if(1===arguments.length){var n=arguments[0].getCoordinates();for(t=0;t<n.length-2;t++)this.checkCollapse(n[t],n[t+1],n[t+2])}},hasInteriorIntersection:function(t,e,n){for(var i=0;i<t.getIntersectionNum();i++){var r=t.getIntersection(i);if(!r.equals(e)&&!r.equals(n))return!0}return!1},checkCollapse:function(t,e,n){if(t.equals(n))throw new v("found non-noded collapse at "+Pr.fact.createLineString([t,e,n]))},interfaces_:function(){return[]},getClass:function(){return Pr}}),Pr.fact=new le,e(Or.prototype,{intersectsScaled:function(t,e){var n=Math.min(t.x,e.x),i=Math.max(t.x,e.x),r=Math.min(t.y,e.y),s=Math.max(t.y,e.y),o=this._maxx<n||this._minx>i||this._maxy<r||this._miny>s;if(o)return!1;var a=this.intersectsToleranceSquare(t,e);return y.isTrue(!(o&&a),"Found bad envelope test"),a},initCorners:function(t){this._minx=t.x-.5,this._maxx=t.x+.5,this._miny=t.y-.5,this._maxy=t.y+.5,this._corner[0]=new x(this._maxx,this._maxy),this._corner[1]=new x(this._minx,this._maxy),this._corner[2]=new x(this._minx,this._miny),this._corner[3]=new x(this._maxx,this._miny)},intersects:function(t,e){return 1===this._scaleFactor?this.intersectsScaled(t,e):(this.copyScaled(t,this._p0Scaled),this.copyScaled(e,this._p1Scaled),this.intersectsScaled(this._p0Scaled,this._p1Scaled))},scale:function(t){return Math.round(t*this._scaleFactor)},getCoordinate:function(){return this._originalPt},copyScaled:function(t,e){e.x=this.scale(t.x),e.y=this.scale(t.y)},getSafeEnvelope:function(){if(null===this._safeEnv){var t=Or.SAFE_ENV_EXPANSION_FACTOR/this._scaleFactor;this._safeEnv=new M(this._originalPt.x-t,this._originalPt.x+t,this._originalPt.y-t,this._originalPt.y+t)}return this._safeEnv},intersectsPixelClosure:function(t,e){return this._li.computeIntersection(t,e,this._corner[0],this._corner[1]),!!this._li.hasIntersection()||(this._li.computeIntersection(t,e,this._corner[1],this._corner[2]),!!this._li.hasIntersection()||(this._li.computeIntersection(t,e,this._corner[2],this._corner[3]),!!this._li.hasIntersection()||(this._li.computeIntersection(t,e,this._corner[3],this._corner[0]),!!this._li.hasIntersection())))},intersectsToleranceSquare:function(t,e){var n=!1,i=!1;return this._li.computeIntersection(t,e,this._corner[0],this._corner[1]),!!this._li.isProper()||(this._li.computeIntersection(t,e,this._corner[1],this._corner[2]),!!this._li.isProper()||(this._li.hasIntersection()&&(n=!0),this._li.computeIntersection(t,e,this._corner[2],this._corner[3]),!!this._li.isProper()||(this._li.hasIntersection()&&(i=!0),this._li.computeIntersection(t,e,this._corner[3],this._corner[0]),!!this._li.isProper()||(!(!n||!i)||(!!t.equals(this._pt)||!!e.equals(this._pt))))))},addSnappedNode:function(t,e){var n=t.getCoordinate(e),i=t.getCoordinate(e+1);return!!this.intersects(n,i)&&(t.addIntersection(this.getCoordinate(),e),!0)},interfaces_:function(){return[]},getClass:function(){return Or}}),Or.SAFE_ENV_EXPANSION_FACTOR=.75,e(br.prototype,{snap:function(){if(1===arguments.length){var t=arguments[0];return this.snap(t,null,-1)}if(3===arguments.length){var e=arguments[0],n=arguments[1],i=arguments[2],r=e.getSafeEnvelope(),s=new Mr(e,n,i);return this._index.query(r,{interfaces_:function(){return[Te]},visitItem:function(t){t.select(r,s)}}),s.isNodeAdded()}},interfaces_:function(){return[]},getClass:function(){return br}}),p(Mr,hn),e(Mr.prototype,{isNodeAdded:function(){return this._isNodeAdded},select:function(){if(!(2===arguments.length&&Number.isInteger(arguments[1])&&arguments[0]instanceof yn))return hn.prototype.select.apply(this,arguments);var t=arguments[0],e=arguments[1],n=t.getContext();if(null!==this._parentEdge&&n===this._parentEdge&&e===this._hotPixelVertexIndex)return null;this._isNodeAdded=this._hotPixel.addSnappedNode(n,e)},interfaces_:function(){return[]},getClass:function(){return Mr}}),br.HotPixelSnapAction=Mr,e(Dr.prototype,{processIntersections:function(t,e,n,i){if(t===n&&e===i)return null;var r=t.getCoordinates()[e],s=t.getCoordinates()[e+1],o=n.getCoordinates()[i],a=n.getCoordinates()[i+1];if(this._li.computeIntersection(r,s,o,a),this._li.hasIntersection()&&this._li.isInteriorIntersection()){for(var u=0;u<this._li.getIntersectionNum();u++)this._interiorIntersections.add(this._li.getIntersection(u));t.addIntersections(this._li,e,0),n.addIntersections(this._li,i,1)}},isDone:function(){return!1},getInteriorIntersections:function(){return this._interiorIntersections},interfaces_:function(){return[wr]},getClass:function(){return Dr}}),e(Ar.prototype,{checkCorrectness:function(t){var e=new Pr(Qi.getNodedSubstrings(t));try{e.checkValid()}catch(t){if(!(t instanceof D))throw t;t.printStackTrace()}},getNodedSubstrings:function(){return Qi.getNodedSubstrings(this._nodedSegStrings)},snapRound:function(t,e){var n=this.findInteriorIntersections(t,e);this.computeIntersectionSnaps(n),this.computeVertexSnaps(t)},findInteriorIntersections:function(t,e){var n=new Dr(e);return this._noder.setSegmentIntersector(n),this._noder.computeNodes(t),n.getInteriorIntersections()},computeVertexSnaps:function(){if(L(arguments[0],N))for(var t=arguments[0].iterator();t.hasNext();){var e=t.next();this.computeVertexSnaps(e)}else if(arguments[0]instanceof Qi)for(var n=arguments[0],i=n.getCoordinates(),r=0;r<i.length;r++){var s=new Or(i[r],this._scaleFactor,this._li);this._pointSnapper.snap(s,n,r)&&n.addIntersection(i[r],r)}},computeNodes:function(t){this._nodedSegStrings=t,this._noder=new er,this._pointSnapper=new br(this._noder.getIndex()),this.snapRound(t,this._li)},computeIntersectionSnaps:function(t){for(var e=t.iterator();e.hasNext();){var n=new Or(e.next(),this._scaleFactor,this._li);this._pointSnapper.snap(n)}},interfaces_:function(){return[$i]},getClass:function(){return Ar}}),e(Fr.prototype,{bufferFixedPrecision:function(t){var e=new ir(new Ar(new ae(1)),t.getScale()),n=new Tr(this._bufParams);n.setWorkingPrecisionModel(t),n.setNoder(e),this._resultGeometry=n.buffer(this._argGeom,this._distance)},bufferReducedPrecision:function(){if(0===arguments.length){for(var t=Fr.MAX_PRECISION_DIGITS;0<=t;t--){try{this.bufferReducedPrecision(t)}catch(t){if(!(t instanceof si))throw t;this._saveException=t}if(null!==this._resultGeometry)return null}throw this._saveException}if(1===arguments.length){var e=arguments[0],n=new ae(Fr.precisionScaleFactor(this._argGeom,this._distance,e));this.bufferFixedPrecision(n)}},computeGeometry:function(){if(this.bufferOriginalPrecision(),null!==this._resultGeometry)return null;var t=this._argGeom.getFactory().getPrecisionModel();t.getType()===ae.FIXED?this.bufferFixedPrecision(t):this.bufferReducedPrecision()},setQuadrantSegments:function(t){this._bufParams.setQuadrantSegments(t)},bufferOriginalPrecision:function(){try{var t=new Tr(this._bufParams);this._resultGeometry=t.buffer(this._argGeom,this._distance)}catch(t){if(!(t instanceof v))throw t;this._saveException=t}},getResultGeometry:function(t){return this._distance=t,this.computeGeometry(),this._resultGeometry},setEndCapStyle:function(t){this._bufParams.setEndCapStyle(t)},interfaces_:function(){return[]},getClass:function(){return Fr}}),Fr.bufferOp=function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];return new Fr(t).getResultGeometry(e)}if(3===arguments.length){if(Number.isInteger(arguments[2])&&arguments[0]instanceof K&&"number"==typeof arguments[1]){var n=arguments[0],i=arguments[1],r=arguments[2];return(a=new Fr(n)).setQuadrantSegments(r),a.getResultGeometry(i)}if(arguments[2]instanceof ar&&arguments[0]instanceof K&&"number"==typeof arguments[1]){var s=arguments[0],o=arguments[1];return(a=new Fr(s,arguments[2])).getResultGeometry(o)}}else if(4===arguments.length){var a,u=arguments[0],l=arguments[1],h=arguments[2],c=arguments[3];return(a=new Fr(u)).setQuadrantSegments(h),a.setEndCapStyle(c),a.getResultGeometry(l)}},Fr.precisionScaleFactor=function(t,e,n){var i=t.getEnvelopeInternal(),r=U.max(Math.abs(i.getMaxX()),Math.abs(i.getMaxY()),Math.abs(i.getMinX()),Math.abs(i.getMinY()))+2*(0<e?e:0),s=n-Math.trunc(Math.log(r)/Math.log(10)+1);return Math.pow(10,s)},Fr.CAP_ROUND=ar.CAP_ROUND,Fr.CAP_BUTT=ar.CAP_FLAT,Fr.CAP_FLAT=ar.CAP_FLAT,Fr.CAP_SQUARE=ar.CAP_SQUARE,Fr.MAX_PRECISION_DIGITS=12;var Gr=Object.freeze({BufferOp:Fr,BufferParameters:ar});function qr(){this._comps=null;var t=arguments[0];this._comps=t}function Br(){if(this._component=null,this._segIndex=null,this._pt=null,2===arguments.length){var t=arguments[0],e=arguments[1];Br.call(this,t,Br.INSIDE_AREA,e)}else if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];this._component=n,this._segIndex=i,this._pt=r}}function zr(){this._pts=null;var t=arguments[0];this._pts=t}function Vr(){this._locations=null;var t=arguments[0];this._locations=t}function Yr(){if(this._geom=null,this._terminateDistance=0,this._ptLocator=new zn,this._minDistanceLocation=null,this._minDistance=S.MAX_VALUE,2===arguments.length){var t=arguments[0],e=arguments[1];Yr.call(this,t,e,0)}else if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];this._geom=new Array(2).fill(null),this._geom[0]=n,this._geom[1]=i,this._terminateDistance=r}}e(qr.prototype,{filter:function(t){t instanceof Ut&&this._comps.add(t)},interfaces_:function(){return[Ct]},getClass:function(){return qr}}),qr.getPolygons=function(){if(1===arguments.length){var t=arguments[0];return qr.getPolygons(t,new P)}if(2===arguments.length){var e=arguments[0],n=arguments[1];return e instanceof Ut?n.add(e):e instanceof Lt&&e.apply(new qr(n)),n}},e(Br.prototype,{isInsideArea:function(){return this._segIndex===Br.INSIDE_AREA},getCoordinate:function(){return this._pt},getGeometryComponent:function(){return this._component},getSegmentIndex:function(){return this._segIndex},interfaces_:function(){return[]},getClass:function(){return Br}}),Br.INSIDE_AREA=-1,e(zr.prototype,{filter:function(t){t instanceof Vt&&this._pts.add(t)},interfaces_:function(){return[Ct]},getClass:function(){return zr}}),zr.getPoints=function(){if(1===arguments.length){var t=arguments[0];return t instanceof Vt?De.singletonList(t):zr.getPoints(t,new P)}if(2===arguments.length){var e=arguments[0],n=arguments[1];return e instanceof Vt?n.add(e):e instanceof Lt&&e.apply(new zr(n)),n}},e(Vr.prototype,{filter:function(t){(t instanceof Vt||t instanceof Bt||t instanceof Ut)&&this._locations.add(new Br(t,0,t.getCoordinate()))},interfaces_:function(){return[Ct]},getClass:function(){return Vr}}),Vr.getLocations=function(t){var e=new P;return t.apply(new Vr(e)),e},e(Yr.prototype,{computeContainmentDistance:function(){if(0===arguments.length){var t=new Array(2).fill(null);if(this.computeContainmentDistance(0,t),this._minDistance<=this._terminateDistance)return null;this.computeContainmentDistance(1,t)}else if(2===arguments.length){var e=arguments[0],n=arguments[1],i=this._geom[e];if(i.getDimension()<2)return null;var r=1-e,s=qr.getPolygons(i);if(0<s.size()){var o=Vr.getLocations(this._geom[r]);if(this.computeContainmentDistance(o,s,n),this._minDistance<=this._terminateDistance)return this._minDistanceLocation[r]=n[0],this._minDistanceLocation[e]=n[1],null}}else if(3===arguments.length)if(arguments[2]instanceof Array&&L(arguments[0],w)&&L(arguments[1],w)){for(var a=arguments[0],u=arguments[1],l=arguments[2],h=0;h<a.size();h++)for(var c=a.get(h),f=0;f<u.size();f++)if(this.computeContainmentDistance(c,u.get(f),l),this._minDistance<=this._terminateDistance)return null}else if(arguments[2]instanceof Array&&arguments[0]instanceof Br&&arguments[1]instanceof Ut){var g=arguments[0],d=arguments[1],_=arguments[2],p=g.getCoordinate();if(ve.EXTERIOR!==this._ptLocator.locate(p,d))return _[this._minDistance=0]=g,_[1]=new Br(d,p),null}},computeMinDistanceLinesPoints:function(t,e,n){for(var i=0;i<t.size();i++)for(var r=t.get(i),s=0;s<e.size();s++){var o=e.get(s);if(this.computeMinDistance(r,o,n),this._minDistance<=this._terminateDistance)return null}},computeFacetDistance:function(){var t=new Array(2).fill(null),e=Ge.getLines(this._geom[0]),n=Ge.getLines(this._geom[1]),i=zr.getPoints(this._geom[0]),r=zr.getPoints(this._geom[1]);return this.computeMinDistanceLines(e,n,t),this.updateMinDistance(t,!1),this._minDistance<=this._terminateDistance?null:(t[0]=null,t[1]=null,this.computeMinDistanceLinesPoints(e,r,t),this.updateMinDistance(t,!1),this._minDistance<=this._terminateDistance?null:(t[0]=null,t[1]=null,this.computeMinDistanceLinesPoints(n,i,t),this.updateMinDistance(t,!0),this._minDistance<=this._terminateDistance?null:(t[0]=null,t[1]=null,this.computeMinDistancePoints(i,r,t),void this.updateMinDistance(t,!1))))},nearestLocations:function(){return this.computeMinDistance(),this._minDistanceLocation},updateMinDistance:function(t,e){if(null===t[0])return null;e?(this._minDistanceLocation[0]=t[1],this._minDistanceLocation[1]=t[0]):(this._minDistanceLocation[0]=t[0],this._minDistanceLocation[1]=t[1])},nearestPoints:function(){return this.computeMinDistance(),[this._minDistanceLocation[0].getCoordinate(),this._minDistanceLocation[1].getCoordinate()]},computeMinDistance:function(){if(0===arguments.length){if(null!==this._minDistanceLocation)return null;if(this._minDistanceLocation=new Array(2).fill(null),this.computeContainmentDistance(),this._minDistance<=this._terminateDistance)return null;this.computeFacetDistance()}else if(3===arguments.length)if(arguments[2]instanceof Array&&arguments[0]instanceof Bt&&arguments[1]instanceof Vt){var t=arguments[0],e=arguments[1],n=arguments[2];if(t.getEnvelopeInternal().distance(e.getEnvelopeInternal())>this._minDistance)return null;for(var i=t.getCoordinates(),r=e.getCoordinate(),s=0;s<i.length-1;s++){if((f=X.pointToSegment(r,i[s],i[s+1]))<this._minDistance){this._minDistance=f;var o=new me(i[s],i[s+1]).closestPoint(r);n[0]=new Br(t,s,o),n[1]=new Br(e,0,r)}if(this._minDistance<=this._terminateDistance)return null}}else if(arguments[2]instanceof Array&&arguments[0]instanceof Bt&&arguments[1]instanceof Bt){var a=arguments[0],u=arguments[1],l=arguments[2];if(a.getEnvelopeInternal().distance(u.getEnvelopeInternal())>this._minDistance)return null;i=a.getCoordinates();var h=u.getCoordinates();for(s=0;s<i.length-1;s++)for(var c=0;c<h.length-1;c++){var f;if((f=X.segmentToSegment(i[s],i[s+1],h[c],h[c+1]))<this._minDistance){this._minDistance=f;var g=new me(i[s],i[s+1]),d=new me(h[c],h[c+1]),_=g.closestPoints(d);l[0]=new Br(a,s,_[0]),l[1]=new Br(u,c,_[1])}if(this._minDistance<=this._terminateDistance)return null}}},computeMinDistancePoints:function(t,e,n){for(var i=0;i<t.size();i++)for(var r=t.get(i),s=0;s<e.size();s++){var o=e.get(s),a=r.getCoordinate().distance(o.getCoordinate());if(a<this._minDistance&&(this._minDistance=a,n[0]=new Br(r,0,r.getCoordinate()),n[1]=new Br(o,0,o.getCoordinate())),this._minDistance<=this._terminateDistance)return null}},distance:function(){if(null===this._geom[0]||null===this._geom[1])throw new c("null geometries are not supported");return this._geom[0].isEmpty()||this._geom[1].isEmpty()?0:(this.computeMinDistance(),this._minDistance)},computeMinDistanceLines:function(t,e,n){for(var i=0;i<t.size();i++)for(var r=t.get(i),s=0;s<e.size();s++){var o=e.get(s);if(this.computeMinDistance(r,o,n),this._minDistance<=this._terminateDistance)return null}},interfaces_:function(){return[]},getClass:function(){return Yr}}),Yr.distance=function(t,e){return new Yr(t,e).distance()},Yr.isWithinDistance=function(t,e,n){return!(n<t.getEnvelopeInternal().distance(e.getEnvelopeInternal()))&&new Yr(t,e,n).distance()<=n},Yr.nearestPoints=function(t,e){return new Yr(t,e).nearestPoints()};var kr=Object.freeze({DistanceOp:Yr});function Ur(){this._factory=null,this._directedEdges=new P,this._coordinates=null;var t=arguments[0];this._factory=t}function Xr(){this._isMarked=!1,this._isVisited=!1,this._data=null}function Hr(){Xr.apply(this),this._parentEdge=null,this._from=null,this._to=null,this._p0=null,this._p1=null,this._sym=null,this._edgeDirection=null,this._quadrant=null,this._angle=null;var t=arguments[0],e=arguments[1],n=arguments[2],i=arguments[3];this._from=t,this._to=e,this._edgeDirection=i,this._p0=t.getCoordinate(),this._p1=n;var r=this._p1.x-this._p0.x,s=this._p1.y-this._p0.y;this._quadrant=xn.quadrant(r,s),this._angle=Math.atan2(s,r)}function Wr(){var t=arguments[0],e=arguments[1],n=arguments[2],i=arguments[3];Hr.call(this,t,e,n,i)}function jr(){if(Xr.apply(this),this._dirEdge=null,0===arguments.length);else if(2===arguments.length){var t=arguments[0],e=arguments[1];this.setDirectedEdges(t,e)}}function Kr(){this._outEdges=new P,this._sorted=!1}function Zr(){if(Xr.apply(this),this._pt=null,this._deStar=null,1===arguments.length){var t=arguments[0];Zr.call(this,t,new Kr)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this._pt=e,this._deStar=n}}function Qr(){jr.apply(this),this._line=null;var t=arguments[0];this._line=t}function Jr(){this._nodeMap=new pt}function $r(){this._edges=new ut,this._dirEdges=new ut,this._nodeMap=new Jr}function ts(){$r.apply(this)}function es(){this._graph=new ts,this._mergedLineStrings=null,this._factory=null,this._edgeStrings=null}e(Ur.prototype,{getCoordinates:function(){if(null===this._coordinates){for(var t=0,e=0,n=new b,i=this._directedEdges.iterator();i.hasNext();){var r=i.next();r.getEdgeDirection()?t++:e++,n.add(r.getEdge().getLine().getCoordinates(),!1,r.getEdgeDirection())}this._coordinates=n.toCoordinateArray(),t<e&&nt.reverse(this._coordinates)}return this._coordinates},toLineString:function(){return this._factory.createLineString(this.getCoordinates())},add:function(t){this._directedEdges.add(t)},interfaces_:function(){return[]},getClass:function(){return Ur}}),e(Xr.prototype,{setVisited:function(t){this._isVisited=t},isMarked:function(){return this._isMarked},setData:function(t){this._data=t},getData:function(){return this._data},setMarked:function(t){this._isMarked=t},getContext:function(){return this._data},isVisited:function(){return this._isVisited},setContext:function(t){this._data=t},interfaces_:function(){return[]},getClass:function(){return Xr}}),Xr.getComponentWithVisitedState=function(t,e){for(;t.hasNext();){var n=t.next();if(n.isVisited()===e)return n}return null},Xr.setVisited=function(t,e){for(;t.hasNext();){t.next().setVisited(e)}},Xr.setMarked=function(t,e){for(;t.hasNext();){t.next().setMarked(e)}},p(Hr,Xr),e(Hr.prototype,{isRemoved:function(){return null===this._parentEdge},compareDirection:function(t){return this._quadrant>t._quadrant?1:this._quadrant<t._quadrant?-1:V.index(t._p0,t._p1,this._p1)},getCoordinate:function(){return this._from.getCoordinate()},print:function(t){var e=this.getClass().getName(),n=e.lastIndexOf("."),i=e.substring(n+1);t.print("  "+i+": "+this._p0+" - "+this._p1+" "+this._quadrant+":"+this._angle)},getDirectionPt:function(){return this._p1},getAngle:function(){return this._angle},compareTo:function(t){var e=t;return this.compareDirection(e)},getFromNode:function(){return this._from},getSym:function(){return this._sym},setEdge:function(t){this._parentEdge=t},remove:function(){this._sym=null,this._parentEdge=null},getEdge:function(){return this._parentEdge},getQuadrant:function(){return this._quadrant},setSym:function(t){this._sym=t},getToNode:function(){return this._to},getEdgeDirection:function(){return this._edgeDirection},interfaces_:function(){return[n]},getClass:function(){return Hr}}),Hr.toEdges=function(t){for(var e=new P,n=t.iterator();n.hasNext();)e.add(n.next()._parentEdge);return e},p(Wr,Hr),e(Wr.prototype,{getNext:function(){return 2!==this.getToNode().getDegree()?null:this.getToNode().getOutEdges().getEdges().get(0)===this.getSym()?this.getToNode().getOutEdges().getEdges().get(1):(y.isTrue(this.getToNode().getOutEdges().getEdges().get(1)===this.getSym()),this.getToNode().getOutEdges().getEdges().get(0))},interfaces_:function(){return[]},getClass:function(){return Wr}}),p(jr,Xr),e(jr.prototype,{isRemoved:function(){return null===this._dirEdge},setDirectedEdges:function(t,e){this._dirEdge=[t,e],t.setEdge(this),e.setEdge(this),t.setSym(e),e.setSym(t),t.getFromNode().addOutEdge(t),e.getFromNode().addOutEdge(e)},getDirEdge:function(){if(Number.isInteger(arguments[0])){var t=arguments[0];return this._dirEdge[t]}if(arguments[0]instanceof Zr){var e=arguments[0];return this._dirEdge[0].getFromNode()===e?this._dirEdge[0]:this._dirEdge[1].getFromNode()===e?this._dirEdge[1]:null}},remove:function(){this._dirEdge=null},getOppositeNode:function(t){return this._dirEdge[0].getFromNode()===t?this._dirEdge[0].getToNode():this._dirEdge[1].getFromNode()===t?this._dirEdge[1].getToNode():null},interfaces_:function(){return[]},getClass:function(){return jr}}),e(Kr.prototype,{getNextEdge:function(t){var e=this.getIndex(t);return this._outEdges.get(this.getIndex(e+1))},getCoordinate:function(){var t=this.iterator();return t.hasNext()?t.next().getCoordinate():null},iterator:function(){return this.sortEdges(),this._outEdges.iterator()},sortEdges:function(){this._sorted||(De.sort(this._outEdges),this._sorted=!0)},remove:function(t){this._outEdges.remove(t)},getEdges:function(){return this.sortEdges(),this._outEdges},getNextCWEdge:function(t){var e=this.getIndex(t);return this._outEdges.get(this.getIndex(e-1))},getIndex:function(){if(arguments[0]instanceof jr){var t=arguments[0];this.sortEdges();for(var e=0;e<this._outEdges.size();e++){if(this._outEdges.get(e).getEdge()===t)return e}return-1}if(arguments[0]instanceof Hr){var n=arguments[0];this.sortEdges();for(e=0;e<this._outEdges.size();e++){if(this._outEdges.get(e)===n)return e}return-1}if(Number.isInteger(arguments[0])){var i=arguments[0]%this._outEdges.size();return i<0&&(i+=this._outEdges.size()),i}},add:function(t){this._outEdges.add(t),this._sorted=!1},getDegree:function(){return this._outEdges.size()},interfaces_:function(){return[]},getClass:function(){return Kr}}),p(Zr,Xr),e(Zr.prototype,{isRemoved:function(){return null===this._pt},addOutEdge:function(t){this._deStar.add(t)},getCoordinate:function(){return this._pt},getOutEdges:function(){return this._deStar},remove:function(){if(0===arguments.length)this._pt=null;else if(1===arguments.length){var t=arguments[0];this._deStar.remove(t)}},getIndex:function(t){return this._deStar.getIndex(t)},getDegree:function(){return this._deStar.getDegree()},interfaces_:function(){return[]},getClass:function(){return Zr}}),Zr.getEdgesBetween=function(t,e){var n=new ut(Hr.toEdges(t.getOutEdges().getEdges())),i=Hr.toEdges(e.getOutEdges().getEdges());return n.retainAll(i),n},p(Qr,jr),e(Qr.prototype,{getLine:function(){return this._line},interfaces_:function(){return[]},getClass:function(){return Qr}}),e(Jr.prototype,{find:function(t){return this._nodeMap.get(t)},iterator:function(){return this._nodeMap.values().iterator()},remove:function(t){return this._nodeMap.remove(t)},values:function(){return this._nodeMap.values()},add:function(t){return this._nodeMap.put(t.getCoordinate(),t),t},interfaces_:function(){return[]},getClass:function(){return Jr}}),e($r.prototype,{findNodesOfDegree:function(t){for(var e=new P,n=this.nodeIterator();n.hasNext();){var i=n.next();i.getDegree()===t&&e.add(i)}return e},dirEdgeIterator:function(){return this._dirEdges.iterator()},edgeIterator:function(){return this._edges.iterator()},remove:function(){if(arguments[0]instanceof jr){var t=arguments[0];this.remove(t.getDirEdge(0)),this.remove(t.getDirEdge(1)),this._edges.remove(t),t.remove()}else if(arguments[0]instanceof Hr){var e=arguments[0];null!==(r=e.getSym())&&r.setSym(null),e.getFromNode().remove(e),e.remove(),this._dirEdges.remove(e)}else if(arguments[0]instanceof Zr){for(var n=arguments[0],i=n.getOutEdges().getEdges().iterator();i.hasNext();){var r,s=i.next();null!==(r=s.getSym())&&this.remove(r),this._dirEdges.remove(s);var o=s.getEdge();null!==o&&this._edges.remove(o)}this._nodeMap.remove(n.getCoordinate()),n.remove()}},findNode:function(t){return this._nodeMap.find(t)},getEdges:function(){return this._edges},nodeIterator:function(){return this._nodeMap.iterator()},contains:function(){if(arguments[0]instanceof jr){var t=arguments[0];return this._edges.contains(t)}if(arguments[0]instanceof Hr){var e=arguments[0];return this._dirEdges.contains(e)}},add:function(){if(arguments[0]instanceof Zr){var t=arguments[0];this._nodeMap.add(t)}else if(arguments[0]instanceof jr){var e=arguments[0];this._edges.add(e),this.add(e.getDirEdge(0)),this.add(e.getDirEdge(1))}else if(arguments[0]instanceof Hr){var n=arguments[0];this._dirEdges.add(n)}},getNodes:function(){return this._nodeMap.values()},interfaces_:function(){return[]},getClass:function(){return $r}}),p(ts,$r),e(ts.prototype,{addEdge:function(t){if(t.isEmpty())return null;var e=nt.removeRepeatedPoints(t.getCoordinates());if(e.length<=1)return null;var n=e[0],i=e[e.length-1],r=this.getNode(n),s=this.getNode(i),o=new Wr(r,s,e[1],!0),a=new Wr(s,r,e[e.length-2],!1),u=new Qr(t);u.setDirectedEdges(o,a),this.add(u)},getNode:function(t){var e=this.findNode(t);return null===e&&(e=new Zr(t),this.add(e)),e},interfaces_:function(){return[]},getClass:function(){return ts}}),e(es.prototype,{buildEdgeStringsForUnprocessedNodes:function(){for(var t=this._graph.getNodes().iterator();t.hasNext();){var e=t.next();e.isMarked()||(y.isTrue(2===e.getDegree()),this.buildEdgeStringsStartingAt(e),e.setMarked(!0))}},buildEdgeStringsForNonDegree2Nodes:function(){for(var t=this._graph.getNodes().iterator();t.hasNext();){var e=t.next();2!==e.getDegree()&&(this.buildEdgeStringsStartingAt(e),e.setMarked(!0))}},buildEdgeStringsForObviousStartNodes:function(){this.buildEdgeStringsForNonDegree2Nodes()},getMergedLineStrings:function(){return this.merge(),this._mergedLineStrings},buildEdgeStringsStartingAt:function(t){for(var e=t.getOutEdges().iterator();e.hasNext();){var n=e.next();n.getEdge().isMarked()||this._edgeStrings.add(this.buildEdgeStringStartingWith(n))}},merge:function(){if(null!==this._mergedLineStrings)return null;Xr.setMarked(this._graph.nodeIterator(),!1),Xr.setMarked(this._graph.edgeIterator(),!1),this._edgeStrings=new P,this.buildEdgeStringsForObviousStartNodes(),this.buildEdgeStringsForIsolatedLoops(),this._mergedLineStrings=new P;for(var t=this._edgeStrings.iterator();t.hasNext();){var e=t.next();this._mergedLineStrings.add(e.toLineString())}},buildEdgeStringStartingWith:function(t){for(var e=new Ur(this._factory),n=t;e.add(n),n.getEdge().setMarked(!0),null!==(n=n.getNext())&&n!==t;);return e},add:function(){if(arguments[0]instanceof K)arguments[0].apply({interfaces_:function(){return[j]},filter:function(t){t instanceof Bt&&this.add(t)}});else if(L(arguments[0],N)){var t=arguments[0];this._mergedLineStrings=null;for(var e=t.iterator();e.hasNext();){var n=e.next();this.add(n)}}else if(arguments[0]instanceof Bt){var i=arguments[0];null===this._factory&&(this._factory=i.getFactory()),this._graph.addEdge(i)}},buildEdgeStringsForIsolatedLoops:function(){this.buildEdgeStringsForUnprocessedNodes()},interfaces_:function(){return[]},getClass:function(){return es}});var ns=Object.freeze({LineMerger:es});function is(){this._pts=null,this._data=null;var t=arguments[0],e=arguments[1];this._pts=t,this._data=e}function rs(){this._findAllIntersections=!1,this._isCheckEndSegmentsOnly=!1,this._li=null,this._interiorIntersection=null,this._intSegments=null,this._intersections=new P,this._intersectionCount=0,this._keepIntersections=!0;var t=arguments[0];this._li=t,this._interiorIntersection=null}function ss(){this._li=new pe,this._segStrings=null,this._findAllIntersections=!1,this._segInt=null,this._isValid=!0;var t=arguments[0];this._segStrings=t}function os(){this._nv=null;var t=arguments[0];this._nv=new ss(os.toSegmentStrings(t))}function as(){this._mapOp=null;var t=arguments[0];this._mapOp=t}function us(){this._op=null,this._geometryFactory=null,this._ptLocator=null,this._lineEdgesList=new P,this._resultLineList=new P;var t=arguments[0],e=arguments[1],n=arguments[2];this._op=t,this._geometryFactory=e,this._ptLocator=n}function ls(){this._op=null,this._geometryFactory=null,this._resultPointList=new P;var t=arguments[0],e=arguments[1];this._op=t,this._geometryFactory=e}function hs(){if(this._snapTolerance=0,this._srcPts=null,this._seg=new me,this._allowSnappingToSourceVertices=!1,this._isClosed=!1,arguments[0]instanceof Bt&&"number"==typeof arguments[1]){var t=arguments[0],e=arguments[1];hs.call(this,t.getCoordinates(),e)}else if(arguments[0]instanceof Array&&"number"==typeof arguments[1]){var n=arguments[0],i=arguments[1];this._srcPts=n,this._isClosed=hs.isClosed(n),this._snapTolerance=i}}function cs(){this._srcGeom=null;var t=arguments[0];this._srcGeom=t}function fs(){if(Tn.apply(this),this._snapTolerance=null,this._snapPts=null,this._isSelfSnap=!1,2===arguments.length){var t=arguments[0],e=arguments[1];this._snapTolerance=t,this._snapPts=e}else if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];this._snapTolerance=n,this._snapPts=i,this._isSelfSnap=r}}function gs(){this._isFirst=!0,this._commonMantissaBitsCount=53,this._commonBits=new h,this._commonSignExp=null}function ds(){this._commonCoord=null,this._ccFilter=new _s}function _s(){this._commonBitsX=new gs,this._commonBitsY=new gs}function ps(){this.trans=null;var t=arguments[0];this.trans=t}function ms(){this._geom=new Array(2).fill(null),this._snapTolerance=null,this._cbr=null;var t=arguments[0],e=arguments[1];this._geom[0]=t,this._geom[1]=e,this.computeSnapTolerance()}function vs(){this._geom=new Array(2).fill(null);var t=arguments[0],e=arguments[1];this._geom[0]=t,this._geom[1]=e}function ys(){if(this._li=new pe,this._resultPrecisionModel=null,this._arg=null,1===arguments.length){var t=arguments[0];this.setComputationPrecision(t.getPrecisionModel()),this._arg=new Array(1).fill(null),this._arg[0]=new li(0,t)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];ys.call(this,e,n,Q.OGC_SFS_BOUNDARY_RULE)}else if(3===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2];0<=i.getPrecisionModel().compareTo(r.getPrecisionModel())?this.setComputationPrecision(i.getPrecisionModel()):this.setComputationPrecision(r.getPrecisionModel()),this._arg=new Array(2).fill(null),this._arg[0]=new li(0,i,s),this._arg[1]=new li(1,r,s)}}function xs(){this._ptLocator=new zn,this._geomFact=null,this._resultGeom=null,this._graph=null,this._edgeList=new Lr,this._resultPolyList=new P,this._resultLineList=new P,this._resultPointList=new P;var t=arguments[0],e=arguments[1];ys.call(this,t,e),this._graph=new ui(new Cr),this._geomFact=t.getFactory()}e(is.prototype,{getCoordinates:function(){return this._pts},size:function(){return this._pts.length},getCoordinate:function(t){return this._pts[t]},isClosed:function(){return this._pts[0].equals(this._pts[this._pts.length-1])},getSegmentOctant:function(t){return t===this._pts.length-1?-1:ji.octant(this.getCoordinate(t),this.getCoordinate(t+1))},setData:function(t){this._data=t},getData:function(){return this._data},toString:function(){return de.toLineString(new $t(this._pts))},interfaces_:function(){return[Ki]},getClass:function(){return is}}),e(rs.prototype,{getInteriorIntersection:function(){return this._interiorIntersection},setCheckEndSegmentsOnly:function(t){this._isCheckEndSegmentsOnly=t},getIntersectionSegments:function(){return this._intSegments},count:function(){return this._intersectionCount},getIntersections:function(){return this._intersections},setFindAllIntersections:function(t){this._findAllIntersections=t},setKeepIntersections:function(t){this._keepIntersections=t},processIntersections:function(t,e,n,i){if(!this._findAllIntersections&&this.hasIntersection())return null;if(t===n&&e===i)return null;if(this._isCheckEndSegmentsOnly&&!(this.isEndSegment(t,e)||this.isEndSegment(n,i)))return null;var r=t.getCoordinates()[e],s=t.getCoordinates()[e+1],o=n.getCoordinates()[i],a=n.getCoordinates()[i+1];this._li.computeIntersection(r,s,o,a),this._li.hasIntersection()&&this._li.isInteriorIntersection()&&(this._intSegments=new Array(4).fill(null),this._intSegments[0]=r,this._intSegments[1]=s,this._intSegments[2]=o,this._intSegments[3]=a,this._interiorIntersection=this._li.getIntersection(0),this._keepIntersections&&this._intersections.add(this._interiorIntersection),this._intersectionCount++)},isEndSegment:function(t,e){return 0===e||e>=t.size()-2},hasIntersection:function(){return null!==this._interiorIntersection},isDone:function(){return!this._findAllIntersections&&null!==this._interiorIntersection},interfaces_:function(){return[wr]},getClass:function(){return rs}}),rs.createAllIntersectionsFinder=function(t){var e=new rs(t);return e.setFindAllIntersections(!0),e},rs.createAnyIntersectionFinder=function(t){return new rs(t)},rs.createIntersectionCounter=function(t){var e=new rs(t);return e.setFindAllIntersections(!0),e.setKeepIntersections(!1),e},e(ss.prototype,{execute:function(){if(null!==this._segInt)return null;this.checkInteriorIntersections()},getIntersections:function(){return this._segInt.getIntersections()},isValid:function(){return this.execute(),this._isValid},setFindAllIntersections:function(t){this._findAllIntersections=t},checkInteriorIntersections:function(){this._isValid=!0,this._segInt=new rs(this._li),this._segInt.setFindAllIntersections(this._findAllIntersections);var t=new er;if(t.setSegmentIntersector(this._segInt),t.computeNodes(this._segStrings),this._segInt.hasIntersection())return this._isValid=!1,null},checkValid:function(){if(this.execute(),!this._isValid)throw new si(this.getErrorMessage(),this._segInt.getInteriorIntersection())},getErrorMessage:function(){if(this._isValid)return"no intersections found";var t=this._segInt.getIntersectionSegments();return"found non-noded intersection between "+de.toLineString(t[0],t[1])+" and "+de.toLineString(t[2],t[3])},interfaces_:function(){return[]},getClass:function(){return ss}}),ss.computeIntersections=function(t){var e=new ss(t);return e.setFindAllIntersections(!0),e.isValid(),e.getIntersections()},e(os.prototype,{checkValid:function(){this._nv.checkValid()},interfaces_:function(){return[]},getClass:function(){return os}}),os.toSegmentStrings=function(t){for(var e=new P,n=t.iterator();n.hasNext();){var i=n.next();e.add(new is(i.getCoordinates(),i))}return e},os.checkValid=function(t){new os(t).checkValid()},e(as.prototype,{map:function(t){for(var e=new P,n=0;n<t.getNumGeometries();n++){var i=this._mapOp.map(t.getGeometryN(n));i.isEmpty()||e.add(i)}return t.getFactory().createGeometryCollection(le.toGeometryArray(e))},interfaces_:function(){return[]},getClass:function(){return as}}),as.map=function(t,e){return new as(e).map(t)},e(us.prototype,{collectLines:function(t){for(var e=this._op.getGraph().getEdgeEnds().iterator();e.hasNext();){var n=e.next();this.collectLineEdge(n,t,this._lineEdgesList),this.collectBoundaryTouchEdge(n,t,this._lineEdgesList)}},labelIsolatedLine:function(t,e){var n=this._ptLocator.locate(t.getCoordinate(),this._op.getArgGeometry(e));t.getLabel().setLocation(e,n)},build:function(t){return this.findCoveredLineEdges(),this.collectLines(t),this.buildLines(t),this._resultLineList},collectLineEdge:function(t,e,n){var i=t.getLabel(),r=t.getEdge();t.isLineEdge()&&(t.isVisited()||!xs.isResultOfOp(i,e)||r.isCovered()||(n.add(r),t.setVisitedEdge(!0)))},findCoveredLineEdges:function(){for(var t=this._op.getGraph().getNodes().iterator();t.hasNext();){t.next().getEdges().findCoveredLineEdges()}for(var e=this._op.getGraph().getEdgeEnds().iterator();e.hasNext();){var n=e.next(),i=n.getEdge();if(n.isLineEdge()&&!i.isCoveredSet()){var r=this._op.isCoveredByA(n.getCoordinate());i.setCovered(r)}}},labelIsolatedLines:function(t){for(var e=t.iterator();e.hasNext();){var n=e.next(),i=n.getLabel();n.isIsolated()&&(i.isNull(0)?this.labelIsolatedLine(n,0):this.labelIsolatedLine(n,1))}},buildLines:function(t){for(var e=this._lineEdgesList.iterator();e.hasNext();){var n=e.next(),i=(n.getLabel(),this._geometryFactory.createLineString(n.getCoordinates()));this._resultLineList.add(i),n.setInResult(!0)}},collectBoundaryTouchEdge:function(t,e,n){var i=t.getLabel();return t.isLineEdge()?null:t.isVisited()?null:t.isInteriorAreaEdge()?null:t.getEdge().isInResult()?null:(y.isTrue(!(t.isInResult()||t.getSym().isInResult())||!t.getEdge().isInResult()),void(xs.isResultOfOp(i,e)&&e===xs.INTERSECTION&&(n.add(t.getEdge()),t.setVisitedEdge(!0))))},interfaces_:function(){return[]},getClass:function(){return us}}),e(ls.prototype,{filterCoveredNodeToPoint:function(t){var e=t.getCoordinate();if(!this._op.isCoveredByLA(e)){var n=this._geometryFactory.createPoint(e);this._resultPointList.add(n)}},extractNonCoveredResultNodes:function(t){for(var e=this._op.getGraph().getNodes().iterator();e.hasNext();){var n=e.next();if(!n.isInResult()&&(!n.isIncidentEdgeInResult()&&(0===n.getEdges().getDegree()||t===xs.INTERSECTION))){var i=n.getLabel();xs.isResultOfOp(i,t)&&this.filterCoveredNodeToPoint(n)}}},build:function(t){return this.extractNonCoveredResultNodes(t),this._resultPointList},interfaces_:function(){return[]},getClass:function(){return ls}}),e(hs.prototype,{snapVertices:function(t,e){for(var n=this._isClosed?t.size()-1:t.size(),i=0;i<n;i++){var r=t.get(i),s=this.findSnapForVertex(r,e);null!==s&&(t.set(i,new x(s)),0===i&&this._isClosed&&t.set(t.size()-1,new x(s)))}},findSnapForVertex:function(t,e){for(var n=0;n<e.length;n++){if(t.equals2D(e[n]))return null;if(t.distance(e[n])<this._snapTolerance)return e[n]}return null},snapTo:function(t){var e=new b(this._srcPts);return this.snapVertices(e,t),this.snapSegments(e,t),e.toCoordinateArray()},snapSegments:function(t,e){if(0===e.length)return null;var n=e.length;e[0].equals2D(e[e.length-1])&&(n=e.length-1);for(var i=0;i<n;i++){var r=e[i],s=this.findSegmentIndexToSnap(r,t);0<=s&&t.add(s+1,new x(r),!1)}},findSegmentIndexToSnap:function(t,e){for(var n=S.MAX_VALUE,i=-1,r=0;r<e.size()-1;r++){if(this._seg.p0=e.get(r),this._seg.p1=e.get(r+1),this._seg.p0.equals2D(t)||this._seg.p1.equals2D(t)){if(this._allowSnappingToSourceVertices)continue;return-1}var s=this._seg.distance(t);s<this._snapTolerance&&s<n&&(n=s,i=r)}return i},setAllowSnappingToSourceVertices:function(t){this._allowSnappingToSourceVertices=t},interfaces_:function(){return[]},getClass:function(){return hs}}),hs.isClosed=function(t){return!(t.length<=1)&&t[0].equals2D(t[t.length-1])},e(cs.prototype,{snapTo:function(t,e){return new fs(e,this.extractTargetCoordinates(t)).transform(this._srcGeom)},snapToSelf:function(t,e){var n=new fs(t,this.extractTargetCoordinates(this._srcGeom),!0).transform(this._srcGeom),i=n;return e&&L(i,kt)&&(i=n.buffer(0)),i},computeSnapTolerance:function(t){return this.computeMinimumSegmentLength(t)/10},extractTargetCoordinates:function(t){for(var e=new yt,n=t.getCoordinates(),i=0;i<n.length;i++)e.add(n[i]);return e.toArray(new Array(0).fill(null))},computeMinimumSegmentLength:function(t){for(var e=S.MAX_VALUE,n=0;n<t.length-1;n++){var i=t[n].distance(t[n+1]);i<e&&(e=i)}return e},interfaces_:function(){return[]},getClass:function(){return cs}}),cs.snap=function(t,e,n){var i=new Array(2).fill(null),r=new cs(t);i[0]=r.snapTo(e,n);var s=new cs(e);return i[1]=s.snapTo(i[0],n),i},cs.computeOverlaySnapTolerance=function(){if(1===arguments.length){var t=arguments[0],e=cs.computeSizeBasedSnapTolerance(t),n=t.getPrecisionModel();if(n.getType()===ae.FIXED){var i=1/n.getScale()*2/1.415;e<i&&(e=i)}return e}if(2===arguments.length){var r=arguments[0],s=arguments[1];return Math.min(cs.computeOverlaySnapTolerance(r),cs.computeOverlaySnapTolerance(s))}},cs.computeSizeBasedSnapTolerance=function(t){var e=t.getEnvelopeInternal();return Math.min(e.getHeight(),e.getWidth())*cs.SNAP_PRECISION_FACTOR},cs.snapToSelf=function(t,e,n){return new cs(t).snapToSelf(e,n)},cs.SNAP_PRECISION_FACTOR=1e-9,p(fs,Tn),e(fs.prototype,{snapLine:function(t,e){var n=new hs(t,this._snapTolerance);return n.setAllowSnappingToSourceVertices(this._isSelfSnap),n.snapTo(e)},transformCoordinates:function(t,e){var n=t.toCoordinateArray(),i=this.snapLine(n,this._snapPts);return this._factory.getCoordinateSequenceFactory().create(i)},interfaces_:function(){return[]},getClass:function(){return fs}}),e(gs.prototype,{getCommon:function(){return S.longBitsToDouble(this._commonBits)},add:function(t){var e=S.doubleToLongBits(t);return this._isFirst?(this._commonBits=e,this._commonSignExp=gs.signExpBits(this._commonBits),this._isFirst=!1,null):gs.signExpBits(e)!==this._commonSignExp?(this._commonBits.high=0,this._commonBits.low=0,null):(this._commonMantissaBitsCount=gs.numCommonMostSigMantissaBits(this._commonBits,e),void(this._commonBits=gs.zeroLowerBits(this._commonBits,64-(12+this._commonMantissaBitsCount))))},toString:function(){if(1===arguments.length){var t=arguments[0],e=S.longBitsToDouble(t),n="0000000000000000000000000000000000000000000000000000000000000000"+h.toBinaryString(t),i=n.substring(n.length-64);return i.substring(0,1)+"  "+i.substring(1,12)+"(exp) "+i.substring(12)+" [ "+e+" ]"}},interfaces_:function(){return[]},getClass:function(){return gs}}),gs.getBit=function(t,e){var n=1<<e%32;return e<32?0!=(t.low&n)?1:0:0!=(t.high&n)?1:0},gs.signExpBits=function(t){return t.high>>>20},gs.zeroLowerBits=function(t,e){var n="low";if(32<e&&(t.low=0,e%=32,n="high"),0<e){var i=e<32?~((1<<e)-1):0;t[n]&=i}return t},gs.numCommonMostSigMantissaBits=function(t,e){for(var n=0,i=52;0<=i;i--){if(gs.getBit(t,i)!==gs.getBit(e,i))return n;n++}return 52},e(ds.prototype,{addCommonBits:function(t){var e=new ps(this._commonCoord);t.apply(e),t.geometryChanged()},removeCommonBits:function(t){if(0===this._commonCoord.x&&0===this._commonCoord.y)return t;var e=new x(this._commonCoord);e.x=-e.x,e.y=-e.y;var n=new ps(e);return t.apply(n),t.geometryChanged(),t},getCommonCoordinate:function(){return this._commonCoord},add:function(t){t.apply(this._ccFilter),this._commonCoord=this._ccFilter.getCommonCoordinate()},interfaces_:function(){return[]},getClass:function(){return ds}}),e(_s.prototype,{filter:function(t){this._commonBitsX.add(t.x),this._commonBitsY.add(t.y)},getCommonCoordinate:function(){return new x(this._commonBitsX.getCommon(),this._commonBitsY.getCommon())},interfaces_:function(){return[Z]},getClass:function(){return _s}}),e(ps.prototype,{filter:function(t,e){var n=t.getOrdinate(e,0)+this.trans.x,i=t.getOrdinate(e,1)+this.trans.y;t.setOrdinate(e,0,n),t.setOrdinate(e,1,i)},isDone:function(){return!1},isGeometryChanged:function(){return!0},interfaces_:function(){return[St]},getClass:function(){return ps}}),ds.CommonCoordinateFilter=_s,ds.Translater=ps,e(ms.prototype,{selfSnap:function(t){return new cs(t).snapTo(t,this._snapTolerance)},removeCommonBits:function(t){this._cbr=new ds,this._cbr.add(t[0]),this._cbr.add(t[1]);var e=new Array(2).fill(null);return e[0]=this._cbr.removeCommonBits(t[0].copy()),e[1]=this._cbr.removeCommonBits(t[1].copy()),e},prepareResult:function(t){return this._cbr.addCommonBits(t),t},getResultGeometry:function(t){var e=this.snap(this._geom),n=xs.overlayOp(e[0],e[1],t);return this.prepareResult(n)},checkValid:function(t){t.isValid()||Y.out.println("Snapped geometry is invalid")},computeSnapTolerance:function(){this._snapTolerance=cs.computeOverlaySnapTolerance(this._geom[0],this._geom[1])},snap:function(t){var e=this.removeCommonBits(t);return cs.snap(e[0],e[1],this._snapTolerance)},interfaces_:function(){return[]},getClass:function(){return ms}}),ms.overlayOp=function(t,e,n){return new ms(t,e).getResultGeometry(n)},ms.union=function(t,e){return ms.overlayOp(t,e,xs.UNION)},ms.intersection=function(t,e){return ms.overlayOp(t,e,xs.INTERSECTION)},ms.symDifference=function(t,e){return ms.overlayOp(t,e,xs.SYMDIFFERENCE)},ms.difference=function(t,e){return ms.overlayOp(t,e,xs.DIFFERENCE)},e(vs.prototype,{getResultGeometry:function(t){var e=null,n=!1,i=null;try{e=xs.overlayOp(this._geom[0],this._geom[1],t),n=!0}catch(t){if(!(t instanceof v))throw t;i=t}if(!n)try{e=ms.overlayOp(this._geom[0],this._geom[1],t)}catch(t){throw t instanceof v?i:t}return e},interfaces_:function(){return[]},getClass:function(){return vs}}),vs.overlayOp=function(t,e,n){return new vs(t,e).getResultGeometry(n)},vs.union=function(t,e){return vs.overlayOp(t,e,xs.UNION)},vs.intersection=function(t,e){return vs.overlayOp(t,e,xs.INTERSECTION)},vs.symDifference=function(t,e){return vs.overlayOp(t,e,xs.SYMDIFFERENCE)},vs.difference=function(t,e){return vs.overlayOp(t,e,xs.DIFFERENCE)},e(ys.prototype,{getArgGeometry:function(t){return this._arg[t].getGeometry()},setComputationPrecision:function(t){this._resultPrecisionModel=t,this._li.setPrecisionModel(this._resultPrecisionModel)},interfaces_:function(){return[]},getClass:function(){return ys}}),p(xs,ys),e(xs.prototype,{insertUniqueEdge:function(t){var e=this._edgeList.findEqualEdge(t);if(null!==e){var n=e.getLabel(),i=t.getLabel();e.isPointwiseEqual(t)||(i=new jn(t.getLabel())).flip();var r=e.getDepth();r.isNull()&&r.add(n),r.add(i),n.merge(i)}else this._edgeList.add(t)},getGraph:function(){return this._graph},cancelDuplicateResultEdges:function(){for(var t=this._graph.getEdgeEnds().iterator();t.hasNext();){var e=t.next(),n=e.getSym();e.isInResult()&&n.isInResult()&&(e.setInResult(!1),n.setInResult(!1))}},isCoveredByLA:function(t){return!!this.isCovered(t,this._resultLineList)||!!this.isCovered(t,this._resultPolyList)},computeGeometry:function(t,e,n,i){var r=new P;return r.addAll(t),r.addAll(e),r.addAll(n),r.isEmpty()?xs.createEmptyResult(i,this._arg[0].getGeometry(),this._arg[1].getGeometry(),this._geomFact):this._geomFact.buildGeometry(r)},mergeSymLabels:function(){for(var t=this._graph.getNodes().iterator();t.hasNext();){t.next().getEdges().mergeSymLabels()}},isCovered:function(t,e){for(var n=e.iterator();n.hasNext();){var i=n.next();if(this._ptLocator.locate(t,i)!==ve.EXTERIOR)return!0}return!1},replaceCollapsedEdges:function(){for(var t=new P,e=this._edgeList.iterator();e.hasNext();){var n=e.next();n.isCollapsed()&&(e.remove(),t.add(n.getCollapsedEdge()))}this._edgeList.addAll(t)},updateNodeLabelling:function(){for(var t=this._graph.getNodes().iterator();t.hasNext();){var e=t.next(),n=e.getEdges().getLabel();e.getLabel().merge(n)}},getResultGeometry:function(t){return this.computeOverlay(t),this._resultGeom},insertUniqueEdges:function(t){for(var e=t.iterator();e.hasNext();){var n=e.next();this.insertUniqueEdge(n)}},computeOverlay:function(t){this.copyPoints(0),this.copyPoints(1),this._arg[0].computeSelfNodes(this._li,!1),this._arg[1].computeSelfNodes(this._li,!1),this._arg[0].computeEdgeIntersections(this._arg[1],this._li,!0);var e=new P;this._arg[0].computeSplitEdges(e),this._arg[1].computeSplitEdges(e),this.insertUniqueEdges(e),this.computeLabelsFromDepths(),this.replaceCollapsedEdges(),os.checkValid(this._edgeList.getEdges()),this._graph.addEdges(this._edgeList.getEdges()),this.computeLabelling(),this.labelIncompleteNodes(),this.findResultAreaEdges(t),this.cancelDuplicateResultEdges();var n=new dr(this._geomFact);n.add(this._graph),this._resultPolyList=n.getPolygons();var i=new us(this,this._geomFact,this._ptLocator);this._resultLineList=i.build(t);var r=new ls(this,this._geomFact,this._ptLocator);this._resultPointList=r.build(t),this._resultGeom=this.computeGeometry(this._resultPointList,this._resultLineList,this._resultPolyList,t)},labelIncompleteNode:function(t,e){var n=this._ptLocator.locate(t.getCoordinate(),this._arg[e].getGeometry());t.getLabel().setLocation(e,n)},copyPoints:function(t){for(var e=this._arg[t].getNodeIterator();e.hasNext();){var n=e.next();this._graph.addNode(n.getCoordinate()).setLabel(t,n.getLabel().getLocation(t))}},findResultAreaEdges:function(t){for(var e=this._graph.getEdgeEnds().iterator();e.hasNext();){var n=e.next(),i=n.getLabel();i.isArea()&&!n.isInteriorAreaEdge()&&xs.isResultOfOp(i.getLocation(0,Vn.RIGHT),i.getLocation(1,Vn.RIGHT),t)&&n.setInResult(!0)}},computeLabelsFromDepths:function(){for(var t=this._edgeList.iterator();t.hasNext();){var e=t.next(),n=e.getLabel(),i=e.getDepth();if(!i.isNull()){i.normalize();for(var r=0;r<2;r++)n.isNull(r)||!n.isArea()||i.isNull(r)||(0===i.getDelta(r)?n.toLine(r):(y.isTrue(!i.isNull(r,Vn.LEFT),"depth of LEFT side has not been initialized"),n.setLocation(r,Vn.LEFT,i.getLocation(r,Vn.LEFT)),y.isTrue(!i.isNull(r,Vn.RIGHT),"depth of RIGHT side has not been initialized"),n.setLocation(r,Vn.RIGHT,i.getLocation(r,Vn.RIGHT))))}}},computeLabelling:function(){for(var t=this._graph.getNodes().iterator();t.hasNext();){t.next().getEdges().computeLabelling(this._arg)}this.mergeSymLabels(),this.updateNodeLabelling()},labelIncompleteNodes:function(){for(var t=this._graph.getNodes().iterator();t.hasNext();){var e=t.next(),n=e.getLabel();e.isIsolated()&&(n.isNull(0)?this.labelIncompleteNode(e,0):this.labelIncompleteNode(e,1)),e.getEdges().updateLabelling(n)}},isCoveredByA:function(t){return!!this.isCovered(t,this._resultPolyList)},interfaces_:function(){return[]},getClass:function(){return xs}}),xs.overlayOp=function(t,e,n){return new xs(t,e).getResultGeometry(n)},xs.union=function(t,e){if(t.isEmpty()||e.isEmpty()){if(t.isEmpty()&&e.isEmpty())return xs.createEmptyResult(xs.UNION,t,e,t.getFactory());if(t.isEmpty())return e.copy();if(e.isEmpty())return t.copy()}if(t.isGeometryCollection()||e.isGeometryCollection())throw new c("This method does not support GeometryCollection arguments");return vs.overlayOp(t,e,xs.UNION)},xs.intersection=function(t,e){if(t.isEmpty()||e.isEmpty())return xs.createEmptyResult(xs.INTERSECTION,t,e,t.getFactory());if(t.isGeometryCollection()){var n=e;return as.map(t,{interfaces_:function(){return[MapOp]},map:function(t){return t.intersection(n)}})}if(t.isGeometryCollection()||e.isGeometryCollection())throw new c("This method does not support GeometryCollection arguments");return vs.overlayOp(t,e,xs.INTERSECTION)},xs.symDifference=function(t,e){if(t.isEmpty()||e.isEmpty()){if(t.isEmpty()&&e.isEmpty())return xs.createEmptyResult(xs.SYMDIFFERENCE,t,e,t.getFactory());if(t.isEmpty())return e.copy();if(e.isEmpty())return t.copy()}if(t.isGeometryCollection()||e.isGeometryCollection())throw new c("This method does not support GeometryCollection arguments");return vs.overlayOp(t,e,xs.SYMDIFFERENCE)},xs.resultDimension=function(t,e,n){var i=e.getDimension(),r=n.getDimension(),s=-1;switch(t){case xs.INTERSECTION:s=Math.min(i,r);break;case xs.UNION:s=Math.max(i,r);break;case xs.DIFFERENCE:s=i;break;case xs.SYMDIFFERENCE:s=Math.max(i,r)}return s},xs.createEmptyResult=function(t,e,n,i){var r=null;switch(xs.resultDimension(t,e,n)){case-1:r=i.createGeometryCollection();break;case 0:r=i.createPoint();break;case 1:r=i.createLineString();break;case 2:r=i.createPolygon()}return r},xs.difference=function(t,e){if(t.isEmpty())return xs.createEmptyResult(xs.DIFFERENCE,t,e,t.getFactory());if(e.isEmpty())return t.copy();if(t.isGeometryCollection()||e.isGeometryCollection())throw new c("This method does not support GeometryCollection arguments");return vs.overlayOp(t,e,xs.DIFFERENCE)},xs.isResultOfOp=function(){if(2===arguments.length){var t=arguments[0],e=arguments[1],n=t.getLocation(0),i=t.getLocation(1);return xs.isResultOfOp(n,i,e)}if(3===arguments.length){var r=arguments[0],s=arguments[1],o=arguments[2];switch(r===ve.BOUNDARY&&(r=ve.INTERIOR),s===ve.BOUNDARY&&(s=ve.INTERIOR),o){case xs.INTERSECTION:return r===ve.INTERIOR&&s===ve.INTERIOR;case xs.UNION:return r===ve.INTERIOR||s===ve.INTERIOR;case xs.DIFFERENCE:return r===ve.INTERIOR&&s!==ve.INTERIOR;case xs.SYMDIFFERENCE:return r===ve.INTERIOR&&s!==ve.INTERIOR||r!==ve.INTERIOR&&s===ve.INTERIOR}return!1}},xs.INTERSECTION=1,xs.UNION=2,xs.DIFFERENCE=3,xs.SYMDIFFERENCE=4;var Es=Object.freeze({OverlayOp:xs});function Is(){this._edgeRing=null,this._next=null,this._label=-1;var t=arguments[0],e=arguments[1],n=arguments[2],i=arguments[3];Hr.call(this,t,e,n,i)}function Ns(){jr.apply(this),this._line=null;var t=arguments[0];this._line=t}function Cs(){this._factory=null,this._deList=new P,this._lowestEdge=null,this._ring=null,this._ringPts=null,this._holes=null,this._shell=null,this._isHole=null,this._isProcessed=!1,this._isIncludedSet=!1,this._isIncluded=!1;var t=arguments[0];this._factory=t}function Ss(){}function Ls(){$r.apply(this),this._factory=null;var t=arguments[0];this._factory=t}function ws(){if(this._lineStringAdder=new Rs(this),this._graph=null,this._dangles=new P,this._cutEdges=new P,this._invalidRingLines=new P,this._holeList=null,this._shellList=null,this._polyList=null,this._isCheckingRingsValid=!0,this._extractOnlyPolygonal=null,this._geomFactory=null,0===arguments.length)ws.call(this,!1);else if(1===arguments.length){var t=arguments[0];this._extractOnlyPolygonal=t}}function Rs(){this.p=null;var t=arguments[0];this.p=t}p(Is,Hr),e(Is.prototype,{getNext:function(){return this._next},isInRing:function(){return null!==this._edgeRing},setRing:function(t){this._edgeRing=t},setLabel:function(t){this._label=t},getLabel:function(){return this._label},setNext:function(t){this._next=t},getRing:function(){return this._edgeRing},interfaces_:function(){return[]},getClass:function(){return Is}}),p(Ns,jr),e(Ns.prototype,{getLine:function(){return this._line},interfaces_:function(){return[]},getClass:function(){return Ns}}),e(Cs.prototype,{isIncluded:function(){return this._isIncluded},getCoordinates:function(){if(null===this._ringPts){for(var t=new b,e=this._deList.iterator();e.hasNext();){var n=e.next(),i=n.getEdge();Cs.addEdge(i.getLine().getCoordinates(),n.getEdgeDirection(),t)}this._ringPts=t.toCoordinateArray()}return this._ringPts},isIncludedSet:function(){return this._isIncludedSet},isValid:function(){return this.getCoordinates(),!(this._ringPts.length<=3)&&(this.getRing(),this._ring.isValid())},build:function(t){for(var e=t;this.add(e),e.setRing(this),e=e.getNext(),y.isTrue(null!==e,"found null DE in ring"),y.isTrue(e===t||!e.isInRing(),"found DE already in ring"),e!==t;);},isOuterHole:function(){return!!this._isHole&&!this.hasShell()},getPolygon:function(){var t=null;if(null!==this._holes){t=new Array(this._holes.size()).fill(null);for(var e=0;e<this._holes.size();e++)t[e]=this._holes.get(e)}return this._factory.createPolygon(this._ring,t)},isHole:function(){return this._isHole},isProcessed:function(){return this._isProcessed},addHole:function(){if(arguments[0]instanceof Ht){var t=arguments[0];null===this._holes&&(this._holes=new P),this._holes.add(t)}else if(arguments[0]instanceof Cs){var e=arguments[0];e.setShell(this);var n=e.getRing();null===this._holes&&(this._holes=new P),this._holes.add(n)}},setIncluded:function(t){this._isIncluded=t,this._isIncludedSet=!0},getOuterHole:function(){if(this.isHole())return null;for(var t=0;t<this._deList.size();t++){var e=this._deList.get(t).getSym().getRing();if(e.isOuterHole())return e}return null},computeHole:function(){var t=this.getRing();this._isHole=V.isCCW(t.getCoordinates())},hasShell:function(){return null!==this._shell},isOuterShell:function(){return null!==this.getOuterHole()},getLineString:function(){return this.getCoordinates(),this._factory.createLineString(this._ringPts)},toString:function(){return de.toLineString(new $t(this.getCoordinates()))},getShell:function(){return this.isHole()?this._shell:this},add:function(t){this._deList.add(t)},getRing:function(){if(null!==this._ring)return this._ring;this.getCoordinates(),this._ringPts.length<3&&Y.out.println(this._ringPts);try{this._ring=this._factory.createLinearRing(this._ringPts)}catch(t){if(!(t instanceof D))throw t;Y.out.println(this._ringPts)}return this._ring},updateIncluded:function(){if(this.isHole())return null;for(var t=0;t<this._deList.size();t++){var e=this._deList.get(t).getSym().getRing().getShell();if(null!==e&&e.isIncludedSet())return this.setIncluded(!e.isIncluded()),null}},setShell:function(t){this._shell=t},setProcessed:function(t){this._isProcessed=t},interfaces_:function(){return[]},getClass:function(){return Cs}}),Cs.findDirEdgesInRing=function(t){for(var e=t,n=new P;n.add(e),e=e.getNext(),y.isTrue(null!==e,"found null DE in ring"),y.isTrue(e===t||!e.isInRing(),"found DE already in ring"),e!==t;);return n},Cs.addEdge=function(t,e,n){if(e)for(var i=0;i<t.length;i++)n.add(t[i],!1);else for(i=t.length-1;0<=i;i--)n.add(t[i],!1)},Cs.findEdgeRingContaining=function(t,e){for(var n=t.getRing(),i=n.getEnvelopeInternal(),r=n.getCoordinateN(0),s=null,o=null,a=e.iterator();a.hasNext();){var u=a.next(),l=u.getRing(),h=l.getEnvelopeInternal();if(!h.equals(i)&&h.contains(i)){r=nt.ptNotInList(n.getCoordinates(),l.getCoordinates());var c=!1;ke.isInRing(r,l.getCoordinates())&&(c=!0),c&&(null===s||o.contains(h))&&(o=(s=u).getRing().getEnvelopeInternal())}}return s},e(Ss.prototype,{compare:function(t,e){var n=e;return t.getRing().getEnvelope().compareTo(n.getRing().getEnvelope())},interfaces_:function(){return[u]},getClass:function(){return Ss}}),Cs.EnvelopeComparator=Ss,p(Ls,$r),e(Ls.prototype,{findEdgeRing:function(t){var e=new Cs(this._factory);return e.build(t),e},computeDepthParity:function(){if(0===arguments.length)for(;;){return null}},computeNextCWEdges:function(){for(var t=this.nodeIterator();t.hasNext();){var e=t.next();Ls.computeNextCWEdges(e)}},addEdge:function(t){if(t.isEmpty())return null;var e=nt.removeRepeatedPoints(t.getCoordinates());if(e.length<2)return null;var n=e[0],i=e[e.length-1],r=this.getNode(n),s=this.getNode(i),o=new Is(r,s,e[1],!0),a=new Is(s,r,e[e.length-2],!1),u=new Ns(t);u.setDirectedEdges(o,a),this.add(u)},deleteCutEdges:function(){this.computeNextCWEdges(),Ls.findLabeledEdgeRings(this._dirEdges);for(var t=new P,e=this._dirEdges.iterator();e.hasNext();){var n=e.next();if(!n.isMarked()){var i=n.getSym();if(n.getLabel()===i.getLabel()){n.setMarked(!0),i.setMarked(!0);var r=n.getEdge();t.add(r.getLine())}}}return t},getEdgeRings:function(){this.computeNextCWEdges(),Ls.label(this._dirEdges,-1);var t=Ls.findLabeledEdgeRings(this._dirEdges);this.convertMaximalToMinimalEdgeRings(t);for(var e=new P,n=this._dirEdges.iterator();n.hasNext();){var i=n.next();if(!i.isMarked()&&!i.isInRing()){var r=this.findEdgeRing(i);e.add(r)}}return e},getNode:function(t){var e=this.findNode(t);return null===e&&(e=new Zr(t),this.add(e)),e},convertMaximalToMinimalEdgeRings:function(t){for(var e=t.iterator();e.hasNext();){var n=e.next(),i=n.getLabel(),r=Ls.findIntersectionNodes(n,i);if(null!==r)for(var s=r.iterator();s.hasNext();){var o=s.next();Ls.computeNextCCWEdges(o,i)}}},deleteDangles:function(){for(var t=this.findNodesOfDegree(1),e=new ut,n=new en,i=t.iterator();i.hasNext();)n.push(i.next());for(;!n.isEmpty();){var r=n.pop();Ls.deleteAllEdges(r);for(i=r.getOutEdges().getEdges().iterator();i.hasNext();){var s=i.next();s.setMarked(!0);var o=s.getSym();null!==o&&o.setMarked(!0);var a=s.getEdge();e.add(a.getLine());var u=s.getToNode();1===Ls.getDegreeNonDeleted(u)&&n.push(u)}}return e},interfaces_:function(){return[]},getClass:function(){return Ls}}),Ls.findLabeledEdgeRings=function(t){for(var e=new P,n=1,i=t.iterator();i.hasNext();){var r=i.next();if(!r.isMarked()&&!(0<=r.getLabel())){e.add(r);var s=Cs.findDirEdgesInRing(r);Ls.label(s,n),n++}}return e},Ls.getDegreeNonDeleted=function(t){for(var e=0,n=t.getOutEdges().getEdges().iterator();n.hasNext();){n.next().isMarked()||e++}return e},Ls.deleteAllEdges=function(t){for(var e=t.getOutEdges().getEdges().iterator();e.hasNext();){var n=e.next();n.setMarked(!0);var i=n.getSym();null!==i&&i.setMarked(!0)}},Ls.label=function(t,e){for(var n=t.iterator();n.hasNext();){n.next().setLabel(e)}},Ls.computeNextCWEdges=function(t){for(var e=null,n=null,i=t.getOutEdges().getEdges().iterator();i.hasNext();){var r=i.next();if(!r.isMarked()){if(null===e&&(e=r),null!==n)n.getSym().setNext(r);n=r}}null!==n&&n.getSym().setNext(e)},Ls.computeNextCCWEdges=function(t,e){for(var n=null,i=null,r=t.getOutEdges().getEdges(),s=r.size()-1;0<=s;s--){var o=r.get(s),a=o.getSym(),u=null;o.getLabel()===e&&(u=o);var l=null;a.getLabel()===e&&(l=a),null===u&&null===l||(null!==l&&(i=l),null!==u&&(null!==i&&(i.setNext(u),i=null),null===n&&(n=u)))}null!==i&&(y.isTrue(null!==n),i.setNext(n))},Ls.getDegree=function(t,e){for(var n=0,i=t.getOutEdges().getEdges().iterator();i.hasNext();){i.next().getLabel()===e&&n++}return n},Ls.findIntersectionNodes=function(t,e){var n=t,i=null;do{var r=n.getFromNode();1<Ls.getDegree(r,e)&&(null===i&&(i=new P),i.add(r)),n=n.getNext(),y.isTrue(null!==n,"found null DE in ring"),y.isTrue(n===t||!n.isInRing(),"found DE already in ring")}while(n!==t);return i},e(ws.prototype,{getGeometry:function(){return null===this._geomFactory&&(this._geomFactory=new le),this.polygonize(),this._extractOnlyPolygonal?this._geomFactory.buildGeometry(this._polyList):this._geomFactory.createGeometryCollection(le.toGeometryArray(this._polyList))},getInvalidRingLines:function(){return this.polygonize(),this._invalidRingLines},findValidRings:function(t,e,n){for(var i=t.iterator();i.hasNext();){var r=i.next();r.isValid()?e.add(r):n.add(r.getLineString())}},polygonize:function(){if(null!==this._polyList)return null;if(this._polyList=new P,null===this._graph)return null;this._dangles=this._graph.deleteDangles(),this._cutEdges=this._graph.deleteCutEdges();var t=this._graph.getEdgeRings(),e=new P;this._invalidRingLines=new P,this._isCheckingRingsValid?this.findValidRings(t,e,this._invalidRingLines):e=t,this.findShellsAndHoles(e),ws.assignHolesToShells(this._holeList,this._shellList),De.sort(this._shellList,new Cs.EnvelopeComparator);var n=!0;this._extractOnlyPolygonal&&(ws.findDisjointShells(this._shellList),n=!1),this._polyList=ws.extractPolygons(this._shellList,n)},getDangles:function(){return this.polygonize(),this._dangles},getCutEdges:function(){return this.polygonize(),this._cutEdges},getPolygons:function(){return this.polygonize(),this._polyList},add:function(){if(L(arguments[0],N))for(var t=arguments[0].iterator();t.hasNext();){var e=t.next();this.add(e)}else if(arguments[0]instanceof Bt){var n=arguments[0];this._geomFactory=n.getFactory(),null===this._graph&&(this._graph=new Ls(this._geomFactory)),this._graph.addEdge(n)}else if(arguments[0]instanceof K){arguments[0].apply(this._lineStringAdder)}},setCheckRingsValid:function(t){this._isCheckingRingsValid=t},findShellsAndHoles:function(t){this._holeList=new P,this._shellList=new P;for(var e=t.iterator();e.hasNext();){var n=e.next();n.computeHole(),n.isHole()?this._holeList.add(n):this._shellList.add(n)}},interfaces_:function(){return[]},getClass:function(){return ws}}),ws.findOuterShells=function(t){for(var e=t.iterator();e.hasNext();){var n=e.next(),i=n.getOuterHole();null===i||i.isProcessed()||(n.setIncluded(!0),i.setProcessed(!0))}},ws.extractPolygons=function(t,e){for(var n=new P,i=t.iterator();i.hasNext();){var r=i.next();(e||r.isIncluded())&&n.add(r.getPolygon())}return n},ws.assignHolesToShells=function(t,e){for(var n=t.iterator();n.hasNext();){var i=n.next();ws.assignHoleToShell(i,e)}},ws.assignHoleToShell=function(t,e){var n=Cs.findEdgeRingContaining(t,e);null!==n&&n.addHole(t)},ws.findDisjointShells=function(t){ws.findOuterShells(t);var e=null;do{e=!1;for(var n=t.iterator();n.hasNext();){var i=n.next();i.isIncludedSet()||(i.updateIncluded(),i.isIncludedSet()||(e=!0))}}while(e)},e(Rs.prototype,{filter:function(t){t instanceof Bt&&this.p.add(t)},interfaces_:function(){return[j]},getClass:function(){return Rs}}),ws.LineStringAdder=Rs;var Ts=Object.freeze({Polygonizer:ws});function Ps(){}function Os(){if(this._edgeEnds=new P,1===arguments.length){var t=arguments[0];Os.call(this,null,t)}else if(2===arguments.length){var e=arguments[1];ri.call(this,e.getEdge(),e.getCoordinate(),e.getDirectedCoordinate(),new jn(e.getLabel())),this.insert(e)}}function bs(){Ir.apply(this)}function Ms(){var t=arguments[0],e=arguments[1];ni.call(this,t,e)}function Ds(){ai.apply(this)}function As(){this._li=new pe,this._ptLocator=new zn,this._arg=null,this._nodes=new ii(new Ds),this._im=null,this._isolatedEdges=new P,this._invalidPoint=null;var t=arguments[0];this._arg=t}function Fs(){this._rectEnv=null;var t=arguments[0];this._rectEnv=t.getEnvelopeInternal()}function Gs(){this._li=new pe,this._rectEnv=null,this._diagUp0=null,this._diagUp1=null,this._diagDown0=null,this._diagDown1=null;var t=arguments[0];this._rectEnv=t,this._diagUp0=new x(t.getMinX(),t.getMinY()),this._diagUp1=new x(t.getMaxX(),t.getMaxY()),this._diagDown0=new x(t.getMinX(),t.getMaxY()),this._diagDown1=new x(t.getMaxX(),t.getMinY())}function qs(){this._isDone=!1}function Bs(){this._rectangle=null,this._rectEnv=null;var t=arguments[0];this._rectangle=t,this._rectEnv=t.getEnvelopeInternal()}function zs(){qs.apply(this),this._rectEnv=null,this._intersects=!1;var t=arguments[0];this._rectEnv=t}function Vs(){qs.apply(this),this._rectSeq=null,this._rectEnv=null,this._containsPoint=!1;var t=arguments[0];this._rectSeq=t.getExteriorRing().getCoordinateSequence(),this._rectEnv=t.getEnvelopeInternal()}function Ys(){qs.apply(this),this._rectEnv=null,this._rectIntersector=null,this._hasIntersection=!1,this._p0=new x,this._p1=new x;var t=arguments[0];this._rectEnv=t.getEnvelopeInternal(),this._rectIntersector=new Gs(this._rectEnv)}function ks(){if(this._relate=null,2===arguments.length){var t=arguments[0],e=arguments[1];ys.call(this,t,e),this._relate=new As(this._arg)}else if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];ys.call(this,n,i,r),this._relate=new As(this._arg)}}e(Ps.prototype,{createEdgeEndForNext:function(t,e,n,i){var r=n.segmentIndex+1;if(r>=t.getNumPoints()&&null===i)return null;var s=t.getCoordinate(r);null!==i&&i.segmentIndex===n.segmentIndex&&(s=i.coord);var o=new ri(t,n.coord,s,new jn(t.getLabel()));e.add(o)},createEdgeEndForPrev:function(t,e,n,i){var r=n.segmentIndex;if(0===n.dist){if(0===r)return null;r--}var s=t.getCoordinate(r);null!==i&&i.segmentIndex>=r&&(s=i.coord);var o=new jn(t.getLabel());o.flip();var a=new ri(t,n.coord,s,o);e.add(a)},computeEdgeEnds:function(){if(1===arguments.length){for(var t=arguments[0],e=new P,n=t;n.hasNext();){var i=n.next();this.computeEdgeEnds(i,e)}return e}if(2===arguments.length){var r=arguments[0],s=arguments[1],o=r.getEdgeIntersectionList();o.addEndpoints();var a=o.iterator(),u=null,l=null;if(!a.hasNext())return null;for(var h=a.next();u=l,l=h,h=null,a.hasNext()&&(h=a.next()),null!==l&&(this.createEdgeEndForPrev(r,s,l,u),this.createEdgeEndForNext(r,s,l,h)),null!==l;);}},interfaces_:function(){return[]},getClass:function(){return Ps}}),p(Os,ri),e(Os.prototype,{insert:function(t){this._edgeEnds.add(t)},print:function(t){t.println("EdgeEndBundle--\x3e Label: "+this._label);for(var e=this.iterator();e.hasNext();){e.next().print(t),t.println()}},iterator:function(){return this._edgeEnds.iterator()},getEdgeEnds:function(){return this._edgeEnds},computeLabelOn:function(t,e){for(var n=0,i=!1,r=this.iterator();r.hasNext();){(s=r.next().getLabel().getLocation(t))===ve.BOUNDARY&&n++,s===ve.INTERIOR&&(i=!0)}var s=ve.NONE;i&&(s=ve.INTERIOR),0<n&&(s=li.determineBoundary(e,n)),this._label.setLocation(t,s)},computeLabelSide:function(t,e){for(var n=this.iterator();n.hasNext();){var i=n.next();if(i.getLabel().isArea()){var r=i.getLabel().getLocation(t,e);if(r===ve.INTERIOR)return this._label.setLocation(t,e,ve.INTERIOR),null;r===ve.EXTERIOR&&this._label.setLocation(t,e,ve.EXTERIOR)}}},getLabel:function(){return this._label},computeLabelSides:function(t){this.computeLabelSide(t,Vn.LEFT),this.computeLabelSide(t,Vn.RIGHT)},updateIM:function(t){ei.updateIM(this._label,t)},computeLabel:function(t){for(var e=!1,n=this.iterator();n.hasNext();){n.next().getLabel().isArea()&&(e=!0)}this._label=e?new jn(ve.NONE,ve.NONE,ve.NONE):new jn(ve.NONE);for(var i=0;i<2;i++)this.computeLabelOn(i,t),e&&this.computeLabelSides(i)},interfaces_:function(){return[]},getClass:function(){return Os}}),p(bs,Ir),e(bs.prototype,{updateIM:function(t){for(var e=this.iterator();e.hasNext();){e.next().updateIM(t)}},insert:function(t){var e=this._edgeMap.get(t);null===e?(e=new Os(t),this.insertEdgeEnd(t,e)):e.insert(t)},interfaces_:function(){return[]},getClass:function(){return bs}}),p(Ms,ni),e(Ms.prototype,{updateIMFromEdges:function(t){this._edges.updateIM(t)},computeIM:function(t){t.setAtLeastIfValid(this._label.getLocation(0),this._label.getLocation(1),0)},interfaces_:function(){return[]},getClass:function(){return Ms}}),p(Ds,ai),e(Ds.prototype,{createNode:function(t){return new Ms(t,new bs)},interfaces_:function(){return[]},getClass:function(){return Ds}}),e(As.prototype,{insertEdgeEnds:function(t){for(var e=t.iterator();e.hasNext();){var n=e.next();this._nodes.add(n)}},computeProperIntersectionIM:function(t,e){var n=this._arg[0].getGeometry().getDimension(),i=this._arg[1].getGeometry().getDimension(),r=t.hasProperIntersection(),s=t.hasProperInteriorIntersection();2===n&&2===i?r&&e.setAtLeast("212101212"):2===n&&1===i?(r&&e.setAtLeast("FFF0FFFF2"),s&&e.setAtLeast("1FFFFF1FF")):1===n&&2===i?(r&&e.setAtLeast("F0FFFFFF2"),s&&e.setAtLeast("1F1FFFFFF")):1===n&&1===i&&s&&e.setAtLeast("0FFFFFFFF")},labelIsolatedEdges:function(t,e){for(var n=this._arg[t].getEdgeIterator();n.hasNext();){var i=n.next();i.isIsolated()&&(this.labelIsolatedEdge(i,e,this._arg[e].getGeometry()),this._isolatedEdges.add(i))}},labelIsolatedEdge:function(t,e,n){if(0<n.getDimension()){var i=this._ptLocator.locate(t.getCoordinate(),n);t.getLabel().setAllLocations(e,i)}else t.getLabel().setAllLocations(e,ve.EXTERIOR)},computeIM:function(){var t=new ye;if(t.set(ve.EXTERIOR,ve.EXTERIOR,2),!this._arg[0].getGeometry().getEnvelopeInternal().intersects(this._arg[1].getGeometry().getEnvelopeInternal()))return this.computeDisjointIM(t),t;this._arg[0].computeSelfNodes(this._li,!1),this._arg[1].computeSelfNodes(this._li,!1);var e=this._arg[0].computeEdgeIntersections(this._arg[1],this._li,!1);this.computeIntersectionNodes(0),this.computeIntersectionNodes(1),this.copyNodesAndLabels(0),this.copyNodesAndLabels(1),this.labelIsolatedNodes(),this.computeProperIntersectionIM(e,t);var n=new Ps,i=n.computeEdgeEnds(this._arg[0].getEdgeIterator());this.insertEdgeEnds(i);var r=n.computeEdgeEnds(this._arg[1].getEdgeIterator());return this.insertEdgeEnds(r),this.labelNodeEdges(),this.labelIsolatedEdges(0,1),this.labelIsolatedEdges(1,0),this.updateIM(t),t},labelNodeEdges:function(){for(var t=this._nodes.iterator();t.hasNext();){t.next().getEdges().computeLabelling(this._arg)}},copyNodesAndLabels:function(t){for(var e=this._arg[t].getNodeIterator();e.hasNext();){var n=e.next();this._nodes.addNode(n.getCoordinate()).setLabel(t,n.getLabel().getLocation(t))}},labelIntersectionNodes:function(t){for(var e=this._arg[t].getEdgeIterator();e.hasNext();)for(var n=e.next(),i=n.getLabel().getLocation(t),r=n.getEdgeIntersectionList().iterator();r.hasNext();){var s=r.next(),o=this._nodes.find(s.coord);o.getLabel().isNull(t)&&(i===ve.BOUNDARY?o.setLabelBoundary(t):o.setLabel(t,ve.INTERIOR))}},labelIsolatedNode:function(t,e){var n=this._ptLocator.locate(t.getCoordinate(),this._arg[e].getGeometry());t.getLabel().setAllLocations(e,n)},computeIntersectionNodes:function(t){for(var e=this._arg[t].getEdgeIterator();e.hasNext();)for(var n=e.next(),i=n.getLabel().getLocation(t),r=n.getEdgeIntersectionList().iterator();r.hasNext();){var s=r.next(),o=this._nodes.addNode(s.coord);i===ve.BOUNDARY?o.setLabelBoundary(t):o.getLabel().isNull(t)&&o.setLabel(t,ve.INTERIOR)}},labelIsolatedNodes:function(){for(var t=this._nodes.iterator();t.hasNext();){var e=t.next(),n=e.getLabel();y.isTrue(0<n.getGeometryCount(),"node with empty label found"),e.isIsolated()&&(n.isNull(0)?this.labelIsolatedNode(e,0):this.labelIsolatedNode(e,1))}},updateIM:function(t){for(var e=this._isolatedEdges.iterator();e.hasNext();){e.next().updateIM(t)}for(var n=this._nodes.iterator();n.hasNext();){var i=n.next();i.updateIM(t),i.updateIMFromEdges(t)}},computeDisjointIM:function(t){var e=this._arg[0].getGeometry();e.isEmpty()||(t.set(ve.INTERIOR,ve.EXTERIOR,e.getDimension()),t.set(ve.BOUNDARY,ve.EXTERIOR,e.getBoundaryDimension()));var n=this._arg[1].getGeometry();n.isEmpty()||(t.set(ve.EXTERIOR,ve.INTERIOR,n.getDimension()),t.set(ve.EXTERIOR,ve.BOUNDARY,n.getBoundaryDimension()))},interfaces_:function(){return[]},getClass:function(){return As}}),e(Fs.prototype,{isContainedInBoundary:function(t){if(t instanceof Ut)return!1;if(t instanceof Vt)return this.isPointContainedInBoundary(t);if(t instanceof Bt)return this.isLineStringContainedInBoundary(t);for(var e=0;e<t.getNumGeometries();e++){var n=t.getGeometryN(e);if(!this.isContainedInBoundary(n))return!1}return!0},isLineSegmentContainedInBoundary:function(t,e){if(t.equals(e))return this.isPointContainedInBoundary(t);if(t.x===e.x){if(t.x===this._rectEnv.getMinX()||t.x===this._rectEnv.getMaxX())return!0}else if(t.y===e.y&&(t.y===this._rectEnv.getMinY()||t.y===this._rectEnv.getMaxY()))return!0;return!1},isLineStringContainedInBoundary:function(t){for(var e=t.getCoordinateSequence(),n=new x,i=new x,r=0;r<e.size()-1;r++)if(e.getCoordinate(r,n),e.getCoordinate(r+1,i),!this.isLineSegmentContainedInBoundary(n,i))return!1;return!0},isPointContainedInBoundary:function(){if(arguments[0]instanceof Vt){var t=arguments[0];return this.isPointContainedInBoundary(t.getCoordinate())}if(arguments[0]instanceof x){var e=arguments[0];return e.x===this._rectEnv.getMinX()||e.x===this._rectEnv.getMaxX()||e.y===this._rectEnv.getMinY()||e.y===this._rectEnv.getMaxY()}},contains:function(t){return!!this._rectEnv.contains(t.getEnvelopeInternal())&&!this.isContainedInBoundary(t)},interfaces_:function(){return[]},getClass:function(){return Fs}}),Fs.contains=function(t,e){return new Fs(t).contains(e)},e(Gs.prototype,{intersects:function(t,e){var n=new M(t,e);if(!this._rectEnv.intersects(n))return!1;if(this._rectEnv.intersects(t))return!0;if(this._rectEnv.intersects(e))return!0;if(0<t.compareTo(e)){var i=t;t=e,e=i}var r=!1;return e.y>t.y&&(r=!0),r?this._li.computeIntersection(t,e,this._diagDown0,this._diagDown1):this._li.computeIntersection(t,e,this._diagUp0,this._diagUp1),!!this._li.hasIntersection()},interfaces_:function(){return[]},getClass:function(){return Gs}}),e(qs.prototype,{applyTo:function(t){for(var e=0;e<t.getNumGeometries()&&!this._isDone;e++){var n=t.getGeometryN(e);if(n instanceof Lt)this.applyTo(n);else if(this.visit(n),this.isDone())return this._isDone=!0,null}},interfaces_:function(){return[]},getClass:function(){return qs}}),e(Bs.prototype,{intersects:function(t){if(!this._rectEnv.intersects(t.getEnvelopeInternal()))return!1;var e=new zs(this._rectEnv);if(e.applyTo(t),e.intersects())return!0;var n=new Vs(this._rectangle);if(n.applyTo(t),n.containsPoint())return!0;var i=new Ys(this._rectangle);return i.applyTo(t),!!i.intersects()},interfaces_:function(){return[]},getClass:function(){return Bs}}),Bs.intersects=function(t,e){return new Bs(t).intersects(e)},p(zs,qs),e(zs.prototype,{isDone:function(){return!0===this._intersects},visit:function(t){var e=t.getEnvelopeInternal();return this._rectEnv.intersects(e)?this._rectEnv.contains(e)?(this._intersects=!0,null):e.getMinX()>=this._rectEnv.getMinX()&&e.getMaxX()<=this._rectEnv.getMaxX()?(this._intersects=!0,null):e.getMinY()>=this._rectEnv.getMinY()&&e.getMaxY()<=this._rectEnv.getMaxY()?(this._intersects=!0,null):void 0:null},intersects:function(){return this._intersects},interfaces_:function(){return[]},getClass:function(){return zs}}),p(Vs,qs),e(Vs.prototype,{isDone:function(){return!0===this._containsPoint},visit:function(t){if(!(t instanceof Ut))return null;var e=t.getEnvelopeInternal();if(!this._rectEnv.intersects(e))return null;for(var n=new x,i=0;i<4;i++)if(this._rectSeq.getCoordinate(i,n),e.contains(n)&&Xe.containsPointInPolygon(n,t))return this._containsPoint=!0,null},containsPoint:function(){return this._containsPoint},interfaces_:function(){return[]},getClass:function(){return Vs}}),p(Ys,qs),e(Ys.prototype,{intersects:function(){return this._hasIntersection},isDone:function(){return!0===this._hasIntersection},visit:function(t){var e=t.getEnvelopeInternal();if(!this._rectEnv.intersects(e))return null;var n=Ge.getLines(t);this.checkIntersectionWithLineStrings(n)},checkIntersectionWithLineStrings:function(t){for(var e=t.iterator();e.hasNext();){var n=e.next();if(this.checkIntersectionWithSegments(n),this._hasIntersection)return null}},checkIntersectionWithSegments:function(t){for(var e=t.getCoordinateSequence(),n=1;n<e.size();n++)if(e.getCoordinate(n-1,this._p0),e.getCoordinate(n,this._p1),this._rectIntersector.intersects(this._p0,this._p1))return this._hasIntersection=!0,null},interfaces_:function(){return[]},getClass:function(){return Ys}}),p(ks,ys),e(ks.prototype,{getIntersectionMatrix:function(){return this._relate.computeIM()},interfaces_:function(){return[]},getClass:function(){return ks}}),ks.covers=function(t,e){return!(2===e.getDimension()&&t.getDimension()<2)&&(!(1===e.getDimension()&&t.getDimension()<1&&0<e.getLength())&&(!!t.getEnvelopeInternal().covers(e.getEnvelopeInternal())&&(!!t.isRectangle()||new ks(t,e).getIntersectionMatrix().isCovers())))},ks.intersects=function(t,e){if(!t.getEnvelopeInternal().intersects(e.getEnvelopeInternal()))return!1;if(t.isRectangle())return Bs.intersects(t,e);if(e.isRectangle())return Bs.intersects(e,t);if(t.isGeometryCollection()||e.isGeometryCollection()){for(var n=0;n<t.getNumGeometries();n++)for(var i=0;i<e.getNumGeometries();i++)if(t.getGeometryN(n).intersects(e.getGeometryN(i)))return!0;return!1}return new ks(t,e).getIntersectionMatrix().isIntersects()},ks.touches=function(t,e){return!!t.getEnvelopeInternal().intersects(e.getEnvelopeInternal())&&new ks(t,e).getIntersectionMatrix().isTouches(t.getDimension(),e.getDimension())},ks.relate=function(){return 2===arguments.length?new ks(arguments[0],arguments[1]).getIntersectionMatrix():3===arguments.length?new ks(arguments[0],arguments[1],arguments[2]).getIntersectionMatrix():void 0},ks.overlaps=function(t,e){return!!t.getEnvelopeInternal().intersects(e.getEnvelopeInternal())&&new ks(t,e).getIntersectionMatrix().isOverlaps(t.getDimension(),e.getDimension())},ks.crosses=function(t,e){return!!t.getEnvelopeInternal().intersects(e.getEnvelopeInternal())&&new ks(t,e).getIntersectionMatrix().isCrosses(t.getDimension(),e.getDimension())},ks.contains=function(t,e){return!(2===e.getDimension()&&t.getDimension()<2)&&(!(1===e.getDimension()&&t.getDimension()<1&&0<e.getLength())&&(!!t.getEnvelopeInternal().contains(e.getEnvelopeInternal())&&(t.isRectangle()?Fs.contains(t,e):new ks(t,e).getIntersectionMatrix().isContains())))};var Us=Object.freeze({RelateOp:ks});function Xs(){this._geomFactory=null,this._skipEmpty=!1,this._inputGeoms=null;var t=arguments[0];this._geomFactory=Xs.extractFactory(t),this._inputGeoms=t}function Hs(){this._pointGeom=null,this._otherGeom=null,this._geomFact=null;var t=arguments[0],e=arguments[1];this._pointGeom=t,this._otherGeom=e,this._geomFact=e.getFactory()}function Ws(){this._geometryType=null,this._comps=null;var t=arguments[0],e=arguments[1];this._geometryType=t,this._comps=e}function js(){this._inputPolys=null,this._geomFactory=null;var t=arguments[0];this._inputPolys=t,null===this._inputPolys&&(this._inputPolys=new P)}function Ks(){if(this._polygons=new P,this._lines=new P,this._points=new P,this._geomFact=null,1===arguments.length){if(L(arguments[0],N)){var t=arguments[0];this.extract(t)}else if(arguments[0]instanceof K){var e=arguments[0];this.extract(e)}}else if(2===arguments.length){var n=arguments[0],i=arguments[1];this._geomFact=i,this.extract(n)}}e(Xs.prototype,{extractElements:function(t,e){if(null===t)return null;for(var n=0;n<t.getNumGeometries();n++){var i=t.getGeometryN(n);this._skipEmpty&&i.isEmpty()||e.add(i)}},combine:function(){for(var t=new P,e=this._inputGeoms.iterator();e.hasNext();){var n=e.next();this.extractElements(n,t)}return 0===t.size()?null!==this._geomFactory?this._geomFactory.createGeometryCollection():null:this._geomFactory.buildGeometry(t)},interfaces_:function(){return[]},getClass:function(){return Xs}}),Xs.combine=function(){if(1===arguments.length)return new Xs(arguments[0]).combine();if(2===arguments.length){var t=arguments[0],e=arguments[1];return new Xs(Xs.createList(t,e)).combine()}if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];return new Xs(Xs.createList(n,i,r)).combine()}},Xs.extractFactory=function(t){return t.isEmpty()?null:t.iterator().next().getFactory()},Xs.createList=function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];return(n=new P).add(t),n.add(e),n}if(3===arguments.length){var n,i=arguments[0],r=arguments[1],s=arguments[2];return(n=new P).add(i),n.add(r),n.add(s),n}},e(Hs.prototype,{union:function(){for(var t=new zn,e=new yt,n=0;n<this._pointGeom.getNumGeometries();n++){var i=this._pointGeom.getGeometryN(n).getCoordinate();t.locate(i,this._otherGeom)===ve.EXTERIOR&&e.add(i)}if(0===e.size())return this._otherGeom;var r=null,s=nt.toCoordinateArray(e);return r=1===s.length?this._geomFact.createPoint(s[0]):this._geomFact.createMultiPointFromCoords(s),Xs.combine(r,this._otherGeom)},interfaces_:function(){return[]},getClass:function(){return Hs}}),Hs.union=function(t,e){return new Hs(t,e).union()},e(Ws.prototype,{filter:function(t){(null===this._geometryType||Ws.isOfType(t,this._geometryType))&&this._comps.add(t)},interfaces_:function(){return[Ct]},getClass:function(){return Ws}}),Ws.isOfType=function(t,e){return t.getGeometryType()===e||e===K.TYPENAME_LINESTRING&&t.getGeometryType()===K.TYPENAME_LINEARRING},Ws.extract=function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];return Ws.extract(t,e,new P)}if(3===arguments.length){if(L(arguments[2],w)&&arguments[0]instanceof K&&"string"==typeof arguments[1]){var n=arguments[0],i=arguments[1],r=arguments[2];return n.getGeometryType()===i?r.add(n):n instanceof Lt&&n.apply(new Ws(i,r)),r}if(L(arguments[2],w)&&arguments[0]instanceof K&&arguments[1]instanceof Class){var s=arguments[0],o=arguments[1],a=arguments[2];return Ws.extract(s,Ws.toGeometryType(o),a)}}},e(js.prototype,{reduceToGeometries:function(t){for(var e=new P,n=t.iterator();n.hasNext();){var i=n.next(),r=null;L(i,w)?r=this.unionTree(i):i instanceof K&&(r=i),e.add(r)}return e},extractByEnvelope:function(t,e,n){for(var i=new P,r=0;r<e.getNumGeometries();r++){var s=e.getGeometryN(r);s.getEnvelopeInternal().intersects(t)?i.add(s):n.add(s)}return this._geomFactory.buildGeometry(i)},unionOptimized:function(t,e){var n=t.getEnvelopeInternal(),i=e.getEnvelopeInternal();if(!n.intersects(i))return Xs.combine(t,e);if(t.getNumGeometries()<=1&&e.getNumGeometries()<=1)return this.unionActual(t,e);var r=n.intersection(i);return this.unionUsingEnvelopeIntersection(t,e,r)},union:function(){if(null===this._inputPolys)throw new IllegalStateException("union() method cannot be called twice");if(this._inputPolys.isEmpty())return null;this._geomFactory=this._inputPolys.iterator().next().getFactory();for(var t=new Pi(js.STRTREE_NODE_CAPACITY),e=this._inputPolys.iterator();e.hasNext();){var n=e.next();t.insert(n.getEnvelopeInternal(),n)}this._inputPolys=null;var i=t.itemsTree();return this.unionTree(i)},binaryUnion:function(){if(1===arguments.length){var t=arguments[0];return this.binaryUnion(t,0,t.size())}if(3===arguments.length){var e=arguments[0],n=arguments[1],i=arguments[2];if(i-n<=1){var r=js.getGeometry(e,n);return this.unionSafe(r,null)}if(i-n==2)return this.unionSafe(js.getGeometry(e,n),js.getGeometry(e,n+1));var s=Math.trunc((i+n)/2),o=(r=this.binaryUnion(e,n,s),this.binaryUnion(e,s,i));return this.unionSafe(r,o)}},repeatedUnion:function(t){for(var e=null,n=t.iterator();n.hasNext();){var i=n.next();e=null===e?i.copy():e.union(i)}return e},unionSafe:function(t,e){return null===t&&null===e?null:null===t?e.copy():null===e?t.copy():this.unionOptimized(t,e)},unionActual:function(t,e){return js.restrictToPolygons(t.union(e))},unionTree:function(t){var e=this.reduceToGeometries(t);return this.binaryUnion(e)},unionUsingEnvelopeIntersection:function(t,e,n){var i=new P,r=this.extractByEnvelope(n,t,i),s=this.extractByEnvelope(n,e,i),o=this.unionActual(r,s);return i.add(o),Xs.combine(i)},bufferUnion:function(){if(1===arguments.length){var t=arguments[0];return t.get(0).getFactory().buildGeometry(t).buffer(0)}if(2===arguments.length){var e=arguments[0],n=arguments[1];return e.getFactory().createGeometryCollection([e,n]).buffer(0)}},interfaces_:function(){return[]},getClass:function(){return js}}),js.restrictToPolygons=function(t){if(L(t,kt))return t;var e=qr.getPolygons(t);return 1===e.size()?e.get(0):t.getFactory().createMultiPolygon(le.toPolygonArray(e))},js.getGeometry=function(t,e){return e>=t.size()?null:t.get(e)},js.union=function(t){return new js(t).union()},js.STRTREE_NODE_CAPACITY=4,e(Ks.prototype,{unionNoOpt:function(t){var e=this._geomFact.createPoint();return vs.overlayOp(t,e,xs.UNION)},unionWithNull:function(t,e){return null===t&&null===e?null:null===e?t:null===t?e:t.union(e)},extract:function(){if(L(arguments[0],N))for(var t=arguments[0].iterator();t.hasNext();){var e=t.next();this.extract(e)}else if(arguments[0]instanceof K){var n=arguments[0];null===this._geomFact&&(this._geomFact=n.getFactory()),Ws.extract(n,K.TYPENAME_POLYGON,this._polygons),Ws.extract(n,K.TYPENAME_LINESTRING,this._lines),Ws.extract(n,K.TYPENAME_POINT,this._points)}},union:function(){if(null===this._geomFact)return null;var t=null;if(0<this._points.size()){var e=this._geomFact.buildGeometry(this._points);t=this.unionNoOpt(e)}var n=null;if(0<this._lines.size()){var i=this._geomFact.buildGeometry(this._lines);n=this.unionNoOpt(i)}var r=null;0<this._polygons.size()&&(r=js.union(this._polygons));var s=this.unionWithNull(n,r),o=null;return o=null===t?s:null===s?t:Hs.union(t,s),null===o?this._geomFact.createGeometryCollection():o},interfaces_:function(){return[]},getClass:function(){return Ks}}),Ks.union=function(){if(1===arguments.length){if(L(arguments[0],N))return new Ks(arguments[0]).union();if(arguments[0]instanceof K)return new Ks(arguments[0]).union()}else if(2===arguments.length){return new Ks(arguments[0],arguments[1]).union()}};var Zs=Object.freeze({UnaryUnionOp:Ks});function Qs(){this._geometryFactory=new le,this._geomGraph=null,this._disconnectedRingcoord=null;var t=arguments[0];this._geomGraph=t}function Js(){this._nodes=new ii(new Ds)}function $s(){this._li=new pe,this._geomGraph=null,this._nodeGraph=new Js,this._invalidPoint=null;var t=arguments[0];this._geomGraph=t}function to(){this._graph=null,this._rings=new P,this._totalEnv=new M,this._index=null,this._nestedPt=null;var t=arguments[0];this._graph=t}function eo(){if(this._errorType=null,this._pt=null,1===arguments.length){var t=arguments[0];eo.call(this,t,null)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this._errorType=e,null!==n&&(this._pt=n.copy())}}function no(){this._parentGeometry=null,this._isSelfTouchingRingFormingHoleValid=!1,this._validErr=null;var t=arguments[0];this._parentGeometry=t}e(Qs.prototype,{visitInteriorRing:function(t,e){var n=t.getCoordinates(),i=n[0],r=Qs.findDifferentPoint(n,i),s=e.findEdgeInSameDirection(i,r),o=e.findEdgeEnd(s),a=null;o.getLabel().getLocation(0,Vn.RIGHT)===ve.INTERIOR?a=o:o.getSym().getLabel().getLocation(0,Vn.RIGHT)===ve.INTERIOR&&(a=o.getSym()),y.isTrue(null!==a,"unable to find dirEdge with Interior on RHS"),this.visitLinkedDirectedEdges(a)},visitShellInteriors:function(t,e){if(t instanceof Ut){var n=t;this.visitInteriorRing(n.getExteriorRing(),e)}if(t instanceof Wt)for(var i=t,r=0;r<i.getNumGeometries();r++){n=i.getGeometryN(r);this.visitInteriorRing(n.getExteriorRing(),e)}},getCoordinate:function(){return this._disconnectedRingcoord},setInteriorEdgesInResult:function(t){for(var e=t.getEdgeEnds().iterator();e.hasNext();){var n=e.next();n.getLabel().getLocation(0,Vn.RIGHT)===ve.INTERIOR&&n.setInResult(!0)}},visitLinkedDirectedEdges:function(t){for(var e=t,n=t;y.isTrue(null!==n,"found null Directed Edge"),n.setVisited(!0),(n=n.getNext())!==e;);},buildEdgeRings:function(t){for(var e=new P,n=t.iterator();n.hasNext();){var i=n.next();if(i.isInResult()&&null===i.getEdgeRing()){var r=new gr(i,this._geometryFactory);r.linkDirectedEdgesForMinimalEdgeRings();var s=r.buildMinimalRings();e.addAll(s)}}return e},hasUnvisitedShellEdge:function(t){for(var e=0;e<t.size();e++){var n=t.get(e);if(!n.isHole()){var i=n.getEdges(),r=i.get(0);if(r.getLabel().getLocation(0,Vn.RIGHT)===ve.INTERIOR)for(var s=0;s<i.size();s++)if(!(r=i.get(s)).isVisited())return this._disconnectedRingcoord=r.getCoordinate(),!0}}return!1},isInteriorsConnected:function(){var t=new P;this._geomGraph.computeSplitEdges(t);var e=new ui(new Cr);e.addEdges(t),this.setInteriorEdgesInResult(e),e.linkResultDirectedEdges();var n=this.buildEdgeRings(e.getEdgeEnds());return this.visitShellInteriors(this._geomGraph.getGeometry(),e),!this.hasUnvisitedShellEdge(n)},interfaces_:function(){return[]},getClass:function(){return Qs}}),Qs.findDifferentPoint=function(t,e){for(var n=0;n<t.length;n++)if(!t[n].equals(e))return t[n];return null},e(Js.prototype,{insertEdgeEnds:function(t){for(var e=t.iterator();e.hasNext();){var n=e.next();this._nodes.add(n)}},getNodeIterator:function(){return this._nodes.iterator()},copyNodesAndLabels:function(t,e){for(var n=t.getNodeIterator();n.hasNext();){var i=n.next();this._nodes.addNode(i.getCoordinate()).setLabel(e,i.getLabel().getLocation(e))}},build:function(t){this.computeIntersectionNodes(t,0),this.copyNodesAndLabels(t,0);var e=(new Ps).computeEdgeEnds(t.getEdgeIterator());this.insertEdgeEnds(e)},computeIntersectionNodes:function(t,e){for(var n=t.getEdgeIterator();n.hasNext();)for(var i=n.next(),r=i.getLabel().getLocation(e),s=i.getEdgeIntersectionList().iterator();s.hasNext();){var o=s.next(),a=this._nodes.addNode(o.coord);r===ve.BOUNDARY?a.setLabelBoundary(e):a.getLabel().isNull(e)&&a.setLabel(e,ve.INTERIOR)}},interfaces_:function(){return[]},getClass:function(){return Js}}),e($s.prototype,{isNodeEdgeAreaLabelsConsistent:function(){for(var t=this._nodeGraph.getNodeIterator();t.hasNext();){var e=t.next();if(!e.getEdges().isAreaLabelsConsistent(this._geomGraph))return this._invalidPoint=e.getCoordinate().copy(),!1}return!0},getInvalidPoint:function(){return this._invalidPoint},hasDuplicateRings:function(){for(var t=this._nodeGraph.getNodeIterator();t.hasNext();)for(var e=t.next().getEdges().iterator();e.hasNext();){var n=e.next();if(1<n.getEdgeEnds().size())return this._invalidPoint=n.getEdge().getCoordinate(0),!0}return!1},isNodeConsistentArea:function(){var t=this._geomGraph.computeSelfNodes(this._li,!0,!0);return t.hasProperIntersection()?(this._invalidPoint=t.getProperIntersectionPoint(),!1):(this._nodeGraph.build(this._geomGraph),this.isNodeEdgeAreaLabelsConsistent())},interfaces_:function(){return[]},getClass:function(){return $s}}),e(to.prototype,{buildIndex:function(){this._index=new Pi;for(var t=0;t<this._rings.size();t++){var e=this._rings.get(t),n=e.getEnvelopeInternal();this._index.insert(n,e)}},getNestedPoint:function(){return this._nestedPt},isNonNested:function(){this.buildIndex();for(var t=0;t<this._rings.size();t++)for(var e=this._rings.get(t),n=e.getCoordinates(),i=this._index.query(e.getEnvelopeInternal()),r=0;r<i.size();r++){var s=i.get(r),o=s.getCoordinates();if(e!==s&&e.getEnvelopeInternal().intersects(s.getEnvelopeInternal())){var a=no.findPtNotNode(n,s,this._graph);if(null!==a)if(ke.isInRing(a,o))return this._nestedPt=a,!1}}return!0},add:function(t){this._rings.add(t),this._totalEnv.expandToInclude(t.getEnvelopeInternal())},interfaces_:function(){return[]},getClass:function(){return to}}),e(eo.prototype,{getErrorType:function(){return this._errorType},getMessage:function(){return eo.errMsg[this._errorType]},getCoordinate:function(){return this._pt},toString:function(){var t="";return null!==this._pt&&(t=" at or near point "+this._pt),this.getMessage()+t},interfaces_:function(){return[]},getClass:function(){return eo}}),eo.ERROR=0,eo.REPEATED_POINT=1,eo.HOLE_OUTSIDE_SHELL=2,eo.NESTED_HOLES=3,eo.DISCONNECTED_INTERIOR=4,eo.SELF_INTERSECTION=5,eo.RING_SELF_INTERSECTION=6,eo.NESTED_SHELLS=7,eo.DUPLICATE_RINGS=8,eo.TOO_FEW_POINTS=9,eo.INVALID_COORDINATE=10,eo.RING_NOT_CLOSED=11,eo.errMsg=["Topology Validation Error","Repeated Point","Hole lies outside shell","Holes are nested","Interior is disconnected","Self-intersection","Ring Self-intersection","Nested shells","Duplicate Rings","Too few distinct points in geometry component","Invalid Coordinate","Ring is not closed"],e(no.prototype,{checkInvalidCoordinates:function(){if(arguments[0]instanceof Array){for(var t=arguments[0],e=0;e<t.length;e++)if(!no.isValid(t[e]))return this._validErr=new eo(eo.INVALID_COORDINATE,t[e]),null}else if(arguments[0]instanceof Ut){var n=arguments[0];if(this.checkInvalidCoordinates(n.getExteriorRing().getCoordinates()),null!==this._validErr)return null;for(e=0;e<n.getNumInteriorRing();e++)if(this.checkInvalidCoordinates(n.getInteriorRingN(e).getCoordinates()),null!==this._validErr)return null}},checkHolesNotNested:function(t,e){for(var n=new to(e),i=0;i<t.getNumInteriorRing();i++){var r=t.getInteriorRingN(i);n.add(r)}n.isNonNested()||(this._validErr=new eo(eo.NESTED_HOLES,n.getNestedPoint()))},checkConsistentArea:function(t){var e=new $s(t);if(!e.isNodeConsistentArea())return this._validErr=new eo(eo.SELF_INTERSECTION,e.getInvalidPoint()),null;e.hasDuplicateRings()&&(this._validErr=new eo(eo.DUPLICATE_RINGS,e.getInvalidPoint()))},isValid:function(){return this.checkValid(this._parentGeometry),null===this._validErr},checkShellInsideHole:function(t,e,n){var i=t.getCoordinates(),r=e.getCoordinates(),s=no.findPtNotNode(i,e,n);if(null!==s&&!ke.isInRing(s,r))return s;var o=no.findPtNotNode(r,t,n);return null!==o?ke.isInRing(o,i)?o:null:(y.shouldNeverReachHere("points in shell and hole appear to be equal"),null)},checkNoSelfIntersectingRings:function(t){for(var e=t.getEdgeIterator();e.hasNext();){var n=e.next();if(this.checkNoSelfIntersectingRing(n.getEdgeIntersectionList()),null!==this._validErr)return null}},checkConnectedInteriors:function(t){var e=new Qs(t);e.isInteriorsConnected()||(this._validErr=new eo(eo.DISCONNECTED_INTERIOR,e.getCoordinate()))},checkNoSelfIntersectingRing:function(t){for(var e=new yt,n=!0,i=t.iterator();i.hasNext();){var r=i.next();if(n)n=!1;else{if(e.contains(r.coord))return this._validErr=new eo(eo.RING_SELF_INTERSECTION,r.coord),null;e.add(r.coord)}}},checkHolesInShell:function(t,e){for(var n=t.getExteriorRing(),i=new ze(n),r=0;r<t.getNumInteriorRing();r++){var s=t.getInteriorRingN(r),o=no.findPtNotNode(s.getCoordinates(),n,e);if(null===o)return null;if(ve.EXTERIOR===i.locate(o))return this._validErr=new eo(eo.HOLE_OUTSIDE_SHELL,o),null}},checkTooFewPoints:function(t){if(t.hasTooFewPoints())return this._validErr=new eo(eo.TOO_FEW_POINTS,t.getInvalidPoint()),null},getValidationError:function(){return this.checkValid(this._parentGeometry),this._validErr},checkValid:function(){if(arguments[0]instanceof Vt){var t=arguments[0];this.checkInvalidCoordinates(t.getCoordinates())}else if(arguments[0]instanceof Xt){var e=arguments[0];this.checkInvalidCoordinates(e.getCoordinates())}else if(arguments[0]instanceof Ht){var n=arguments[0];if(this.checkInvalidCoordinates(n.getCoordinates()),null!==this._validErr)return null;if(this.checkClosedRing(n),null!==this._validErr)return null;var i=new li(0,n);if(this.checkTooFewPoints(i),null!==this._validErr)return null;var r=new pe;i.computeSelfNodes(r,!0,!0),this.checkNoSelfIntersectingRings(i)}else if(arguments[0]instanceof Bt){var s=arguments[0];if(this.checkInvalidCoordinates(s.getCoordinates()),null!==this._validErr)return null;i=new li(0,s);this.checkTooFewPoints(i)}else if(arguments[0]instanceof Ut){var o=arguments[0];if(this.checkInvalidCoordinates(o),null!==this._validErr)return null;if(this.checkClosedRings(o),null!==this._validErr)return null;i=new li(0,o);if(this.checkTooFewPoints(i),null!==this._validErr)return null;if(this.checkConsistentArea(i),null!==this._validErr)return null;if(!this._isSelfTouchingRingFormingHoleValid&&(this.checkNoSelfIntersectingRings(i),null!==this._validErr))return null;if(this.checkHolesInShell(o,i),null!==this._validErr)return null;if(this.checkHolesNotNested(o,i),null!==this._validErr)return null;this.checkConnectedInteriors(i)}else if(arguments[0]instanceof Wt){for(var a=arguments[0],u=0;u<a.getNumGeometries();u++){var l=a.getGeometryN(u);if(this.checkInvalidCoordinates(l),null!==this._validErr)return null;if(this.checkClosedRings(l),null!==this._validErr)return null}i=new li(0,a);if(this.checkTooFewPoints(i),null!==this._validErr)return null;if(this.checkConsistentArea(i),null!==this._validErr)return null;if(!this._isSelfTouchingRingFormingHoleValid&&(this.checkNoSelfIntersectingRings(i),null!==this._validErr))return null;for(u=0;u<a.getNumGeometries();u++){l=a.getGeometryN(u);if(this.checkHolesInShell(l,i),null!==this._validErr)return null}for(u=0;u<a.getNumGeometries();u++){l=a.getGeometryN(u);if(this.checkHolesNotNested(l,i),null!==this._validErr)return null}if(this.checkShellsNotNested(a,i),null!==this._validErr)return null;this.checkConnectedInteriors(i)}else if(arguments[0]instanceof Lt){var h=arguments[0];for(u=0;u<h.getNumGeometries();u++){var c=h.getGeometryN(u);if(this.checkValid(c),null!==this._validErr)return null}}else if(arguments[0]instanceof K){var f=arguments[0];if(this._validErr=null,f.isEmpty())return null;if(f instanceof Vt)this.checkValid(f);else if(f instanceof Xt)this.checkValid(f);else if(f instanceof Ht)this.checkValid(f);else if(f instanceof Bt)this.checkValid(f);else if(f instanceof Ut)this.checkValid(f);else if(f instanceof Wt)this.checkValid(f);else{if(!(f instanceof Lt))throw new UnsupportedOperationException(f.getClass().getName());this.checkValid(f)}}},setSelfTouchingRingFormingHoleValid:function(t){this._isSelfTouchingRingFormingHoleValid=t},checkShellNotNested:function(t,e,n){var i=t.getCoordinates(),r=e.getExteriorRing(),s=r.getCoordinates(),o=no.findPtNotNode(i,r,n);if(null===o)return null;if(!ke.isInRing(o,s))return null;if(e.getNumInteriorRing()<=0)return this._validErr=new eo(eo.NESTED_SHELLS,o),null;for(var a=null,u=0;u<e.getNumInteriorRing();u++){var l=e.getInteriorRingN(u);if(null===(a=this.checkShellInsideHole(t,l,n)))return null}this._validErr=new eo(eo.NESTED_SHELLS,a)},checkClosedRings:function(t){if(this.checkClosedRing(t.getExteriorRing()),null!==this._validErr)return null;for(var e=0;e<t.getNumInteriorRing();e++)if(this.checkClosedRing(t.getInteriorRingN(e)),null!==this._validErr)return null},checkClosedRing:function(t){if(!t.isClosed()){var e=null;1<=t.getNumPoints()&&(e=t.getCoordinateN(0)),this._validErr=new eo(eo.RING_NOT_CLOSED,e)}},checkShellsNotNested:function(t,e){for(var n=0;n<t.getNumGeometries();n++)for(var i=t.getGeometryN(n).getExteriorRing(),r=0;r<t.getNumGeometries();r++)if(n!==r){var s=t.getGeometryN(r);if(this.checkShellNotNested(i,s,e),null!==this._validErr)return null}},interfaces_:function(){return[]},getClass:function(){return no}}),no.findPtNotNode=function(t,e,n){for(var i=n.findEdge(e).getEdgeIntersectionList(),r=0;r<t.length;r++){var s=t[r];if(!i.isIntersection(s))return s}return null},no.isValid=function(){if(arguments[0]instanceof K)return new no(arguments[0]).isValid();if(arguments[0]instanceof x){var t=arguments[0];return!S.isNaN(t.x)&&(!S.isInfinite(t.x)&&(!S.isNaN(t.y)&&!S.isInfinite(t.y)))}};var io=Object.freeze({IsValidOp:no,ConsistentAreaTester:$s}),ro=Object.freeze({BoundaryOp:Rt,IsSimpleOp:sr,buffer:Gr,distance:kr,linemerge:ns,overlay:Es,polygonize:Ts,relate:Us,union:Zs,valid:io});function so(){jt.CoordinateOperation.apply(this),this._targetPM=null,this._removeCollapsed=!0;var t=arguments[0],e=arguments[1];this._targetPM=t,this._removeCollapsed=e}function oo(){this._targetPM=null,this._removeCollapsed=!0,this._changePrecisionModel=!1,this._isPointwise=!1;var t=arguments[0];this._targetPM=t}p(so,jt.CoordinateOperation),e(so.prototype,{edit:function(){if(2===arguments.length&&arguments[1]instanceof K&&arguments[0]instanceof Array){var t=arguments[0],e=arguments[1];if(0===t.length)return null;for(var n=new Array(t.length).fill(null),i=0;i<t.length;i++){var r=new x(t[i]);this._targetPM.makePrecise(r),n[i]=r}var s=new b(n,!1).toCoordinateArray(),o=0;e instanceof Bt&&(o=2),e instanceof Ht&&(o=4);var a=n;return this._removeCollapsed&&(a=null),s.length<o?a:s}return jt.CoordinateOperation.prototype.edit.apply(this,arguments)},interfaces_:function(){return[]},getClass:function(){return so}}),e(oo.prototype,{fixPolygonalTopology:function(t){var e=t;this._changePrecisionModel||(e=this.changePM(t,this._targetPM));var n=Fr.bufferOp(e,0),i=n;return this._changePrecisionModel||(i=n.copy(),this.changePM(i,t.getPrecisionModel())),i},reducePointwise:function(t){var e=null;this._changePrecisionModel?e=new jt(this.createFactory(t.getFactory(),this._targetPM)):e=new jt;var n=this._removeCollapsed;return 2<=t.getDimension()&&(n=!0),e.edit(t,new so(this._targetPM,n))},changePM:function(t,e){return this.createEditor(t.getFactory(),e).edit(t,new jt.NoOpGeometryOperation)},setRemoveCollapsedComponents:function(t){this._removeCollapsed=t},createFactory:function(t,e){return new le(e,t.getSRID(),t.getCoordinateSequenceFactory())},setChangePrecisionModel:function(t){this._changePrecisionModel=t},reduce:function(t){var e=this.reducePointwise(t);return this._isPointwise?e:L(e,kt)?no.isValid(e)?e:this.fixPolygonalTopology(e):e},setPointwise:function(t){this._isPointwise=t},createEditor:function(t,e){return t.getPrecisionModel()===e?new jt:new jt(this.createFactory(t,e))},interfaces_:function(){return[]},getClass:function(){return oo}}),oo.reduce=function(t,e){return new oo(e).reduce(t)},oo.reducePointwise=function(t,e){var n=new oo(e);return n.setPointwise(!0),n.reduce(t)};var ao=Object.freeze({GeometryPrecisionReducer:oo});function uo(){this._pts=null,this._usePt=null,this._distanceTolerance=null,this._seg=new me;var t=arguments[0];this._pts=t}function lo(){this._inputGeom=null,this._distanceTolerance=null,this._isEnsureValidTopology=!0;var t=arguments[0];this._inputGeom=t}function ho(){Tn.apply(this),this._isEnsureValidTopology=!0,this._distanceTolerance=null;var t=arguments[0],e=arguments[1];this._isEnsureValidTopology=t,this._distanceTolerance=e}function co(){if(this._parent=null,this._index=null,2===arguments.length){var t=arguments[0],e=arguments[1];co.call(this,t,e,null,-1)}else if(4===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2],s=arguments[3];me.call(this,n,i),this._parent=r,this._index=s}}function fo(){if(this._parentLine=null,this._segs=null,this._resultSegs=new P,this._minimumSize=null,1===arguments.length){var t=arguments[0];fo.call(this,t,2)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this._parentLine=e,this._minimumSize=n,this.init()}}function go(){this._index=new Ei}function _o(){this._querySeg=null,this._items=new P;var t=arguments[0];this._querySeg=t}function po(){this._li=new pe,this._inputIndex=new go,this._outputIndex=new go,this._line=null,this._linePts=null;var t=arguments[this._distanceTolerance=0],e=arguments[1];this._inputIndex=t,this._outputIndex=e}function mo(){this._inputIndex=new go,this._outputIndex=new go,this._distanceTolerance=0}function vo(){this._inputGeom=null,this._lineSimplifier=new mo,this._linestringMap=null;var t=arguments[0];this._inputGeom=t}function yo(){Tn.apply(this),this._linestringMap=null;var t=arguments[0];this._linestringMap=t}function xo(){this.tps=null;var t=arguments[0];this.tps=t}e(uo.prototype,{simplifySection:function(t,e){if(t+1===e)return null;this._seg.p0=this._pts[t],this._seg.p1=this._pts[e];for(var n=-1,i=t,r=t+1;r<e;r++){var s=this._seg.distance(this._pts[r]);n<s&&(n=s,i=r)}if(n<=this._distanceTolerance)for(r=t+1;r<e;r++)this._usePt[r]=!1;else this.simplifySection(t,i),this.simplifySection(i,e)},setDistanceTolerance:function(t){this._distanceTolerance=t},simplify:function(){this._usePt=new Array(this._pts.length).fill(null);for(var t=0;t<this._pts.length;t++)this._usePt[t]=!0;this.simplifySection(0,this._pts.length-1);var e=new b;for(t=0;t<this._pts.length;t++)this._usePt[t]&&e.add(new x(this._pts[t]));return e.toCoordinateArray()},interfaces_:function(){return[]},getClass:function(){return uo}}),uo.simplify=function(t,e){var n=new uo(t);return n.setDistanceTolerance(e),n.simplify()},e(lo.prototype,{setEnsureValid:function(t){this._isEnsureValidTopology=t},getResultGeometry:function(){return this._inputGeom.isEmpty()?this._inputGeom.copy():new ho(this._isEnsureValidTopology,this._distanceTolerance).transform(this._inputGeom)},setDistanceTolerance:function(t){if(t<0)throw new c("Tolerance must be non-negative");this._distanceTolerance=t},interfaces_:function(){return[]},getClass:function(){return lo}}),lo.simplify=function(t,e){var n=new lo(t);return n.setDistanceTolerance(e),n.getResultGeometry()},p(ho,Tn),e(ho.prototype,{transformPolygon:function(t,e){if(t.isEmpty())return null;var n=Tn.prototype.transformPolygon.call(this,t,e);return e instanceof Wt?n:this.createValidArea(n)},createValidArea:function(t){return this._isEnsureValidTopology?t.buffer(0):t},transformCoordinates:function(t,e){var n=t.toCoordinateArray(),i=null;return i=0===n.length?new Array(0).fill(null):uo.simplify(n,this._distanceTolerance),this._factory.getCoordinateSequenceFactory().create(i)},transformMultiPolygon:function(t,e){var n=Tn.prototype.transformMultiPolygon.call(this,t,e);return this.createValidArea(n)},transformLinearRing:function(t,e){var n=e instanceof Ut,i=Tn.prototype.transformLinearRing.call(this,t,e);return!n||i instanceof Ht?i:null},interfaces_:function(){return[]},getClass:function(){return ho}}),lo.DPTransformer=ho,p(co,me),e(co.prototype,{getIndex:function(){return this._index},getParent:function(){return this._parent},interfaces_:function(){return[]},getClass:function(){return co}}),e(fo.prototype,{addToResult:function(t){this._resultSegs.add(t)},asLineString:function(){return this._parentLine.getFactory().createLineString(fo.extractCoordinates(this._resultSegs))},getResultSize:function(){var t=this._resultSegs.size();return 0===t?0:t+1},getParent:function(){return this._parentLine},getSegment:function(t){return this._segs[t]},getParentCoordinates:function(){return this._parentLine.getCoordinates()},getMinimumSize:function(){return this._minimumSize},asLinearRing:function(){return this._parentLine.getFactory().createLinearRing(fo.extractCoordinates(this._resultSegs))},getSegments:function(){return this._segs},init:function(){var t=this._parentLine.getCoordinates();this._segs=new Array(t.length-1).fill(null);for(var e=0;e<t.length-1;e++){var n=new co(t[e],t[e+1],this._parentLine,e);this._segs[e]=n}},getResultCoordinates:function(){return fo.extractCoordinates(this._resultSegs)},interfaces_:function(){return[]},getClass:function(){return fo}}),fo.extractCoordinates=function(t){for(var e=new Array(t.size()+1).fill(null),n=null,i=0;i<t.size();i++)n=t.get(i),e[i]=n.p0;return e[e.length-1]=n.p1,e},e(go.prototype,{remove:function(t){this._index.remove(new M(t.p0,t.p1),t)},add:function(){if(arguments[0]instanceof fo)for(var t=arguments[0].getSegments(),e=0;e<t.length;e++){var n=t[e];this.add(n)}else if(arguments[0]instanceof me){var i=arguments[0];this._index.insert(new M(i.p0,i.p1),i)}},query:function(t){var e=new M(t.p0,t.p1),n=new _o(t);return this._index.query(e,n),n.getItems()},interfaces_:function(){return[]},getClass:function(){return go}}),e(_o.prototype,{visitItem:function(t){var e=t;M.intersects(e.p0,e.p1,this._querySeg.p0,this._querySeg.p1)&&this._items.add(t)},getItems:function(){return this._items},interfaces_:function(){return[Te]},getClass:function(){return _o}}),e(po.prototype,{flatten:function(t,e){var n=new me(this._linePts[t],this._linePts[e]);return this.remove(this._line,t,e),this._outputIndex.add(n),n},hasBadIntersection:function(t,e,n){return!!this.hasBadOutputIntersection(n)||!!this.hasBadInputIntersection(t,e,n)},setDistanceTolerance:function(t){this._distanceTolerance=t},simplifySection:function(t,e,n){n+=1;var i=new Array(2).fill(null);if(t+1===e){var r=this._line.getSegment(t);return this._line.addToResult(r),null}var s=!0;this._line.getResultSize()<this._line.getMinimumSize()&&(n+1<this._line.getMinimumSize()&&(s=!1));var o=new Array(1).fill(null),a=this.findFurthestPoint(this._linePts,t,e,o);o[0]>this._distanceTolerance&&(s=!1);var u=new me;if(u.p0=this._linePts[t],u.p1=this._linePts[e],i[0]=t,i[1]=e,this.hasBadIntersection(this._line,i,u)&&(s=!1),s){r=this.flatten(t,e);return this._line.addToResult(r),null}this.simplifySection(t,a,n),this.simplifySection(a,e,n)},hasBadOutputIntersection:function(t){for(var e=this._outputIndex.query(t).iterator();e.hasNext();){var n=e.next();if(this.hasInteriorIntersection(n,t))return!0}return!1},findFurthestPoint:function(t,e,n,i){var r=new me;r.p0=t[e],r.p1=t[n];for(var s=-1,o=e,a=e+1;a<n;a++){var u=t[a],l=r.distance(u);s<l&&(s=l,o=a)}return i[0]=s,o},simplify:function(t){this._line=t,this._linePts=t.getParentCoordinates(),this.simplifySection(0,this._linePts.length-1,0)},remove:function(t,e,n){for(var i=e;i<n;i++){var r=t.getSegment(i);this._inputIndex.remove(r)}},hasInteriorIntersection:function(t,e){return this._li.computeIntersection(t.p0,t.p1,e.p0,e.p1),this._li.isInteriorIntersection()},hasBadInputIntersection:function(t,e,n){for(var i=this._inputIndex.query(n).iterator();i.hasNext();){var r=i.next();if(this.hasInteriorIntersection(r,n)){if(po.isInLineSection(t,e,r))continue;return!0}}return!1},interfaces_:function(){return[]},getClass:function(){return po}}),po.isInLineSection=function(t,e,n){if(n.getParent()!==t.getParent())return!1;var i=n.getIndex();return i>=e[0]&&i<e[1]},e(mo.prototype,{setDistanceTolerance:function(t){this._distanceTolerance=t},simplify:function(t){for(var e=t.iterator();e.hasNext();)this._inputIndex.add(e.next());for(e=t.iterator();e.hasNext();){var n=new po(this._inputIndex,this._outputIndex);n.setDistanceTolerance(this._distanceTolerance),n.simplify(e.next())}},interfaces_:function(){return[]},getClass:function(){return mo}}),e(vo.prototype,{getResultGeometry:function(){return this._inputGeom.isEmpty()?this._inputGeom.copy():(this._linestringMap=new oe,this._inputGeom.apply(new xo(this)),this._lineSimplifier.simplify(this._linestringMap.values()),new yo(this._linestringMap).transform(this._inputGeom))},setDistanceTolerance:function(t){if(t<0)throw new c("Tolerance must be non-negative");this._lineSimplifier.setDistanceTolerance(t)},interfaces_:function(){return[]},getClass:function(){return vo}}),vo.simplify=function(t,e){var n=new vo(t);return n.setDistanceTolerance(e),n.getResultGeometry()},p(yo,Tn),e(yo.prototype,{transformCoordinates:function(t,e){if(0===t.size())return null;if(e instanceof Bt){var n=this._linestringMap.get(e);return this.createCoordinateSequence(n.getResultCoordinates())}return Tn.prototype.transformCoordinates.call(this,t,e)},interfaces_:function(){return[]},getClass:function(){return yo}}),e(xo.prototype,{filter:function(t){if(t instanceof Bt){var e=t;if(e.isEmpty())return null;var n=new fo(e,e.isClosed()?4:2);this.tps._linestringMap.put(e,n)}},interfaces_:function(){return[j]},getClass:function(){return xo}}),vo.LineStringTransformer=yo,vo.LineStringMapBuilderFilter=xo;var Eo=Object.freeze({DouglasPeuckerSimplifier:lo,TopologyPreservingSimplifier:vo});function Io(){this._seg=null,this._segLen=null,this._splitPt=null;var t=arguments[this._minimumLen=0];this._seg=t,this._segLen=t.getLength()}function No(){}function Co(){}function So(){}function Lo(){if(this._p=null,1===arguments.length){var t=arguments[0];this._p=new x(t)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];this._p=new x(e,n)}else if(3===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2];this._p=new x(i,r,s)}}function wo(){this._isOnConstraint=null,this._constraint=null;var t=arguments[0];Lo.call(this,t)}function Ro(){this._rot=null,this._vertex=null,this._next=null,this._data=null}function To(){this._subdiv=null,this._isUsingTolerance=!1;var t=arguments[0];this._subdiv=t,this._isUsingTolerance=0<t.getTolerance()}function Po(){}function Oo(){this._subdiv=null,this._lastEdge=null;var t=arguments[0];this._subdiv=t,this.init()}function bo(){if(this._seg=null,1===arguments.length){if("string"==typeof arguments[0]){var t=arguments[0];v.call(this,t)}else if(arguments[0]instanceof me){var e=arguments[0];v.call(this,"Locate failed to converge (at edge: "+e+").  Possible causes include invalid Subdivision topology or very close sites"),this._seg=new me(e)}}else if(2===arguments.length){var n=arguments[0],i=arguments[1];v.call(this,bo.msgWithSpatial(n,i)),this._seg=new me(i)}}function Mo(){}function Do(){this._visitedKey=0,this._quadEdges=new P,this._startingEdge=null,this._tolerance=null,this._edgeCoincidenceTolerance=null,this._frameVertex=new Array(3).fill(null),this._frameEnv=null,this._locator=null,this._seg=new me,this._triEdges=new Array(3).fill(null);var t=arguments[0],e=arguments[1];this._tolerance=e,this._edgeCoincidenceTolerance=e/Do.EDGE_COINCIDENCE_TOL_FACTOR,this.createFrame(t),this._startingEdge=this.initSubdiv(),this._locator=new Oo(this)}function Ao(){}function Fo(){this._triList=new P}function Go(){this._triList=new P}function qo(){this._coordList=new b,this._triCoords=new P}function Bo(){if(this._ls=null,this._data=null,2===arguments.length){var t=arguments[0],e=arguments[1];this._ls=new me(t,e)}else if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];this._ls=new me(n,i),this._data=r}else if(6===arguments.length){var s=arguments[0],o=arguments[1],a=arguments[2],u=arguments[3],l=arguments[4],h=arguments[5];Bo.call(this,new x(s,o,a),new x(u,l,h))}else if(7===arguments.length){var c=arguments[0],f=arguments[1],g=arguments[2],d=arguments[3],_=arguments[4],p=arguments[5],m=arguments[6];Bo.call(this,new x(c,f,g),new x(d,_,p),m)}}function zo(){this._initialVertices=null,this._segVertices=null,this._segments=new P,this._subdiv=null,this._incDel=null,this._convexHull=null,this._splitFinder=new Co,this._kdt=null,this._vertexFactory=null,this._computeAreaEnv=null,this._splitPt=null,this._tolerance=null;var t=arguments[0],e=arguments[1];this._initialVertices=new P(t),this._tolerance=e,this._kdt=new gi(e)}function Vo(){this._siteCoords=null,this._tolerance=0,this._subdiv=null}function Yo(){this._siteCoords=null,this._constraintLines=null,this._tolerance=0,this._subdiv=null,this._constraintVertexMap=new pt}function ko(){this._siteCoords=null,this._tolerance=0,this._subdiv=null,this._clipEnv=null,this._diagramEnv=null}e(Io.prototype,{splitAt:function(){if(1===arguments.length){var t=arguments[0],e=this._minimumLen/this._segLen;if(t.distance(this._seg.p0)<this._minimumLen)return this._splitPt=this._seg.pointAlong(e),null;if(t.distance(this._seg.p1)<this._minimumLen)return this._splitPt=Io.pointAlongReverse(this._seg,e),null;this._splitPt=t}else if(2===arguments.length){var n=arguments[0],i=arguments[1],r=this.getConstrainedLength(n)/this._segLen;i.equals2D(this._seg.p0)?this._splitPt=this._seg.pointAlong(r):this._splitPt=Io.pointAlongReverse(this._seg,r)}},setMinimumLength:function(t){this._minimumLen=t},getConstrainedLength:function(t){return t<this._minimumLen?this._minimumLen:t},getSplitPoint:function(){return this._splitPt},interfaces_:function(){return[]},getClass:function(){return Io}}),Io.pointAlongReverse=function(t,e){var n=new x;return n.x=t.p1.x-e*(t.p1.x-t.p0.x),n.y=t.p1.y-e*(t.p1.y-t.p0.y),n},e(No.prototype,{findSplitPoint:function(t,e){},interfaces_:function(){return[]},getClass:function(){return No}}),e(Co.prototype,{findSplitPoint:function(t,e){var n=t.getLineSegment(),i=n.getLength()/2,r=new Io(n),s=Co.projectedSplitPoint(t,e),o=2*s.distance(e)*.8;return i<o&&(o=i),r.setMinimumLength(o),r.splitAt(s),r.getSplitPoint()},interfaces_:function(){return[No]},getClass:function(){return Co}}),Co.projectedSplitPoint=function(t,e){return t.getLineSegment().project(e)},e(So.prototype,{interfaces_:function(){return[]},getClass:function(){return So}}),So.triArea=function(t,e,n){return(e.x-t.x)*(n.y-t.y)-(e.y-t.y)*(n.x-t.x)},So.isInCircleDDNormalized=function(t,e,n,i){var r=B.valueOf(t.x).selfSubtract(i.x),s=B.valueOf(t.y).selfSubtract(i.y),o=B.valueOf(e.x).selfSubtract(i.x),a=B.valueOf(e.y).selfSubtract(i.y),u=B.valueOf(n.x).selfSubtract(i.x),l=B.valueOf(n.y).selfSubtract(i.y),h=r.multiply(a).selfSubtract(o.multiply(s)),c=o.multiply(l).selfSubtract(u.multiply(a)),f=u.multiply(s).selfSubtract(r.multiply(l)),g=r.multiply(r).selfAdd(s.multiply(s)),d=o.multiply(o).selfAdd(a.multiply(a)),_=u.multiply(u).selfAdd(l.multiply(l));return 0<g.selfMultiply(c).selfAdd(d.selfMultiply(f)).selfAdd(_.selfMultiply(h)).doubleValue()},So.checkRobustInCircle=function(t,e,n,i){var r=So.isInCircleNonRobust(t,e,n,i),s=So.isInCircleDDSlow(t,e,n,i),o=So.isInCircleCC(t,e,n,i),a=Ee.circumcentre(t,e,n);Y.out.println("p radius diff a = "+Math.abs(i.distance(a)-t.distance(a))/t.distance(a)),r===s&&r===o||(Y.out.println("inCircle robustness failure (double result = "+r+", DD result = "+s+", CC result = "+o+")"),Y.out.println(de.toLineString(new $t([t,e,n,i]))),Y.out.println("Circumcentre = "+de.toPoint(a)+" radius = "+t.distance(a)),Y.out.println("p radius diff a = "+Math.abs(i.distance(a)/t.distance(a)-1)),Y.out.println("p radius diff b = "+Math.abs(i.distance(a)/e.distance(a)-1)),Y.out.println("p radius diff c = "+Math.abs(i.distance(a)/n.distance(a)-1)),Y.out.println())},So.isInCircleDDFast=function(t,e,n,i){var r=B.sqr(t.x).selfAdd(B.sqr(t.y)).selfMultiply(So.triAreaDDFast(e,n,i)),s=B.sqr(e.x).selfAdd(B.sqr(e.y)).selfMultiply(So.triAreaDDFast(t,n,i)),o=B.sqr(n.x).selfAdd(B.sqr(n.y)).selfMultiply(So.triAreaDDFast(t,e,i)),a=B.sqr(i.x).selfAdd(B.sqr(i.y)).selfMultiply(So.triAreaDDFast(t,e,n));return 0<r.selfSubtract(s).selfAdd(o).selfSubtract(a).doubleValue()},So.isInCircleCC=function(t,e,n,i){var r=Ee.circumcentre(t,e,n),s=t.distance(r);return i.distance(r)-s<=0},So.isInCircleNormalized=function(t,e,n,i){var r=t.x-i.x,s=t.y-i.y,o=e.x-i.x,a=e.y-i.y,u=n.x-i.x,l=n.y-i.y;return 0<(r*r+s*s)*(o*l-u*a)+(o*o+a*a)*(u*s-r*l)+(u*u+l*l)*(r*a-o*s)},So.isInCircleDDSlow=function(t,e,n,i){var r=B.valueOf(i.x),s=B.valueOf(i.y),o=B.valueOf(t.x),a=B.valueOf(t.y),u=B.valueOf(e.x),l=B.valueOf(e.y),h=B.valueOf(n.x),c=B.valueOf(n.y),f=o.multiply(o).add(a.multiply(a)).multiply(So.triAreaDDSlow(u,l,h,c,r,s)),g=u.multiply(u).add(l.multiply(l)).multiply(So.triAreaDDSlow(o,a,h,c,r,s)),d=h.multiply(h).add(c.multiply(c)).multiply(So.triAreaDDSlow(o,a,u,l,r,s)),_=r.multiply(r).add(s.multiply(s)).multiply(So.triAreaDDSlow(o,a,u,l,h,c));return 0<f.subtract(g).add(d).subtract(_).doubleValue()},So.isInCircleNonRobust=function(t,e,n,i){return 0<(t.x*t.x+t.y*t.y)*So.triArea(e,n,i)-(e.x*e.x+e.y*e.y)*So.triArea(t,n,i)+(n.x*n.x+n.y*n.y)*So.triArea(t,e,i)-(i.x*i.x+i.y*i.y)*So.triArea(t,e,n)},So.isInCircleRobust=function(t,e,n,i){return So.isInCircleNormalized(t,e,n,i)},So.triAreaDDSlow=function(t,e,n,i,r,s){return n.subtract(t).multiply(s.subtract(e)).subtract(i.subtract(e).multiply(r.subtract(t)))},So.triAreaDDFast=function(t,e,n){var i=B.valueOf(e.x).selfSubtract(t.x).selfMultiply(B.valueOf(n.y).selfSubtract(t.y)),r=B.valueOf(e.y).selfSubtract(t.y).selfMultiply(B.valueOf(n.x).selfSubtract(t.x));return i.selfSubtract(r)},e(Lo.prototype,{circleCenter:function(e,n){var i=new Lo(this.getX(),this.getY()),t=new k(this.bisector(i,e),this.bisector(e,n)),r=null;try{r=new Lo(t.getX(),t.getY())}catch(t){if(!(t instanceof A))throw t;Y.err.println("a: "+i+"  b: "+e+"  c: "+n),Y.err.println(t)}return r},dot:function(t){return this._p.x*t.getX()+this._p.y*t.getY()},magn:function(){return Math.sqrt(this._p.x*this._p.x+this._p.y*this._p.y)},getZ:function(){return this._p.z},bisector:function(t,e){var n=e.getX()-t.getX(),i=e.getY()-t.getY();return new k(new k(t.getX()+n/2,t.getY()+i/2,1),new k(t.getX()-i+n/2,t.getY()+n+i/2,1))},equals:function(){if(1===arguments.length){var t=arguments[0];return this._p.x===t.getX()&&this._p.y===t.getY()}if(2===arguments.length){var e=arguments[0],n=arguments[1];return this._p.distance(e.getCoordinate())<n}},getCoordinate:function(){return this._p},isInCircle:function(t,e,n){return So.isInCircleRobust(t._p,e._p,n._p,this._p)},interpolateZValue:function(t,e,n){var i=t.getX(),r=t.getY(),s=e.getX()-i,o=n.getX()-i,a=e.getY()-r,u=n.getY()-r,l=s*u-o*a,h=this.getX()-i,c=this.getY()-r,f=(u*h-o*c)/l,g=(-a*h+s*c)/l;return t.getZ()+f*(e.getZ()-t.getZ())+g*(n.getZ()-t.getZ())},midPoint:function(t){return new Lo((this._p.x+t.getX())/2,(this._p.y+t.getY())/2,(this._p.z+t.getZ())/2)},rightOf:function(t){return this.isCCW(t.dest(),t.orig())},isCCW:function(t,e){return 0<(t._p.x-this._p.x)*(e._p.y-this._p.y)-(t._p.y-this._p.y)*(e._p.x-this._p.x)},getX:function(){return this._p.x},crossProduct:function(t){return this._p.x*t.getY()-this._p.y*t.getX()},setZ:function(t){this._p.z=t},times:function(t){return new Lo(t*this._p.x,t*this._p.y)},cross:function(){return new Lo(this._p.y,-this._p.x)},leftOf:function(t){return this.isCCW(t.orig(),t.dest())},toString:function(){return"POINT ("+this._p.x+" "+this._p.y+")"},sub:function(t){return new Lo(this._p.x-t.getX(),this._p.y-t.getY())},getY:function(){return this._p.y},classify:function(t,e){var n=e.sub(t),i=this.sub(t),r=n.crossProduct(i);return 0<r?Lo.LEFT:r<0?Lo.RIGHT:n.getX()*i.getX()<0||n.getY()*i.getY()<0?Lo.BEHIND:n.magn()<i.magn()?Lo.BEYOND:t.equals(this)?Lo.ORIGIN:e.equals(this)?Lo.DESTINATION:Lo.BETWEEN},sum:function(t){return new Lo(this._p.x+t.getX(),this._p.y+t.getY())},distance:function(t,e){return Math.sqrt(Math.pow(e.getX()-t.getX(),2)+Math.pow(e.getY()-t.getY(),2))},circumRadiusRatio:function(t,e){var n=this.circleCenter(t,e),i=this.distance(n,t),r=this.distance(this,t),s=this.distance(t,e);return s<r&&(r=s),(s=this.distance(e,this))<r&&(r=s),i/r},interfaces_:function(){return[]},getClass:function(){return Lo}}),Lo.interpolateZ=function(){if(3===arguments.length){var t=arguments[0],e=arguments[1],n=arguments[2],i=e.distance(n),r=t.distance(e),s=n.z-e.z;return e.z+s*(r/i)}if(4===arguments.length){var o=arguments[0],a=arguments[1],u=arguments[2],l=arguments[3],h=a.x,c=a.y,f=u.x-h,g=l.x-h,d=u.y-c,_=l.y-c,p=f*_-g*d,m=o.x-h,v=o.y-c,y=(_*m-g*v)/p,x=(-d*m+f*v)/p;return a.z+y*(u.z-a.z)+x*(l.z-a.z)}},Lo.LEFT=0,Lo.RIGHT=1,Lo.BEYOND=2,Lo.BEHIND=3,Lo.BETWEEN=4,Lo.ORIGIN=5,Lo.DESTINATION=6,p(wo,Lo),e(wo.prototype,{getConstraint:function(){return this._constraint},setOnConstraint:function(t){this._isOnConstraint=t},merge:function(t){t._isOnConstraint&&(this._isOnConstraint=!0,this._constraint=t._constraint)},isOnConstraint:function(){return this._isOnConstraint},setConstraint:function(t){this._isOnConstraint=!0,this._constraint=t},interfaces_:function(){return[]},getClass:function(){return wo}}),e(Ro.prototype,{equalsNonOriented:function(t){return!!this.equalsOriented(t)||!!this.equalsOriented(t.sym())},toLineSegment:function(){return new me(this._vertex.getCoordinate(),this.dest().getCoordinate())},dest:function(){return this.sym().orig()},oNext:function(){return this._next},equalsOriented:function(t){return!(!this.orig().getCoordinate().equals2D(t.orig().getCoordinate())||!this.dest().getCoordinate().equals2D(t.dest().getCoordinate()))},dNext:function(){return this.sym().oNext().sym()},lPrev:function(){return this._next.sym()},rPrev:function(){return this.sym().oNext()},rot:function(){return this._rot},oPrev:function(){return this._rot._next._rot},sym:function(){return this._rot._rot},setOrig:function(t){this._vertex=t},lNext:function(){return this.invRot().oNext().rot()},getLength:function(){return this.orig().getCoordinate().distance(this.dest().getCoordinate())},invRot:function(){return this._rot.sym()},setDest:function(t){this.sym().setOrig(t)},setData:function(t){this._data=t},getData:function(){return this._data},delete:function(){this._rot=null},orig:function(){return this._vertex},rNext:function(){return this._rot._next.invRot()},toString:function(){var t=this._vertex.getCoordinate(),e=this.dest().getCoordinate();return de.toLineString(t,e)},isLive:function(){return null!==this._rot},getPrimary:function(){return this.orig().getCoordinate().compareTo(this.dest().getCoordinate())<=0?this:this.sym()},dPrev:function(){return this.invRot().oNext().invRot()},setNext:function(t){this._next=t},interfaces_:function(){return[]},getClass:function(){return Ro}}),Ro.makeEdge=function(t,e){var n=new Ro,i=new Ro,r=new Ro,s=new Ro;((((n._rot=i)._rot=r)._rot=s)._rot=n).setNext(n),i.setNext(s),r.setNext(r),s.setNext(i);var o=n;return o.setOrig(t),o.setDest(e),o},Ro.swap=function(t){var e=t.oPrev(),n=t.sym().oPrev();Ro.splice(t,e),Ro.splice(t.sym(),n),Ro.splice(t,e.lNext()),Ro.splice(t.sym(),n.lNext()),t.setOrig(e.dest()),t.setDest(n.dest())},Ro.splice=function(t,e){var n=t.oNext().rot(),i=e.oNext().rot(),r=e.oNext(),s=t.oNext(),o=i.oNext(),a=n.oNext();t.setNext(r),e.setNext(s),n.setNext(o),i.setNext(a)},Ro.connect=function(t,e){var n=Ro.makeEdge(t.dest(),e.orig());return Ro.splice(n,t.lNext()),Ro.splice(n.sym(),e),n},e(To.prototype,{insertSite:function(t){var e=this._subdiv.locate(t);if(this._subdiv.isVertexOfEdge(e,t))return e;this._subdiv.isOnEdge(e,t.getCoordinate())&&(e=e.oPrev(),this._subdiv.delete(e.oNext()));var n=this._subdiv.makeEdge(e.orig(),t);Ro.splice(n,e);for(var i=n;(e=(n=this._subdiv.connect(e,n.sym())).oPrev()).lNext()!==i;);for(;;){var r=e.oPrev();if(r.dest().rightOf(e)&&t.isInCircle(e.orig(),r.dest(),e.dest()))Ro.swap(e),e=e.oPrev();else{if(e.oNext()===i)return n;e=e.oNext().lPrev()}}},insertSites:function(t){for(var e=t.iterator();e.hasNext();){var n=e.next();this.insertSite(n)}},interfaces_:function(){return[]},getClass:function(){return To}}),e(Po.prototype,{locate:function(t){},interfaces_:function(){return[]},getClass:function(){return Po}}),e(Oo.prototype,{init:function(){this._lastEdge=this.findEdge()},locate:function(t){this._lastEdge.isLive()||this.init();var e=this._subdiv.locateFromEdge(t,this._lastEdge);return this._lastEdge=e},findEdge:function(){return this._subdiv.getEdges().iterator().next()},interfaces_:function(){return[Po]},getClass:function(){return Oo}}),p(bo,v),e(bo.prototype,{getSegment:function(){return this._seg},interfaces_:function(){return[]},getClass:function(){return bo}}),bo.msgWithSpatial=function(t,e){return null!==e?t+" [ "+e+" ]":t},e(Mo.prototype,{visit:function(t){},interfaces_:function(){return[]},getClass:function(){return Mo}}),e(Do.prototype,{getTriangleVertices:function(t){var e=new Go;return this.visitTriangles(e,t),e.getTriangleVertices()},isFrameVertex:function(t){return!!t.equals(this._frameVertex[0])||(!!t.equals(this._frameVertex[1])||!!t.equals(this._frameVertex[2]))},isVertexOfEdge:function(t,e){return!(!e.equals(t.orig(),this._tolerance)&&!e.equals(t.dest(),this._tolerance))},connect:function(t,e){var n=Ro.connect(t,e);return this._quadEdges.add(n),n},getVoronoiCellPolygon:function(t,e){var n=new P,i=t;do{var r=t.rot().orig().getCoordinate();n.add(r),t=t.oPrev()}while(t!==i);var s=new b;s.addAll(n,!1),s.closeRing(),s.size()<4&&(Y.out.println(s),s.add(s.get(s.size()-1),!0));var o=s.toCoordinateArray(),a=e.createPolygon(e.createLinearRing(o)),u=i.orig();return a.setUserData(u.getCoordinate()),a},setLocator:function(t){this._locator=t},initSubdiv:function(){var t=this.makeEdge(this._frameVertex[0],this._frameVertex[1]),e=this.makeEdge(this._frameVertex[1],this._frameVertex[2]);Ro.splice(t.sym(),e);var n=this.makeEdge(this._frameVertex[2],this._frameVertex[0]);return Ro.splice(e.sym(),n),Ro.splice(n.sym(),t),t},isFrameBorderEdge:function(t){var e=new Array(3).fill(null);Do.getTriangleEdges(t,e);var n=new Array(3).fill(null);Do.getTriangleEdges(t.sym(),n);var i=t.lNext().dest();if(this.isFrameVertex(i))return!0;var r=t.sym().lNext().dest();return!!this.isFrameVertex(r)},makeEdge:function(t,e){var n=Ro.makeEdge(t,e);return this._quadEdges.add(n),n},visitTriangles:function(t,e){this._visitedKey++;var n=new en;n.push(this._startingEdge);for(var i=new ut;!n.empty();){var r=n.pop();if(!i.contains(r)){var s=this.fetchTriangleToVisit(r,n,e,i);null!==s&&t.visit(s)}}},isFrameEdge:function(t){return!(!this.isFrameVertex(t.orig())&&!this.isFrameVertex(t.dest()))},isOnEdge:function(t,e){return this._seg.setCoordinates(t.orig().getCoordinate(),t.dest().getCoordinate()),this._seg.distance(e)<this._edgeCoincidenceTolerance},getEnvelope:function(){return new M(this._frameEnv)},createFrame:function(t){var e=t.getWidth(),n=t.getHeight(),i=0;i=n<e?10*e:10*n,this._frameVertex[0]=new Lo((t.getMaxX()+t.getMinX())/2,t.getMaxY()+i),this._frameVertex[1]=new Lo(t.getMinX()-i,t.getMinY()-i),this._frameVertex[2]=new Lo(t.getMaxX()+i,t.getMinY()-i),this._frameEnv=new M(this._frameVertex[0].getCoordinate(),this._frameVertex[1].getCoordinate()),this._frameEnv.expandToInclude(this._frameVertex[2].getCoordinate())},getTriangleCoordinates:function(t){var e=new qo;return this.visitTriangles(e,t),e.getTriangles()},getVertices:function(t){for(var e=new ut,n=this._quadEdges.iterator();n.hasNext();){var i=n.next(),r=i.orig();!t&&this.isFrameVertex(r)||e.add(r);var s=i.dest();!t&&this.isFrameVertex(s)||e.add(s)}return e},fetchTriangleToVisit:function(t,e,n,i){var r=t,s=0,o=!1;do{this._triEdges[s]=r,this.isFrameEdge(r)&&(o=!0);var a=r.sym();i.contains(a)||e.push(a),i.add(r),s++,r=r.lNext()}while(r!==t);return o&&!n?null:this._triEdges},getEdges:function(){if(0===arguments.length)return this._quadEdges;if(1===arguments.length){for(var t=arguments[0],e=this.getPrimaryEdges(!1),n=new Array(e.size()).fill(null),i=0,r=e.iterator();r.hasNext();){var s=r.next();n[i++]=t.createLineString([s.orig().getCoordinate(),s.dest().getCoordinate()])}return t.createMultiLineString(n)}},getVertexUniqueEdges:function(t){for(var e=new P,n=new ut,i=this._quadEdges.iterator();i.hasNext();){var r=i.next(),s=r.orig();n.contains(s)||(n.add(s),!t&&this.isFrameVertex(s)||e.add(r));var o=r.sym(),a=o.orig();n.contains(a)||(n.add(a),!t&&this.isFrameVertex(a)||e.add(o))}return e},getTriangleEdges:function(t){var e=new Fo;return this.visitTriangles(e,t),e.getTriangleEdges()},getPrimaryEdges:function(t){this._visitedKey++;var e=new P,n=new en;n.push(this._startingEdge);for(var i=new ut;!n.empty();){var r=n.pop();if(!i.contains(r)){var s=r.getPrimary();!t&&this.isFrameEdge(s)||e.add(s),n.push(r.oNext()),n.push(r.sym().oNext()),i.add(r),i.add(r.sym())}}return e},delete:function(t){Ro.splice(t,t.oPrev()),Ro.splice(t.sym(),t.sym().oPrev());var e=t.sym(),n=t.rot(),i=t.rot().sym();this._quadEdges.remove(t),this._quadEdges.remove(e),this._quadEdges.remove(n),this._quadEdges.remove(i),t.delete(),e.delete(),n.delete(),i.delete()},locateFromEdge:function(t,e){for(var n=0,i=this._quadEdges.size(),r=e;;){if(i<++n)throw new bo(r.toLineSegment());if(t.equals(r.orig())||t.equals(r.dest()))break;if(t.rightOf(r))r=r.sym();else if(t.rightOf(r.oNext())){if(t.rightOf(r.dPrev()))break;r=r.dPrev()}else r=r.oNext()}return r},getTolerance:function(){return this._tolerance},getVoronoiCellPolygons:function(t){this.visitTriangles(new Ao,!0);for(var e=new P,n=this.getVertexUniqueEdges(!1).iterator();n.hasNext();){var i=n.next();e.add(this.getVoronoiCellPolygon(i,t))}return e},getVoronoiDiagram:function(t){var e=this.getVoronoiCellPolygons(t);return t.createGeometryCollection(le.toGeometryArray(e))},getTriangles:function(t){for(var e=this.getTriangleCoordinates(!1),n=new Array(e.size()).fill(null),i=0,r=e.iterator();r.hasNext();){var s=r.next();n[i++]=t.createPolygon(t.createLinearRing(s))}return t.createGeometryCollection(n)},insertSite:function(t){var e=this.locate(t);if(t.equals(e.orig(),this._tolerance)||t.equals(e.dest(),this._tolerance))return e;var n=this.makeEdge(e.orig(),t);Ro.splice(n,e);for(var i=n;(e=(n=this.connect(e,n.sym())).oPrev()).lNext()!==i;);return i},locate:function(){if(1===arguments.length){if(arguments[0]instanceof Lo){var t=arguments[0];return this._locator.locate(t)}if(arguments[0]instanceof x){var e=arguments[0];return this._locator.locate(new Lo(e))}}else if(2===arguments.length){var n=arguments[0],i=arguments[1],r=this._locator.locate(new Lo(n));if(null===r)return null;var s=r;r.dest().getCoordinate().equals2D(n)&&(s=r.sym());var o=s;do{if(o.dest().getCoordinate().equals2D(i))return o;o=o.oNext()}while(o!==s);return null}},interfaces_:function(){return[]},getClass:function(){return Do}}),Do.getTriangleEdges=function(t,e){if(e[0]=t,e[1]=e[0].lNext(),e[2]=e[1].lNext(),e[2].lNext()!==e[0])throw new c("Edges do not form a triangle")},e(Ao.prototype,{visit:function(t){for(var e=t[0].orig().getCoordinate(),n=t[1].orig().getCoordinate(),i=t[2].orig().getCoordinate(),r=new Lo(Ee.circumcentre(e,n,i)),s=0;s<3;s++)t[s].rot().setOrig(r)},interfaces_:function(){return[Mo]},getClass:function(){return Ao}}),e(Fo.prototype,{getTriangleEdges:function(){return this._triList},visit:function(t){this._triList.add(t)},interfaces_:function(){return[Mo]},getClass:function(){return Fo}}),e(Go.prototype,{visit:function(t){this._triList.add([t[0].orig(),t[1].orig(),t[2].orig()])},getTriangleVertices:function(){return this._triList},interfaces_:function(){return[Mo]},getClass:function(){return Go}}),e(qo.prototype,{checkTriangleSize:function(t){2<=t.length?de.toLineString(t[0],t[1]):1<=t.length&&de.toPoint(t[0])},visit:function(t){this._coordList.clear();for(var e=0;e<3;e++){var n=t[e].orig();this._coordList.add(n.getCoordinate())}if(0<this._coordList.size()){this._coordList.closeRing();var i=this._coordList.toCoordinateArray();if(4!==i.length)return null;this._triCoords.add(i)}},getTriangles:function(){return this._triCoords},interfaces_:function(){return[Mo]},getClass:function(){return qo}}),Do.TriangleCircumcentreVisitor=Ao,Do.TriangleEdgesListVisitor=Fo,Do.TriangleVertexListVisitor=Go,Do.TriangleCoordinatesVisitor=qo,Do.EDGE_COINCIDENCE_TOL_FACTOR=1e3,e(Bo.prototype,{getLineSegment:function(){return this._ls},getEndZ:function(){return this._ls.getCoordinate(1).z},getStartZ:function(){return this._ls.getCoordinate(0).z},intersection:function(t){return this._ls.intersection(t.getLineSegment())},getStart:function(){return this._ls.getCoordinate(0)},getEnd:function(){return this._ls.getCoordinate(1)},getEndY:function(){return this._ls.getCoordinate(1).y},getStartX:function(){return this._ls.getCoordinate(0).x},equalsTopo:function(t){return this._ls.equalsTopo(t.getLineSegment())},getStartY:function(){return this._ls.getCoordinate(0).y},setData:function(t){this._data=t},getData:function(){return this._data},getEndX:function(){return this._ls.getCoordinate(1).x},toString:function(){return this._ls.toString()},interfaces_:function(){return[]},getClass:function(){return Bo}}),e(zo.prototype,{getInitialVertices:function(){return this._initialVertices},getKDT:function(){return this._kdt},enforceConstraints:function(){this.addConstraintVertices();for(var t=0;0<this.enforceGabriel(this._segments)&&++t<zo.MAX_SPLIT_ITER;);},insertSites:function(t){for(var e=t.iterator();e.hasNext();){var n=e.next();this.insertSite(n)}},getVertexFactory:function(){return this._vertexFactory},getPointArray:function(){for(var t=new Array(this._initialVertices.size()+this._segVertices.size()).fill(null),e=0,n=this._initialVertices.iterator();n.hasNext();){var i=n.next();t[e++]=i.getCoordinate()}for(var r=this._segVertices.iterator();r.hasNext();){i=r.next();t[e++]=i.getCoordinate()}return t},setConstraints:function(t,e){this._segments=t,this._segVertices=e},computeConvexHull:function(){var t=new le,e=new rn(this.getPointArray(),t);this._convexHull=e.getConvexHull()},addConstraintVertices:function(){this.computeConvexHull(),this.insertSites(this._segVertices)},findNonGabrielPoint:function(t){var e=t.getStart(),n=t.getEnd(),i=new x((e.x+n.x)/2,(e.y+n.y)/2),r=e.distance(i),s=new M(i);s.expandBy(r);for(var o=this._kdt.query(s),a=null,u=S.MAX_VALUE,l=o.iterator();l.hasNext();){var h=l.next().getCoordinate();if(!h.equals2D(e)&&!h.equals2D(n)){var c=i.distance(h);if(c<r){(null===a||c<u)&&(a=h,u=c)}}}return a},getConstraintSegments:function(){return this._segments},setSplitPointFinder:function(t){this._splitFinder=t},getConvexHull:function(){return this._convexHull},getTolerance:function(){return this._tolerance},enforceGabriel:function(t){for(var e=new P,n=0,i=new P,r=t.iterator();r.hasNext();){var s=r.next(),o=this.findNonGabrielPoint(s);if(null!==o){this._splitPt=this._splitFinder.findSplitPoint(s,o);var a=this.createVertex(this._splitPt,s);this.insertSite(a).getCoordinate().equals2D(this._splitPt);var u=new Bo(s.getStartX(),s.getStartY(),s.getStartZ(),a.getX(),a.getY(),a.getZ(),s.getData()),l=new Bo(a.getX(),a.getY(),a.getZ(),s.getEndX(),s.getEndY(),s.getEndZ(),s.getData());e.add(u),e.add(l),i.add(s),n+=1}}return t.removeAll(i),t.addAll(e),n},createVertex:function(){if(1===arguments.length){var t=arguments[0],e=null;return e=null!==this._vertexFactory?this._vertexFactory.createVertex(t,null):new wo(t)}if(2===arguments.length){var n=arguments[0],i=arguments[1];e=null;return(e=null!==this._vertexFactory?this._vertexFactory.createVertex(n,i):new wo(n)).setOnConstraint(!0),e}},getSubdivision:function(){return this._subdiv},computeBoundingBox:function(){var t=zo.computeVertexEnvelope(this._initialVertices),e=zo.computeVertexEnvelope(this._segVertices),n=new M(t);n.expandToInclude(e);var i=.2*n.getWidth(),r=.2*n.getHeight(),s=Math.max(i,r);this._computeAreaEnv=new M(n),this._computeAreaEnv.expandBy(s)},setVertexFactory:function(t){this._vertexFactory=t},formInitialDelaunay:function(){this.computeBoundingBox(),this._subdiv=new Do(this._computeAreaEnv,this._tolerance),this._subdiv.setLocator(new Oo(this._subdiv)),this._incDel=new To(this._subdiv),this.insertSites(this._initialVertices)},insertSite:function(){if(arguments[0]instanceof wo){var t=arguments[0],e=this._kdt.insert(t.getCoordinate(),t);if(e.isRepeated()){var n=e.getData();return n.merge(t),n}return this._incDel.insertSite(t),t}if(arguments[0]instanceof x){var i=arguments[0];this.insertSite(this.createVertex(i))}},interfaces_:function(){return[]},getClass:function(){return zo}}),zo.computeVertexEnvelope=function(t){for(var e=new M,n=t.iterator();n.hasNext();){var i=n.next();e.expandToInclude(i.getCoordinate())}return e},zo.MAX_SPLIT_ITER=99,e(Vo.prototype,{create:function(){if(null!==this._subdiv)return null;var t=Vo.envelope(this._siteCoords),e=Vo.toVertices(this._siteCoords);this._subdiv=new Do(t,this._tolerance),new To(this._subdiv).insertSites(e)},setTolerance:function(t){this._tolerance=t},setSites:function(){if(arguments[0]instanceof K){var t=arguments[0];this._siteCoords=Vo.extractUniqueCoordinates(t)}else if(L(arguments[0],N)){var e=arguments[0];this._siteCoords=Vo.unique(nt.toCoordinateArray(e))}},getEdges:function(t){return this.create(),this._subdiv.getEdges(t)},getSubdivision:function(){return this.create(),this._subdiv},getTriangles:function(t){return this.create(),this._subdiv.getTriangles(t)},interfaces_:function(){return[]},getClass:function(){return Vo}}),Vo.extractUniqueCoordinates=function(t){if(null===t)return new b;var e=t.getCoordinates();return Vo.unique(e)},Vo.envelope=function(t){for(var e=new M,n=t.iterator();n.hasNext();){var i=n.next();e.expandToInclude(i)}return e},Vo.unique=function(t){var e=nt.copyDeep(t);return It.sort(e),new b(e,!1)},Vo.toVertices=function(t){for(var e=new P,n=t.iterator();n.hasNext();){var i=n.next();e.add(new Lo(i))}return e},e(Yo.prototype,{createSiteVertices:function(t){for(var e=new P,n=t.iterator();n.hasNext();){var i=n.next();this._constraintVertexMap.containsKey(i)||e.add(new wo(i))}return e},create:function(){if(null!==this._subdiv)return null;var t=Vo.envelope(this._siteCoords),e=new P;null!==this._constraintLines&&(t.expandToInclude(this._constraintLines.getEnvelopeInternal()),this.createVertices(this._constraintLines),e=Yo.createConstraintSegments(this._constraintLines));var n=new zo(this.createSiteVertices(this._siteCoords),this._tolerance);n.setConstraints(e,new P(this._constraintVertexMap.values())),n.formInitialDelaunay(),n.enforceConstraints(),this._subdiv=n.getSubdivision()},setTolerance:function(t){this._tolerance=t},setConstraints:function(t){this._constraintLines=t},setSites:function(t){this._siteCoords=Vo.extractUniqueCoordinates(t)},getEdges:function(t){return this.create(),this._subdiv.getEdges(t)},getSubdivision:function(){return this.create(),this._subdiv},getTriangles:function(t){return this.create(),this._subdiv.getTriangles(t)},createVertices:function(t){for(var e=t.getCoordinates(),n=0;n<e.length;n++){var i=new wo(e[n]);this._constraintVertexMap.put(e[n],i)}},interfaces_:function(){return[]},getClass:function(){return Yo}}),Yo.createConstraintSegments=function(){if(1===arguments.length){for(var t=arguments[0],e=Ge.getLines(t),n=new P,i=e.iterator();i.hasNext();){var r=i.next();Yo.createConstraintSegments(r,n)}return n}if(2===arguments.length){var s=arguments[0],o=arguments[1],a=s.getCoordinates();for(i=1;i<a.length;i++)o.add(new Bo(a[i-1],a[i]))}},e(ko.prototype,{create:function(){if(null!==this._subdiv)return null;var t=Vo.envelope(this._siteCoords);this._diagramEnv=t;var e=Math.max(this._diagramEnv.getWidth(),this._diagramEnv.getHeight());this._diagramEnv.expandBy(e),null!==this._clipEnv&&this._diagramEnv.expandToInclude(this._clipEnv);var n=Vo.toVertices(this._siteCoords);this._subdiv=new Do(t,this._tolerance),new To(this._subdiv).insertSites(n)},getDiagram:function(t){this.create();var e=this._subdiv.getVoronoiDiagram(t);return ko.clipGeometryCollection(e,this._diagramEnv)},setTolerance:function(t){this._tolerance=t},setSites:function(){if(arguments[0]instanceof K){var t=arguments[0];this._siteCoords=Vo.extractUniqueCoordinates(t)}else if(L(arguments[0],N)){var e=arguments[0];this._siteCoords=Vo.unique(nt.toCoordinateArray(e))}},setClipEnvelope:function(t){this._clipEnv=t},getSubdivision:function(){return this.create(),this._subdiv},interfaces_:function(){return[]},getClass:function(){return ko}}),ko.clipGeometryCollection=function(t,e){for(var n=t.getFactory().toGeometry(e),i=new P,r=0;r<t.getNumGeometries();r++){var s=t.getGeometryN(r),o=null;e.contains(s.getEnvelopeInternal())?o=s:e.intersects(s.getEnvelopeInternal())&&(o=n.intersection(s)).setUserData(s.getUserData()),null===o||o.isEmpty()||i.add(o)}return t.getFactory().createGeometryCollection(le.toGeometryArray(i))};var Uo=Object.freeze({Vertex:Lo}),Xo=Object.freeze({ConformingDelaunayTriangulationBuilder:Yo,DelaunayTriangulationBuilder:Vo,VoronoiDiagramBuilder:ko,quadedge:Uo});function Ho(){if(this._componentIndex=0,this._segmentIndex=0,(this._segmentFraction=0)===arguments.length);else if(1===arguments.length){var t=arguments[0];this._componentIndex=t._componentIndex,this._segmentIndex=t._segmentIndex,this._segmentFraction=t._segmentFraction}else if(2===arguments.length){var e=arguments[0],n=arguments[1];Ho.call(this,0,e,n)}else if(3===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2];this._componentIndex=i,this._segmentIndex=r,this._segmentFraction=s,this.normalize()}else if(4===arguments.length){var o=arguments[0],a=arguments[1],u=arguments[2],l=arguments[3];this._componentIndex=o,this._segmentIndex=a,this._segmentFraction=u,l&&this.normalize()}}function Wo(){if(this._linearGeom=null,this._numLines=null,this._currentLine=null,this._componentIndex=0,this._vertexIndex=0,1===arguments.length){var t=arguments[0];Wo.call(this,t,0,0)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];Wo.call(this,e,n.getComponentIndex(),Wo.segmentEndVertexIndex(n))}else if(3===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2];if(!L(i,mt))throw new c("Lineal geometry is required");this._linearGeom=i,this._numLines=i.getNumGeometries(),this._componentIndex=r,this._vertexIndex=s,this.loadCurrentLine()}}function jo(){this._linearGeom=null;var t=arguments[0];this._linearGeom=t}function Ko(){this._linearGeom=null;var t=arguments[0];this._linearGeom=t}function Zo(){this._geomFact=null,this._lines=new P,this._coordList=null,this._ignoreInvalidLines=!1,this._fixInvalidLines=!1,this._lastPt=null;var t=arguments[0];this._geomFact=t}function Qo(){this._line=null;var t=arguments[0];this._line=t}function Jo(){this._linearGeom=null;var t=arguments[0];this._linearGeom=t,this.checkGeometryType()}function $o(){this._linearGeom=null;var t=arguments[0];this._linearGeom=t}function ta(){this._linearGeom=null;var t=arguments[0];this._linearGeom=t}function ea(){this._linearGeom=null;var t=arguments[0];this._linearGeom=t}e(Ho.prototype,{getSegmentIndex:function(){return this._segmentIndex},getComponentIndex:function(){return this._componentIndex},isEndpoint:function(t){var e=t.getGeometryN(this._componentIndex).getNumPoints()-1;return this._segmentIndex>=e||this._segmentIndex===e&&1<=this._segmentFraction},isValid:function(t){if(this._componentIndex<0||this._componentIndex>=t.getNumGeometries())return!1;var e=t.getGeometryN(this._componentIndex);return!(this._segmentIndex<0||this._segmentIndex>e.getNumPoints())&&((this._segmentIndex!==e.getNumPoints()||0===this._segmentFraction)&&!(this._segmentFraction<0||1<this._segmentFraction))},normalize:function(){this._segmentFraction<0&&(this._segmentFraction=0),1<this._segmentFraction&&(this._segmentFraction=1),this._componentIndex<0&&(this._componentIndex=0,this._segmentIndex=0,this._segmentFraction=0),this._segmentIndex<0&&(this._segmentIndex=0,this._segmentFraction=0),1===this._segmentFraction&&(this._segmentFraction=0,this._segmentIndex+=1)},toLowest:function(t){var e=t.getGeometryN(this._componentIndex).getNumPoints()-1;return this._segmentIndex<e?this:new Ho(this._componentIndex,e,1,!1)},getCoordinate:function(t){var e=t.getGeometryN(this._componentIndex),n=e.getCoordinateN(this._segmentIndex);if(this._segmentIndex>=e.getNumPoints()-1)return n;var i=e.getCoordinateN(this._segmentIndex+1);return Ho.pointAlongSegmentByFraction(n,i,this._segmentFraction)},getSegmentFraction:function(){return this._segmentFraction},getSegment:function(t){var e=t.getGeometryN(this._componentIndex),n=e.getCoordinateN(this._segmentIndex);return this._segmentIndex>=e.getNumPoints()-1?new me(e.getCoordinateN(e.getNumPoints()-2),n):new me(n,e.getCoordinateN(this._segmentIndex+1))},clamp:function(t){if(this._componentIndex>=t.getNumGeometries())return this.setToEnd(t),null;if(this._segmentIndex>=t.getNumPoints()){var e=t.getGeometryN(this._componentIndex);this._segmentIndex=e.getNumPoints()-1,this._segmentFraction=1}},setToEnd:function(t){this._componentIndex=t.getNumGeometries()-1;var e=t.getGeometryN(this._componentIndex);this._segmentIndex=e.getNumPoints()-1,this._segmentFraction=1},compareTo:function(t){var e=t;return this._componentIndex<e._componentIndex?-1:this._componentIndex>e._componentIndex?1:this._segmentIndex<e._segmentIndex?-1:this._segmentIndex>e._segmentIndex?1:this._segmentFraction<e._segmentFraction?-1:this._segmentFraction>e._segmentFraction?1:0},copy:function(){return new Ho(this._componentIndex,this._segmentIndex,this._segmentFraction)},toString:function(){return"LinearLoc["+this._componentIndex+", "+this._segmentIndex+", "+this._segmentFraction+"]"},isOnSameSegment:function(t){return this._componentIndex===t._componentIndex&&(this._segmentIndex===t._segmentIndex||(t._segmentIndex-this._segmentIndex==1&&0===t._segmentFraction||this._segmentIndex-t._segmentIndex==1&&0===this._segmentFraction))},snapToVertex:function(t,e){if(this._segmentFraction<=0||1<=this._segmentFraction)return null;var n=this.getSegmentLength(t),i=this._segmentFraction*n,r=n-i;i<=r&&i<e?this._segmentFraction=0:r<=i&&r<e&&(this._segmentFraction=1)},compareLocationValues:function(t,e,n){return this._componentIndex<t?-1:this._componentIndex>t?1:this._segmentIndex<e?-1:this._segmentIndex>e?1:this._segmentFraction<n?-1:this._segmentFraction>n?1:0},getSegmentLength:function(t){var e=t.getGeometryN(this._componentIndex),n=this._segmentIndex;this._segmentIndex>=e.getNumPoints()-1&&(n=e.getNumPoints()-2);var i=e.getCoordinateN(n),r=e.getCoordinateN(n+1);return i.distance(r)},isVertex:function(){return this._segmentFraction<=0||1<=this._segmentFraction},interfaces_:function(){return[n]},getClass:function(){return Ho}}),Ho.getEndLocation=function(t){var e=new Ho;return e.setToEnd(t),e},Ho.pointAlongSegmentByFraction=function(t,e,n){return n<=0?t:1<=n?e:new x((e.x-t.x)*n+t.x,(e.y-t.y)*n+t.y,(e.z-t.z)*n+t.z)},Ho.compareLocationValues=function(t,e,n,i,r,s){return t<i?-1:i<t?1:e<r?-1:r<e?1:n<s?-1:s<n?1:0},e(Wo.prototype,{getComponentIndex:function(){return this._componentIndex},getLine:function(){return this._currentLine},getVertexIndex:function(){return this._vertexIndex},getSegmentEnd:function(){return this._vertexIndex<this.getLine().getNumPoints()-1?this._currentLine.getCoordinateN(this._vertexIndex+1):null},next:function(){if(!this.hasNext())return null;this._vertexIndex++,this._vertexIndex>=this._currentLine.getNumPoints()&&(this._componentIndex++,this.loadCurrentLine(),this._vertexIndex=0)},loadCurrentLine:function(){if(this._componentIndex>=this._numLines)return this._currentLine=null;this._currentLine=this._linearGeom.getGeometryN(this._componentIndex)},getSegmentStart:function(){return this._currentLine.getCoordinateN(this._vertexIndex)},isEndOfLine:function(){return!(this._componentIndex>=this._numLines)&&!(this._vertexIndex<this._currentLine.getNumPoints()-1)},hasNext:function(){return!(this._componentIndex>=this._numLines)&&!(this._componentIndex===this._numLines-1&&this._vertexIndex>=this._currentLine.getNumPoints())},interfaces_:function(){return[]},getClass:function(){return Wo}}),Wo.segmentEndVertexIndex=function(t){return 0<t.getSegmentFraction()?t.getSegmentIndex()+1:t.getSegmentIndex()},e(jo.prototype,{indexOf:function(t){return this.indexOfFromStart(t,null)},indexOfFromStart:function(t,e){for(var n=S.MAX_VALUE,i=0,r=0,s=-1,o=new me,a=new Wo(this._linearGeom);a.hasNext();a.next())if(!a.isEndOfLine()){o.p0=a.getSegmentStart(),o.p1=a.getSegmentEnd();var u=o.distance(t),l=o.segmentFraction(t),h=a.getComponentIndex(),c=a.getVertexIndex();u<n&&(null===e||e.compareLocationValues(h,c,l)<0)&&(i=h,r=c,s=l,n=u)}return n===S.MAX_VALUE?new Ho(e):new Ho(i,r,s)},indexOfAfter:function(t,e){if(null===e)return this.indexOf(t);var n=Ho.getEndLocation(this._linearGeom);if(n.compareTo(e)<=0)return n;var i=this.indexOfFromStart(t,e);return y.isTrue(0<=i.compareTo(e),"computed location is before specified minimum location"),i},interfaces_:function(){return[]},getClass:function(){return jo}}),jo.indexOf=function(t,e){return new jo(t).indexOf(e)},jo.indexOfAfter=function(t,e,n){return new jo(t).indexOfAfter(e,n)},e(Ko.prototype,{indicesOf:function(t){var e=t.getGeometryN(0).getCoordinateN(0),n=t.getGeometryN(t.getNumGeometries()-1),i=n.getCoordinateN(n.getNumPoints()-1),r=new jo(this._linearGeom),s=new Array(2).fill(null);return s[0]=r.indexOf(e),0===t.getLength()?s[1]=s[0].copy():s[1]=r.indexOfAfter(i,s[0]),s},interfaces_:function(){return[]},getClass:function(){return Ko}}),Ko.indicesOf=function(t,e){return new Ko(t).indicesOf(e)},e(Zo.prototype,{getGeometry:function(){return this.endLine(),this._geomFact.buildGeometry(this._lines)},getLastCoordinate:function(){return this._lastPt},endLine:function(){if(null===this._coordList)return null;if(this._ignoreInvalidLines&&this._coordList.size()<2)return this._coordList=null;var t=this._coordList.toCoordinateArray(),e=t;this._fixInvalidLines&&(e=this.validCoordinateSequence(t));var n=this._coordList=null;try{n=this._geomFact.createLineString(e)}catch(t){if(!(t instanceof c))throw t;if(!this._ignoreInvalidLines)throw t}null!==n&&this._lines.add(n)},setFixInvalidLines:function(t){this._fixInvalidLines=t},add:function(){if(1===arguments.length){var t=arguments[0];this.add(t,!0)}else if(2===arguments.length){var e=arguments[0],n=arguments[1];null===this._coordList&&(this._coordList=new b),this._coordList.add(e,n),this._lastPt=e}},setIgnoreInvalidLines:function(t){this._ignoreInvalidLines=t},validCoordinateSequence:function(t){return 2<=t.length?t:[t[0],t[0]]},interfaces_:function(){return[]},getClass:function(){return Zo}}),e(Qo.prototype,{computeLinear:function(t,e){var n=new Zo(this._line.getFactory());n.setFixInvalidLines(!0),t.isVertex()||n.add(t.getCoordinate(this._line));for(var i=new Wo(this._line,t);i.hasNext()&&!(e.compareLocationValues(i.getComponentIndex(),i.getVertexIndex(),0)<0);i.next()){var r=i.getSegmentStart();n.add(r),i.isEndOfLine()&&n.endLine()}return e.isVertex()||n.add(e.getCoordinate(this._line)),n.getGeometry()},computeLine:function(t,e){var n=this._line.getCoordinates(),i=new b,r=t.getSegmentIndex();0<t.getSegmentFraction()&&(r+=1);var s=e.getSegmentIndex();1===e.getSegmentFraction()&&(s+=1),s>=n.length&&(s=n.length-1),t.isVertex()||i.add(t.getCoordinate(this._line));for(var o=r;o<=s;o++)i.add(n[o]);e.isVertex()||i.add(e.getCoordinate(this._line)),i.size()<=0&&i.add(t.getCoordinate(this._line));var a=i.toCoordinateArray();return a.length<=1&&(a=[a[0],a[0]]),this._line.getFactory().createLineString(a)},extract:function(t,e){return e.compareTo(t)<0?this.reverse(this.computeLinear(e,t)):this.computeLinear(t,e)},reverse:function(t){return t instanceof Bt?t.reverse():t instanceof wt?t.reverse():(y.shouldNeverReachHere("non-linear geometry encountered"),null)},interfaces_:function(){return[]},getClass:function(){return Qo}}),Qo.extract=function(t,e,n){return new Qo(t).extract(e,n)},e(Jo.prototype,{clampIndex:function(t){var e=t.copy();return e.clamp(this._linearGeom),e},project:function(t){return jo.indexOf(this._linearGeom,t)},checkGeometryType:function(){if(!(this._linearGeom instanceof Bt||this._linearGeom instanceof wt))throw new c("Input geometry must be linear")},extractPoint:function(){if(1===arguments.length)return arguments[0].getCoordinate(this._linearGeom);if(2===arguments.length){var t=arguments[0],e=arguments[1],n=t.toLowest(this._linearGeom);return n.getSegment(this._linearGeom).pointAlongOffset(n.getSegmentFraction(),e)}},isValidIndex:function(t){return t.isValid(this._linearGeom)},getEndIndex:function(){return Ho.getEndLocation(this._linearGeom)},getStartIndex:function(){return new Ho},indexOfAfter:function(t,e){return jo.indexOfAfter(this._linearGeom,t,e)},extractLine:function(t,e){return Qo.extract(this._linearGeom,t,e)},indexOf:function(t){return jo.indexOf(this._linearGeom,t)},indicesOf:function(t){return Ko.indicesOf(this._linearGeom,t)},interfaces_:function(){return[]},getClass:function(){return Jo}}),e($o.prototype,{indexOf:function(t){return this.indexOfFromStart(t,-1)},indexOfFromStart:function(t,e){for(var n=S.MAX_VALUE,i=e,r=0,s=new me,o=new Wo(this._linearGeom);o.hasNext();){if(!o.isEndOfLine()){s.p0=o.getSegmentStart(),s.p1=o.getSegmentEnd();var a=s.distance(t),u=this.segmentNearestMeasure(s,t,r);a<n&&e<u&&(i=u,n=a),r+=s.getLength()}o.next()}return i},indexOfAfter:function(t,e){if(e<0)return this.indexOf(t);var n=this._linearGeom.getLength();if(n<e)return n;var i=this.indexOfFromStart(t,e);return y.isTrue(e<=i,"computed index is before specified minimum index"),i},segmentNearestMeasure:function(t,e,n){var i=t.projectionFactor(e);return i<=0?n:i<=1?n+i*t.getLength():n+t.getLength()},interfaces_:function(){return[]},getClass:function(){return $o}}),$o.indexOf=function(t,e){return new $o(t).indexOf(e)},$o.indexOfAfter=function(t,e,n){return new $o(t).indexOfAfter(e,n)},e(ta.prototype,{getLength:function(t){for(var e=0,n=new Wo(this._linearGeom);n.hasNext();){if(!n.isEndOfLine()){var i=n.getSegmentStart(),r=n.getSegmentEnd().distance(i);if(t.getComponentIndex()===n.getComponentIndex()&&t.getSegmentIndex()===n.getVertexIndex())return e+r*t.getSegmentFraction();e+=r}n.next()}return e},resolveHigher:function(t){if(!t.isEndpoint(this._linearGeom))return t;var e=t.getComponentIndex();if(e>=this._linearGeom.getNumGeometries()-1)return t;for(;++e<this._linearGeom.getNumGeometries()-1&&0===this._linearGeom.getGeometryN(e).getLength(););return new Ho(e,0,0)},getLocation:function(){if(1===arguments.length){var t=arguments[0];return this.getLocation(t,!0)}if(2===arguments.length){var e=arguments[0],n=arguments[1],i=e;if(e<0)i=this._linearGeom.getLength()+e;var r=this.getLocationForward(i);return n?r:this.resolveHigher(r)}},getLocationForward:function(t){if(t<=0)return new Ho;for(var e=0,n=new Wo(this._linearGeom);n.hasNext();){if(n.isEndOfLine()){if(e===t)return new Ho(n.getComponentIndex(),n.getVertexIndex(),0)}else{var i=n.getSegmentStart(),r=n.getSegmentEnd().distance(i);if(t<e+r){var s=(t-e)/r;return new Ho(n.getComponentIndex(),n.getVertexIndex(),s)}e+=r}n.next()}return Ho.getEndLocation(this._linearGeom)},interfaces_:function(){return[]},getClass:function(){return ta}}),ta.getLength=function(t,e){return new ta(t).getLength(e)},ta.getLocation=function(){if(2===arguments.length){var t=arguments[0],e=arguments[1];return new ta(t).getLocation(e)}if(3===arguments.length){var n=arguments[0],i=arguments[1],r=arguments[2];return new ta(n).getLocation(i,r)}},e(ea.prototype,{clampIndex:function(t){var e=this.positiveIndex(t),n=this.getStartIndex();if(e<n)return n;var i=this.getEndIndex();return i<e?i:e},locationOf:function(){if(1===arguments.length){var t=arguments[0];return ta.getLocation(this._linearGeom,t)}if(2===arguments.length){var e=arguments[0],n=arguments[1];return ta.getLocation(this._linearGeom,e,n)}},project:function(t){return $o.indexOf(this._linearGeom,t)},positiveIndex:function(t){return 0<=t?t:this._linearGeom.getLength()+t},extractPoint:function(){if(1===arguments.length){var t=arguments[0];return ta.getLocation(this._linearGeom,t).getCoordinate(this._linearGeom)}if(2===arguments.length){var e=arguments[0],n=arguments[1],i=ta.getLocation(this._linearGeom,e).toLowest(this._linearGeom);return i.getSegment(this._linearGeom).pointAlongOffset(i.getSegmentFraction(),n)}},isValidIndex:function(t){return t>=this.getStartIndex()&&t<=this.getEndIndex()},getEndIndex:function(){return this._linearGeom.getLength()},getStartIndex:function(){return 0},indexOfAfter:function(t,e){return $o.indexOfAfter(this._linearGeom,t,e)},extractLine:function(t,e){new Jo(this._linearGeom);var n=this.clampIndex(t),i=this.clampIndex(e),r=n===i,s=this.locationOf(n,r),o=this.locationOf(i);return Qo.extract(this._linearGeom,s,o)},indexOf:function(t){return $o.indexOf(this._linearGeom,t)},indicesOf:function(t){var e=Ko.indicesOf(this._linearGeom,t);return[ta.getLength(this._linearGeom,e[0]),ta.getLength(this._linearGeom,e[1])]},interfaces_:function(){return[]},getClass:function(){return ea}});var na=Object.freeze({LengthIndexedLine:ea,LengthLocationMap:ta,LinearGeometryBuilder:Zo,LinearIterator:Wo,LinearLocation:Ho,LocationIndexedLine:Jo});function ia(){}e(ia.prototype,{interfaces_:function(){return[]},getClass:function(){return ia}}),ia.union=function(t,e){if(t.isEmpty()||e.isEmpty()){if(t.isEmpty()&&e.isEmpty())return xs.createEmptyResult(xs.UNION,t,e,t.getFactory());if(t.isEmpty())return e.copy();if(e.isEmpty())return t.copy()}return t.checkNotGeometryCollection(t),t.checkNotGeometryCollection(e),vs.overlayOp(t,e,xs.UNION)},e(K.prototype,{equalsTopo:function(t){return!!this.getEnvelopeInternal().equals(t.getEnvelopeInternal())&&ks.relate(this,t).isEquals(this.getDimension(),t.getDimension())},union:function(){if(0===arguments.length)return Ks.union(this);if(1===arguments.length){var t=arguments[0];return ia.union(this,t)}},isValid:function(){return no.isValid(this)},intersection:function(t){if(this.isEmpty()||t.isEmpty())return xs.createEmptyResult(xs.INTERSECTION,this,t,this._factory);if(this.isGeometryCollection()){var e=t;return as.map(this,{interfaces_:function(){return[MapOp]},map:function(t){return t.intersection(e)}})}return this.checkNotGeometryCollection(this),this.checkNotGeometryCollection(t),vs.overlayOp(this,t,xs.INTERSECTION)},covers:function(t){return ks.covers(this,t)},coveredBy:function(t){return ks.covers(t,this)},touches:function(t){return ks.touches(this,t)},intersects:function(t){return ks.intersects(this,t)},within:function(t){return ks.contains(t,this)},overlaps:function(t){return ks.overlaps(this,t)},disjoint:function(t){return ks.disjoint(this,t)},crosses:function(t){return ks.crosses(this,t)},buffer:function(){if(1===arguments.length){var t=arguments[0];return Fr.bufferOp(this,t)}if(2===arguments.length){var e=arguments[0],n=arguments[1];return Fr.bufferOp(this,e,n)}if(3===arguments.length){var i=arguments[0],r=arguments[1],s=arguments[2];return Fr.bufferOp(this,i,r,s)}},convexHull:function(){return new rn(this).getConvexHull()},relate:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];if(1===arguments.length){var i=arguments[0];return ks.relate(this,i)}if(2===arguments.length){var r=arguments[0],s=arguments[1];return ks.relate(this,r).matches(s)}},getCentroid:function(){if(this.isEmpty())return this._factory.createPoint();var t=Je.getCentroid(this);return this.createPointFromInternalCoord(t,this)},getInteriorPoint:function(){if(this.isEmpty())return this._factory.createPoint();var t=null,e=this.getDimension();if(0===e)t=new ln(this).getInteriorPoint();else if(1===e){t=new un(this).getInteriorPoint()}else{t=new on(this).getInteriorPoint()}return this.createPointFromInternalCoord(t,this)},symDifference:function(t){if(this.isEmpty()||t.isEmpty()){if(this.isEmpty()&&t.isEmpty())return xs.createEmptyResult(xs.SYMDIFFERENCE,this,t,this._factory);if(this.isEmpty())return t.copy();if(t.isEmpty())return this.copy()}return this.checkNotGeometryCollection(this),this.checkNotGeometryCollection(t),vs.overlayOp(this,t,xs.SYMDIFFERENCE)},createPointFromInternalCoord:function(t,e){return e.getPrecisionModel().makePrecise(t),e.getFactory().createPoint(t)},toText:function(){return(new de).write(this)},toString:function(){this.toText()},contains:function(t){return ks.contains(this,t)},difference:function(t){return this.isEmpty()?xs.createEmptyResult(xs.DIFFERENCE,this,t,this._factory):t.isEmpty()?this.copy():(this.checkNotGeometryCollection(this),this.checkNotGeometryCollection(t),vs.overlayOp(this,t,xs.DIFFERENCE))},isSimple:function(){return new sr(this).isSimple()},isWithinDistance:function(t,e){return!(e<this.getEnvelopeInternal().distance(t.getEnvelopeInternal()))&&Yr.isWithinDistance(this,t,e)},distance:function(t){return Yr.distance(this,t)},isEquivalentClass:function(t){return this.getClass()===t.getClass()}});t.version="1.6.1 (9be7f16)",t.algorithm=Rn,t.densify=bn,t.dissolve=Bn,t.geom=Ie,t.geomgraph=hi,t.index=Mi,t.io=ki,t.noding=rr,t.operation=ro,t.precision=ao,t.simplify=Eo,t.triangulate=Xo,t.linearref=na,Object.defineProperty(t,"__esModule",{value:!0})});
//# sourceMappingURL=jsts.min.js.map