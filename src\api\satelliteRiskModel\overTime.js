import request from '@/router/axios'

export function fetchPageList(query) {
    return request({
        url: '/soc/overtime/page',
        method: 'get',
        params: query,
    })
}

export function add(data) {
    return request({
        url: '/soc/overtime/add',
        method: 'post',
        data,
    })
}

export function update(data) {
    return request({
        url: '/soc/overtime/update',
        method: 'post',
        data,
    })
}

export function del(id) {
    return request({
        url: '/soc/overtime/del?id=' + id,
        method: 'delete',
    })
}

export function queryTTSTemplateByType(type) {
    return request({
        url: '/soc/tts/template/queryTTSTemplateByType',
        method: 'get',
        params: {
            type,
        },
    })
}
