import axiosApi from '@/router/axios'
import fileDownload from 'js-file-download'

export default {
  // 查询全部工单（erp整合）
  orderAll(params) {
    return axiosApi({
      url: `/erp/custOrder/all`,
      method: 'get',
      params,
    })
  },

  // 查询工单编号（erp整合）
  number() {
    return axiosApi({
      url: `/erp/custOrder/getBizKey`,
      method: 'get',
    })
  },

  // 查询所有项目
  projectAll() {
    return axiosApi({
      url: `/dmerp-base/project/all`,
      method: 'get',
    })
  },

  // 查询所有指派人(整合erp)
  orderAllUser() {
    return axiosApi({
      url: `/admin/user/getAll`,
      method: 'get',
    })
  },
  // 处理角色
  orderAllList() {
    return axiosApi({
      url: `/admin/role/list`,
      method: 'get',
    })
  },

  //获取全部操作人信息
  allUser() {
    return axiosApi({
      url: `/admin/user/getAll`,
      method: 'get',
    })
  },

  // 根据imei-车牌号获取工单基本信息（整合erp）
  getBaseInfo(params) {
    return axiosApi({
      url: `/erp/custOrder/getBaseInfo`,
      method: 'get',
      params,
    })
  },

  // 根据imei-车牌号获取工单基本信息
  upload(params) {
    return axiosApi({
      url: `/dmerp-base/file/upload`,
      method: 'post',
      params,
    })
  },

  //新建客服工单(保存)（整合erp）
  orderCreate(data) {
    return axiosApi({
      url: `/erp/custOrder/create`,
      method: 'post',
      data,
    })
  },

  // 通过id查询工单详情(整合erp)
  orderDetail(id) {
    return axiosApi({
      url: `/erp/custOrder/detail/${id}`,
      method: 'get',
    })
  },

  //编辑客服工单（整合erp）
  orderEdit(data) {
    return axiosApi({
      url: `/erp/custOrder/update`,
      method: 'post',
      data,
    })
  },

  // 删除工单（整合erp）客服工单
  custOrderDelete(id) {
    return axiosApi({
      url: `/erp/custOrder/${id}`,
      method: 'delete',
    })
  },

  //批量删除（整合erp）客服工单
  orderBatchDelete(data) {
    return axiosApi({
      url: `/erp/custOrder/batchDelete`,
      method: 'post',
      data,
    })
  },

  // 处理工单（整合erp）处理-保存
  orderDeal(data) {
    return axiosApi({
      url: `/erp/custOrder/deal`,
      method: 'post',
      data,
    })
  },

  //工单导出
  // orderExport(params, name, closedPage = false) {
  //   return axiosApi({
  //     url: `/dmerp-base/custOrder/export?closedPage=${closedPage}`,
  //     method: 'get',
  //     params,
  //     responseType: 'blob',
  //     timeout: 60 * 60 * 1000,
  //   }).then(res => {
  //     fileDownload(res.data, `${name}.xlsx`);
  //   });
  // },
  orderExport(params, closedPage = false) {
    return axiosApi({
      url: `/dmerp-base/custOrder/export?closedPage=${closedPage}`,
      method: 'get',
      params,
    })
  },

  // 客服工单-- 关闭工单（整合erp）
  closeOrder(data) {
    return axiosApi({
      url: `/erp/custOrder/close`,
      method: 'post',
      data,
    })
  },
  // 获取设备挂靠的项目
  project_device_projectList(imei) {
    return axiosApi({
      url: `/dmerp-base/project-device/projectList`,
      params: { imei },
    })
  },
}
