import request from '@/router/axios'

export function fetchList(query) {
    return request({
        url: '/soc/tcalarmrule/page',
        method: 'get',
        params: query
    })
}

export function fetchListInPlatform(query) {
    return request({
        url: '/platform/tcalarmrule/page',
        method: 'get',
        params: query
    })
}

export function getObj(id) {
    return request({
        url: '/soc/tcalarmrule/' + id,
        method: 'get'
    })
}

export function addObj(obj) {
    return request({
        url: '/soc/tcalarmrule',
        method: 'post',
        data: obj
    })
}

export function putObj(obj) {
    return request({
        url: '/soc/tcalarmrule',
        method: 'put',
        data: obj
    })
}


export function delObj(id) {
    return request({
        url: '/soc/tcalarmrule/' + id,
        method: 'delete'
    })
}

export function getSubListByPtype(key) {
    return request({
        url: '/soc/tcalarmrule/getSubType/' + key,
        method: 'get'
    })
}