import axiosApi from '@/router/axios';
import fileDownload from 'js-file-download';
import QS from 'qs';
//信息录入查询
export function getDeviceInfoAll(data) {
  return axiosApi({
    url: `/dmerp-base/device/getDeviceInfoAll`,
    method: 'post',
    data,
  });
}
//信息录入产品型号
export function getProductType(params) {
  return axiosApi({
    url: `/dmerp-base/device/getProductType`,
    method: 'get',
    params,
  });
}

// 获取接入点
export function getAccessPointAndModel(params) {
  return axiosApi({
    url: `/dmerp-base/device/getProductType`,
    method: 'get',
    params,
  });
}

//信息录入组织名称
export function getOrg(params) {
  return axiosApi({
    url: `/dmerp-base/org/getOrg`,
    method: 'get',
    params,
  });
}
// 根据一级节点查询客户树
export function getChildById(id) {
  return axiosApi({
    url: `/dmerp-base/org/getChildById?parentId=${id}`,
    method: 'get',
  });
}

//808模板下载
export function eight(name) {
  return axiosApi({
    url: `/dmerp-base/device/eight/template`,
    method: 'get',
    responseType: 'blob',
  }).then(res => {
    fileDownload(res.data, `${name}.xlsx`);
  });
}
//808组织名称
export function getOrgTree(data) {
  return axiosApi({
    url: `/dmerp-base/eightapi/getOrgTree`,
    method: 'post',
    data,
  });
}
//808注册
export function sendEightPlatform(data) {
  return axiosApi({
    url: `/dmerp-base/device/sendEightPlatform`,
    method: 'post',
    data,
  });
}
//iot注册
export function iotDeviceRegister(data) {
  return axiosApi({
    url: `/dmerp-base/device/iotDeviceRegister`,
    method: 'post',
    data,
  });
}
//IOT模板下载
export function iot(name) {
  return axiosApi({
    url: `/dmerp-base/device/iot/template`,
    method: 'get',
    responseType: 'blob',
  }).then(res => {
    fileDownload(res.data, `${name}.xlsx`);
  });
}
//查询全部通道配置
export function allWithoutPage(params) {
  return axiosApi({
    url: `/dmerp-base/channelConfig/allWithoutPage`,
    method: 'get',
    params,
  });
}
export function getAllList(data) {
  return axiosApi({
    url: `/dmerp-base/channelConfig/all`,
    method: 'post',
    data,
  });
}
//删除
export function device(id) {
  return axiosApi({
    url: `/dmerp-base/device/${id}`,
    method: 'delete',
  });
}
// 组织更新模板下载
export function downloadTemplate(name) {
  return axiosApi({
    url: `/dmerp-base/device/template`,
    method: 'get',
    responseType: 'blob',
  }).then(res => {
    fileDownload(res.data, `${name}.xlsx`);
  });
}

// 导入进度查询
export function upStatus(params) {
  return axiosApi({
    url: `/dmerp-base/device/importStatus/${params}`,
    method: 'get',
  });
}

// 导入工单校验
export function upValid(data) {
  return axiosApi({
    url: `/dmerp-base/device/batchUpdateOrgValid`,
    method: 'post',
    data,
  });
}

// 运维工单导入
export function upIntroduction(data) {
  return axiosApi({
    url: `/dmerp-base/device/batchUpdateOrg`,
    method: 'post',
    data,
  });
}

// formdata: { file: File }
export function getDeviceListFile(formdata, name) {
  return axiosApi({
    url: `/dmerp-base/device/getDeviceListFile`,
    method: 'post',
    data: formdata,
    responseType: 'blob',
  }).then(res => {
    fileDownload(res.data, `${name}.xlsx`);
  });
}

// /dmerp-base/device/batchRemove
// 批量删除
// 批量删除
export function batchRemove(data) {
  return axiosApi({
    url: `/dmerp-base/device/batchRemove`,
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: QS.stringify(data),
  });
}

export function getDetail(id) {
  // /dmerp-base/device/detail/
  return axiosApi({
    url: `/dmerp-base/device/detail/${id}`,
    method: 'get',
  });
}

// export function exportExcel(params, name) {
//   return axiosApi({
//     url: `/dmerp-base/device/export`,
//     method: 'get',
//     params,
//     responseType: 'blob',
//     timeout: 60 * 60 * 1000,
//   }).then(res => {
//     fileDownload(res.data, `${name}.xlsx`);
//   });
// }
export function exportExcel(params) {
  return axiosApi({
    url: `/dmerp-base/device/export`,
    method: 'get',
    params,
  });
}

export function getBatchTemplate(name, productType) {
  return axiosApi({
    url: `/dmerp-base/device/batch/template`,
    method: 'get',
    responseType: 'blob',
    params: productType,
    timeout: 60 * 60 * 1000,
  }).then(res => {
    if (res.data.code !== 1) {
      fileDownload(res.data, `${name}.xlsx`);
    }
  });
}

export function getAreaBlock() {
  return axiosApi({
    url: `/dmerp-base/device/deviceAreas`,
    method: 'get',
  });
}

export function getVisOrgTree() {
  if (!getVisOrgTree.cache) {
    getVisOrgTree.cache = axiosApi({
      url: `/dmerp-base/openapi/vissOrgTree`,
      method: 'get',
    });
  }
  return getVisOrgTree.cache;
}

// /dmerp-base/device/deviceRegister
//808注册
export function deviceRegister(data, params, hide_error = false) {
  return axiosApi({
    url: `/dmerp-base/device/deviceRegister`,
    method: 'post',
    data,
    params,
    hide_error,
  });
}

// /dmerp-base/car/vmsSync
export function vmsSync(params) {
  return axiosApi({
    url: `/dmerp-base/car/vmsSync?deviceId=${params.deviceId}&vin=${params.vin}`,
    method: 'get',
  });
}

//新增项目挂靠/更新项目挂靠 模板下载
export function projectDependence_template(name) {
  return axiosApi({
    url: `/dmerp-base/device/projectDependence/template`,
    method: 'get',
    responseType: 'blob',
  }).then(res => {
    fileDownload(res.data, `${name}.xlsx`);
  });
}
// 新增项目挂靠 导入
export function addProjectDependence(data, hide_error) {
  return axiosApi({
    url: '/dmerp-base/device/addProjectDependence',
    method: 'post',
    data,
    hide_error,
  });
}
// 更新项目挂靠 导入
export function updateProjectDependence(data, hide_error) {
  return axiosApi({
    url: '/dmerp-base/device/updateProjectDependence',
    method: 'post',
    data,
    hide_error,
  });
}

// 新增车队挂靠/更新车队挂靠 模板下载
export function orgInsuranceDevice_addModelDownLoad(name) {
  return axiosApi({
    url: `/dmerp-base/orgInsuranceDevice/addModelDownLoad`,
    method: 'get',
    responseType: 'blob',
  }).then(res => {
    fileDownload(res.data, `${name}.xlsx`);
  });
}
// 新增车队挂靠 导入
// code 保司组织机构
// operate 0 新增设备挂靠车队  1 覆盖更新设备挂靠车队
export function orgInsuranceDevice_addModelUpLoad(code, operate, data, hide_error) {
  return axiosApi({
    url: `/dmerp-base/orgInsuranceDevice/addModelUpLoad/${code}/${operate}`,
    method: 'post',
    data,
    hide_error,
  });
}

// /dmerp-base/device/update
export function device_update(data) {
  return axiosApi({
    url: `/dmerp-base/device/update`,
    method: 'put',
    data,
  });
}
