import request from '@/router/axios'

export function fetchPageList(query) {
    return request({
        url: '/soc/voice_message/page',
        method: 'get',
        params: query
    })
}

export function getObj(id) {
    return request({
        url: '/soc/voice_message/' + id,
        method: 'get'
    })
}

export function addObj(obj) {
    return request({
        url: '/soc/voice_message/save',
        method: 'post',
        data: obj
    })
}

export function putObj(obj) {
    return request({
        url: '/soc/voice_message/update',
        method: 'put',
        data: obj
    })
}


export function delObj(id) {
    return request({
        url: '/soc/voice_message/remove/' + id,
        method: 'delete'
    })
}

export function getVoiceMessages(obj) {
    return request({
        url: '/soc/voice_message/getVoiceMessages',
        method: 'post',
        data: obj
    })
}

