import request from '@/router/axios';

export function queryPointSta(data) {
  let params = {
    "client": data.client,  // 1微信小程序 2PC   
    "startTime": data.startTime, //开始时间
    "endTime": data.endTime, // 结束时间
    current: data.pageNo, //1 页码
    size: data.pageSize //10 每页数量
  }
  return request({
    url: '/fleet/buriedPoint/pointPage',
    method: 'get',
    params: params,
  })
}
//导出
export function pointExport(query) {
    return request({
        url: '/fleet/buriedPoint/pointExport',
        method: 'get',
        params: query,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}