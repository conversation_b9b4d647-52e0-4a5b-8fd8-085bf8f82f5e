import request from '@/router/axios';

// 查询
export function queryPage(data) {
  return request({
    url: '/fleet/electricFence/queryPage',
    method: 'post',
    data,
  })
}

// 新增
export function createPage(data) {
  return request({
    url: '/fleet/electricFence/create',
    method: 'post',
    data,
  })
}

// 编辑修改
export function updatePage(data) {
  return request({
    url: '/fleet/electricFence/update',
    method: 'post',
    data,
  })
}

// 删除
export function removeElectric(id) {
  return request({
    url: `/fleet/electricFence/remove/${id}`,
    method: 'post',
  });
}

//车牌号查询设备号
export function getDeptById(deptId) {
  return request({
    url: `/admin/dept/getDeptById/${deptId}`,
    method: 'get',
  });
}
