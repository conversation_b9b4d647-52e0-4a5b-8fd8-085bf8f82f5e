import request from '@/router/axios'

// 视频提取查询列表
export function queryVideoExtractionList(data) { 
    return request({
      url: '/fleet/videoExtraction/selectPage',
      method: 'post',
      data
    })
}

// 通过车牌号查询相关信息
export function queryVideoTypeByCarNo(query) { 
    return request({
      url: '/fleet/videoExtraction/queryMsgByCar',
      method: 'get',
      params: query
    })
}

// 新增提取前判断
export function preCreateVideoExtraction(data) { 
    return request({
      url: '/fleet/videoExtraction/preCreate',
      method: 'post',
      data
    })
}

// 新增视频提取
export function createVideoExtraction(data) { 
    return request({
      url: '/fleet/videoExtraction/create',
      method: 'post',
      data
    })
}

// 查询单个提取的视频
export function queryVideoExtractionById(query) { 
    return request({
      url: '/fleet/videoExtraction/selectById',
      method: 'get',
      params: query
    })
}

// 查看私有协议视频
export function getPrivateVideoHttpPath(data) { 
    return request({
      url: '/fleet/videoExtraction/getVideoHttpPath',
      method: 'post',
      data
    })
}

// 下载提取视频
export function downloadPrivateVideo(data) { 
    return request({
      url: '/fleet/videoExtraction/download',
      method: 'post',
      data
    })
}
// 视频时长字典
export function videoDurationDict(query) { 
  return request({
    url: '/admin/dict/type/video_duration',
    method: 'get',
    params: query
  })
}
export function extraCountDict(query) { 
  return request({
    url: '/admin/param/publicValue/EXTRA_COUNT',
    method: 'get',
    params: query
  })
}
// 模糊搜索
export function queryVagueCar(params) {
  return request({
      url: '/erp/vehicle/queryVagueCarInfo',
      method: 'get',
      params,
  });
};
export function downloadUserGuide(params) {
  return request({
      url: '/fleet/videoExtraction/downloadUserGuide',
      method: 'get',
      params,
  });
};