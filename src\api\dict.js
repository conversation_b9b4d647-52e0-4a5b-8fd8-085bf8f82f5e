import request from '@/router/axios';

export function getdictType(query) {
  return request({
    url: '/admin/dict/type/' + query,
    method: 'get',
  });
}

let dictFromUic = new Map();
export function dictType(query) {
  return new Promise(async resolve => {
    if (dictFromUic.get(query)) {
      resolve(dictFromUic.get(query));
    } else {
      await getdictType(query).then(res => {
        if (res && res.data) {
          res.data.data.sort((a, b) => {
            return a.sort - b.sort;
          });
          dictFromUic.set(query, res);
        }
        resolve(res);
      });
    }
  });
}

export function dictTypeBatch(data) {
  let requestPromiseArray = [];
  data.forEach(dict => {
    requestPromiseArray.push(dictType(dict));
  });
  return Promise.all2(requestPromiseArray);
}

Promise.all2 = function (iterators) {
  return new Promise((resolve, reject) => {
    if (!iterators || iterators.length === 0) {
      resolve([]);
    } else {
      // 计算器，用于判断所有任务是否执行完成
      let count = 0;
      // 结果数组
      let result = [];
      // 执行数组长度
      let len = iterators.length;
      for (let i = 0; i < len; i++) {
        // 考虑到iterators[i]可能是普通对象，则统一包装为Promise对象
        Promise.resolve(iterators[i]).then(
          data => {
            // 按顺序保存对应的结果
            result[i] = data;
            // 判断++count 的次数是否等于 传入执行数组的长度
            if (++count === len) {
              resolve(result);
            }
          },
          err => {
            // 任何一个Promise对象执行失败，则调用reject()方法
            reject(err);
          }
        );
      }
    }
  });
};

// order_type，order_problem_part，order_service_type，cust_order_type，cust_order_status，ops_order_status，order_advice，order_solution，cust_order_close_reason，ops_order_close_reason
