<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="referrer" content="no-referrer" />
        <link rel="stylesheet" href="<%= BASE_URL %>cdn/animate/3.5.2/animate.css" />
        <link rel="stylesheet" href="<%= BASE_URL %>cdn/avue/avue.css" />
        <link rel="stylesheet" href="<%= BASE_URL %>cdn/avue/index.css" />

        <link rel="icon" href="" />

        <!-- <script type="text/javascript" src="<%= BASE_URL %>cdn/sfmap/index.js"></script> -->
        <!-- <script type="text/javascript" src="<%= BASE_URL %>cdn/sfmap/draw.min.js"></script> -->

        <script type="text/javascript">
            window._AMapSecurityConfig = {
                serviceHost: '/_AMapService',
            }
        </script>
        <script src="<%= BASE_URL %>cdn/EasyPlayer.js-main\vue-demo\js\EasyPlayer-pro.js"></script>
        <script src="<%= BASE_URL %>config.js"></script>
        <script
            type="text/javascript"
            src="//webapi.amap.com/maps?v=2.0&key=05e92bee728f4d783edd1d559246432e&plugin=AMap.Scale,AMap.ToolBar,AMap.Geolocation,AMap.RangingTool,AMap.MarkerCluster,AMap.GraspRoad,AMap.Driving,AMap.DistrictSearch,AMap.MouseTool,AMap.AutoComplete,AMap.PlaceSearch,AMap.PolyEditor,AMap.RectangleEditor,AMap.CircleEditor,AMap.GeoJSON,AMap.PolygonEditor,&plugin=AMap.Geocoder"
        ></script>
        <title></title>
    </head>
    <!--1.4.15版本的高德地图API  -->
    <body>
        <noscript>
            <strong>很抱歉，如果没有 JavaScript 支持，网站将不能正常工作。请启用浏览器的 JavaScript 然后继续。</strong>
        </noscript>
        <div id="app">
            <div class="avue-home">
                <div class="avue-home__main">
                    <div class="avue-home__title">正在加载资源</div>
                    <div class="avue-home__sub-title">初次加载资源可能需要较多时间 请耐心等待</div>
                </div>
            </div>
        </div>
        <script typet="text/javascript" src="<%= BASE_URL %>cdn/jquery/jquery.min.js"></script>
        <script src="<%= BASE_URL %>cdn/avue/index.js" charset="utf-8"></script>
        <!-- <link rel="stylesheet" href="<%= BASE_URL %>cdn/ol/ol.css" /> -->
        <!-- <script async type="text/javascript" src="<%= BASE_URL %>cdn/ol/ol.js"></script> -->
        <script></script>
    </body>
</html>
