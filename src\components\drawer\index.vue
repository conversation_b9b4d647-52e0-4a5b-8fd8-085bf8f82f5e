<template>
  <div>
    <el-drawer
      direction="rtl"
      :visible.sync="drawVisible"
      :size="size"
      :withHeader="false"
      :class="['drawerWrap', closeAble ? 'showClose' : '']"
      :destroy-on-close="true"
      :before-close="handleDrawClose"
      :show-close="closeAble"
      :wrapperClosable="true"
      :append-to-body="appendToBody">
      <div class="drawInner">
        <component
          :is="dynamicComponent"
          :propObject="propObject"
          @close="handleDrawClose"
          @action="handleAction"
          v-if="dynamicComponent"></component>
      </div>
    </el-drawer>
  </div>
</template>
<script>
/**
 * 使用说明
 * 该组件为过度组件，无私有逻辑
 * 父组件mixins 使用并引入orderDrawer.js import orderDrawerCommon from '@/mixins/orderDrawer'
 * 参数说明 ： currentComponents 父组件需要使用那个组件则在私有方法中赋值currentComponents 为组件路径截取@/views/后面至.vue之前的路径，例如：
 *             @/views/operations/workorderDetails.vue  只要 operations/workorderDetails 即可
 *            propObject:携带的参数，object数据
 *            refreshFlag:刷新方式：1：保留参数刷新，2.重置刷新
 *            parentQueryMethodName ：调用父组件请求数据的方法，如果不传默认为orderAll
 *            parentResetMethodName ：调用父组件重置请求数据的方法，如果不传默认为resetting
 */
export default {
  props: {
    currentComponents: {
      type: String,
      default: '',
    },
    propObject: {
      type: Object,
      default: () => {},
    },
    drawVisible: {
      type: Boolean,
      default: false,
    },
    parentQueryMethodName: {
      type: String,
      default: 'orderAll',
    },
    parentResetMethodName: {
      type: String,
      default: 'resetting',
    },
    closeAble: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: '70%',
    },
    appendToBody: {
      type: Boolean,
      default: false,
    },
  },
  computed: {},
  data() {
    return {
      refreshFlag: '',
      dynamicComponent: null,
    }
  },
  watch: {
    refreshFlag(val, oldVal) {
      /**
       * 1：保留参数局部刷新，编辑，处理页签
       * 2：重置刷新，新增
       */
      if (val === '1') {
        this.$parent[this.parentQueryMethodName]()
        this.refreshFlag = ''
      } else if (val === '2') {
        this.$parent[this.parentResetMethodName]()
        this.refreshFlag = ''
      }
    },
    currentComponents: {
      immediate: true,
      handler(val, oldVal) {
        if (val) {
          //vite
          // const imports = import.meta.glob('../../views/**/?*.vue')
          // this.dynamicComponent = imports[`../../views/${val}.vue`]

          //webpack
          const imports = require.context('../../views', true, /\.vue$/)
          this.dynamicComponent = imports(`./${val}.vue`).default
        } else {
          this.dynamicComponent = null
        }
      },
    },
    // currentComponents(val, oldVal) {
    //   if (val) {
    //     const imports = import.meta.glob('../../views/**/?*.vue');
    //     this.dynamicComponent = imports[`../../views/${val}.vue`];
    //     // this.dynamicComponent = () => import(`@/views/${val}.vue`);
    //   } else {
    //     this.dynamicComponent = null;
    //   }
    // },
  },
  methods: {
    handleDrawClose(data) {
      data = data == '1' || data == '2' ? data : ''
      this.$emit('update:currentComponents', '')
      this.$emit('update:propObject', {})
      this.$emit('update:drawVisible', false)
      this.refreshFlag = data
    },
    handleAction(data) {
      const { queryObject, component, type } = data
      this.$emit('update:currentComponents', component)
      this.$emit('update:propObject', queryObject)
    },
  },
}
</script>
<style lang="scss">
.drawerWrap {
  &.showClose {
    .el-drawer__header {
      position: absolute;
      top: 0;
      right: 0;
      padding: 20px 20px 0;
    }
  }
  .el-drawer__header {
    padding: 0;
    margin: 0;
    height: 0;
  }
}
</style>
