html,
body,
#app {
    height: 100%;
    margin: 0;
    padding: 0;
}

.avue-home {
    background-color: #303133;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.avue-home__main {
    user-select: none;
    width: 100%;
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.avue-home__footer {
    width: 100%;
    flex-grow: 0;
    text-align: center;
    padding: 1em 0;
}

.avue-home__footer > a {
    font-size: 12px;
    color: #ABABAB;
    text-decoration: none;
}

.avue-home__loading {
    height: 32px;
    width: 32px;
    margin-bottom: 20px;
}

.avue-home__title {
    color: #FFF;
    font-size: 14px;
    margin-bottom: 10px;
}

.avue-home__sub-title {
    color: #ABABAB;
    font-size: 12px;
}
.easyplayer-loading {
    display: none !important;
}