import request from '@/router/axios';

// 查询写卡记录
export function queryPage(data) {
  return request({
    url: '/fleet/writeCard/queryPage',
    method: 'post',
    data,
  })
}

//车牌号查询设备号
export function queryImei(query) {
  return request({
    url: '/fleet/writeCard/queryImei',
    method: 'get',
    params: query,
  });
}

// 新增写卡
export function createCard(data) {
  return request({
    url: '/fleet/writeCard/create',
    method: 'post',
    data,
  })
}
