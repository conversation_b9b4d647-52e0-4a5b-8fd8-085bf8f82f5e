import request from '@/router/axios'

//摄像头质量
export function queryCameraQualityList(data) {
    return request({
        url: '/rbi/devicehealth/queryCameraHealth',
        method: 'post',
        data
    })
}
//摄像头质量-导出
export function exportCameraQualityList(data) {
    return request({
        url: '/rbi/devicehealth/exportCameraHealthList',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}


//摄像头遮挡
export function queryCameraBaffleList(data) {
    return request({
        url: '/rbi/devicehealth/queryCameraOcclusionList',
        method: 'post',
        data
    })
}
//摄像头遮挡-导出
export function exportCameraBaffleList(data) {
    return request({
        url: '/rbi/devicehealth/exportCameraOcclusionList',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}


//设备异常
export function queryAbnormalDeviceDetectionList(data) {
    return request({
        url: '/rbi/devicehealth/queryAbnormalDeviceDetectionList',
        method: 'post',
        data
    })
}
//设备异常-导出
export function exportAbnormalDeviceDetectionList(data) {
    return request({
        url: '/rbi/devicehealth/exportAbnormalDeviceDetectionList',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}


//设备总体异常
export function queryDeviceExceptionCollectList(data) {
    return request({
        url: '/rbi/devicehealth/queryEquipmentAbnormalList',
        method: 'post',
        data
    })
}
//设备总体异常-导出
export function exportDeviceExceptionCollectList(data) {
    return request({
        url: '/rbi/devicehealth/exportEquipmentAbnormalList',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}


//报警里程异常
export function queryDeviceMileAbnormalList(data) {
    return request({
        url: '/rbi/devicehealth/queryDeviceMileAbnormalList',
        method: 'post',
        data
    })
}
//报警里程异常-导出
export function exportDeviceMileAbnormalList(data) {
    return request({
        url: '/rbi/devicehealth/exportDeviceMileAbnormalList',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}

//健康度-分项目
export function queryEquipmentCustomerList(data) {
    return request({
        url: '/rbi/devicehealth/queryEquipmentCustomerList',
        method: 'post',
        data
    })
}
//健康度-分项目
export function exportEquipmentCustomerList(data) {
    return request({
        url: '/rbi/devicehealth/exportEquipmentCustomerList',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}


//健康度-分项目-week
export function queryWeekEquipmentCustomerList(data) {
    return request({
        url: '/rbi/devicehealth/queryWeekEquipmentCustomerList',
        method: 'post',
        data
    })
}
//健康度-分项目-week
export function exportWeekEquipmentCustomerList(data) {
    return request({
        url: '/rbi/devicehealth/exportWeekEquipmentCustomerList',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}