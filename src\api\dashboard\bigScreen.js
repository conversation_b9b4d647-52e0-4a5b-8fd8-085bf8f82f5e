/*
 *    Copyright (c) 2018-2025, cloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloud
 */

import request from '@/router/axios'

export function fetchCoreIndicatorData(query) { // 核心指标监控数据
  return request({
    url: '/rbi/bigScreen/coreIndicator',
    method: 'get',
    params: query
  })
}

export function fetchVehicleIndicatorData(query) { // 车辆指标
  return request({
    url: '/rbi/bigScreen/vehicleIndicator',
    method: 'get',
    params: query
  })
}

export function fetchVehicleMeddleAlarmData(query) { // 车辆指标-需干预工单
  return request({
    url: '/rbi/bigScreen/needMeddleAlarms',
    method: 'get',
    params: query
  })
}

export function fetchCurrentWorkOrderData(query) { // 人效指标-实时工单
  return request({
    url: '/rbi/bigScreen/currentWorkOrder',
    method: 'get',
    params: query
  })
}

export function fetchInspectWorkOrderData(query) { // 人效指标-质检工单
  return request({
    url: '/rbi/bigScreen/inspectWorkOrder',
    method: 'get',
    params: query
  })
}
