<template>
  <el-dialog
    :visible="visible"
    width="50%"
    :before-close="handleClose"
    :destroy-on-close="true"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="custom-dialog">
    <div class="header">
      <div class="title_text">{{ `导出数据${title ? '--' + title : ''}` }}</div>
    </div>
    <div class="export-text-box-wrap">
      <div class="export-text-box">
        <p style="color: #333; font-weight: 600">
          请选择导出字段<el-checkbox disabled v-model="checked" style="margin-left: 10px">为必须导出的字段</el-checkbox>
        </p>
      </div>
    </div>
    <div class="export-main">
      <el-checkbox style="margin-left: 10px" :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange"
        >全选
      </el-checkbox>
      <p class="export-main-tips">主表字段</p>
      <el-row :gutter="20" class="export-main-row">
        <el-checkbox-group v-model="checkColumns">
          <el-col :xs="12" :sm="12" :md="12" :lg="8" :xl="6" v-for="(column, index) in columnsKeys" :key="column">
            <el-checkbox :disabled="requiredKeys.indexOf(column) > -1" :label="column"
              ><span style="margin-right: 5px" v-if="requiredKeys.indexOf(column) > -1 || widthStarColumns.indexOf(column) > -1">*</span>
              {{ columnsOptions[index].label }}</el-checkbox
            >
          </el-col>
        </el-checkbox-group>
      </el-row>
    </div>
    <div class="footer">
      <el-button :disabled="exportLoading" @click="handleClose">取消</el-button>
      <el-button :loading="exportLoading" :disabled="exportLoading" @click="handleExport" type="primary">导出</el-button>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/router/axios';
/**
 * 带模板下载的上传弹框
 */
const ignoreColumns = [];
export default {
  name: 'exportDialog',
  props: {
    // 对话框是否展示
    visible: {
      type: Boolean,
      default: false,
    },
    // 标题
    title: {
      type: String,
      default: '',
    },
    // 表格列
    columns: {
      type: Array,
      default: [],
    },
    // 必选表格列
    requiredKeys: {
      type: Array,
      default: () => {
        return ['imei'];
      },
    },
    // 导入状态
    exportLoading: {
      type: Boolean,
      default: false,
    },
    // 不需要导出的列
    ignoreColumns: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 在页面上不展示但是需要导出的列
    defaultExtendColumns: {
      type: Array,
      default: () => {
        return [];
      },
    },
    widthStarColumns: {
      type: Array,
      default: () => {
        return ['projectName', 'customerName1', 'carNo', 'type'];
      },
    },
  },
  data() {
    return {
      checked: true,
      checkAll: false,
      isIndeterminate: true,
      checkColumns: [],
      columnsKeys: [],
      defaultIgnoreColumns: ignoreColumns,
      columnsOptions: [],
    };
  },
  computed: {},
  watch: {
    checkColumns(val) {
      if (val.length === this.columnsKeys.length) {
        this.isIndeterminate = false;
        this.checkAll = true;
      } else {
        this.isIndeterminate = true;
        this.checkAll = false;
      }
    },
  },
  methods: {
    handleClose() {
      // 是否正在导出，如果请求完成则关闭弹窗，如果请求为完成则提示
      if (!this.exportLoading) {
        this.$emit('close');
      } else {
        this.$message.warning('正在导出文件，请等待');
      }
    },
    // 全选与非全选
    handleCheckAllChange(val) {
      if (val) {
        this.checkColumns = this.columnsKeys;
        this.isIndeterminate = false;
      } else {
        this.isIndeterminate = true;
        this.checkColumns = this.requiredKeys;
      }
    },
    // 提交父组件导出方式传递已选中的数据
    handleExport() {
      this.$emit('action', this.checkColumns);
      this.$nextTick(() => {
        this.checkColumns = [].concat(this.requiredKeys);
      });
    },
    // 获取预设的列，进行处理
    getColumns() {
      let ignoreColumns = this.ignoreColumns.concat(this.defaultIgnoreColumns);
      this.checkColumns = this.checkColumns.concat(this.requiredKeys);
      let columns = [...this.columns];
      // sort 将必选项制定， filter过滤忽略的选中项
      this.columnsOptions = columns
        .concat(this.defaultExtendColumns)
        .sort((a, b) => {
          return this.requiredKeys.indexOf(b.prop) - this.requiredKeys.indexOf(a.prop);
        })
        .filter(a => {
          return ignoreColumns.indexOf(a.prop) === -1;
        });
      this.columnsKeys = this.columnsOptions.map(c => {
        return c.prop;
      });
    },
  },
  mounted() {
    this.getColumns();
    this.handleCheckAllChange(true);
  },
};
</script>

<style lang="scss" scoped>
.custom-dialog ::v-deep .el-dialog__body {
  padding: 20px;
  .export-text-box-wrap {
    padding: 10px;
    .export-text-box {
      padding: 10px;
      width: 100%;
      background: #f9fffd;
      border: 1px solid #33e7a2;
      border-radius: 2px;
      p {
        margin: 0;
        margin-bottom: 5px;
        color: #555;
      }
    }
  }

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    padding: 10px;
    border-bottom: 1px solid #d2d2d2;
    margin-bottom: 10px;
    .title_text {
      min-width: 150px;
    }

    &::before {
      content: '';
      width: 3px;
      height: 18px;
      margin-right: 10px;
      background: #72a5ff;
    }
  }
  .export-main {
    .export-main-tips {
      padding: 10px;
      border-bottom: 1px solid #d2d2d2;
    }
    .export-main-row {
      padding: 10px;
    }
  }
  .footer {
    border-top: 1px solid #d2d2d2;
    padding: 10px;
    text-align: right;
  }
}
</style>
