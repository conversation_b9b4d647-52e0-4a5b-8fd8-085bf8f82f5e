import request from '@/router/axios'

export function fetchVirtualWeekMonthTotalityReportList(data) { // 运营报表列表
  let url = '/fleet/xnFleetWeekMonth/queryWeekMonthTotality';
  if (data.reportType === 'D') {
    url = '/fleet/xnFleetDaily/queryDailyTotality';
  }
  return request({
    url,
    method: 'post',
    data
  })
}
export function fetchVirtualMileWeekDayReportList(data) { // 查询客户维度七-30天内每天的总里程及报警数
  return request({
    url: '/fleet/xnFleetWeekMonth/queryMileWeekDay',
    method: 'post',
    data
  })
}
export function fetchVirtualRecent4WeekReportList(data) { // 查询近4周的指标
  return request({
    url: '/fleet/xnFleetWeekMonth/queryRecent4WeekDay',
    method: 'post',
    data
  })
}
export function fetchVirtualDefendCarReportList(data) { // 车辆护航情况分析(TOP15)排名
  let url = '/fleet/xnFleetWeekMonth/queryDefendCar';
  if (data.reportType === 'D') {
    url = '/fleet/xnFleetDaily/queryDefendCar';
  }
  return request({
    url,
    method: 'post',
    data
  })
}
export function fetchVirtualSafeCarTopReportList(data) { // 车辆安全驾驶排名前10
  let url = '/fleet/xnFleetWeekMonth/querySafeCarTop';
  if (data.reportType === 'D') {
    url = '/fleet/xnFleetDaily/querySafeCarTop';
  }
  return request({
    url,
    method: 'post',
    data
  })
}
export function fetchVirtualDriverSafeReportList(data) { // 查询司机风险行为分析
  return request({
    url: '/fleet/xnFleetWeekMonth/queryDriverSafe',
    method: 'post',
    data
  })
}
export function updateVirtualReportDsc(data) { // 更新操作记录
  return request({
    url: '/fleet/xnFleetDaily/updateReportDsc',
    method: 'post',
    data
  })
}
export function fetchVirtualReportDscList(data) { // 查询报表反馈记录
  return request({
    url: '/fleet/xnFleetDaily/queryReportDsc',
    method: 'post',
    data
  })
}

// 字典获取
export function getDictByType(type) {
  return request({
    url: `/admin/dict/type/${type}`,
    method: 'get',
  })
}
