import request from '@/router/axios'

//
export function queryDeviceAlarmTypeByDate(data) {
    return request({
        url: '/rbi/deviceAlarmType/queryDeviceAlarmTypeByDate',
        method: 'post',
        data,
    })
}

//--导出
export function exportDeviceAlarmTypeByDate(data) {
    return request({
        url: '/rbi/deviceAlarmType/exportDeviceAlarmTypeByDate',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}