
import request from '@/router/axios'

export function fetchPageList(query) {
    return request({
        url: '/soc/nightChange/page',
        method: 'get',
        params: query
    })
}

export function add(data) {
    return request({
        url: '/soc/nightChange/add',
        method: 'post',
        data,
    })
}

export function update(data) {
    return request({
        url: '/soc/nightChange/update',
        method: 'post',
        data,
    })
}

export function del(id) {
    return request({
        url: '/soc/nightChange/del?id=' + id,
        method: 'delete'
    })
}

