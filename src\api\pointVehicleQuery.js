import request from '@/router/axios'

//查询所有地区树
export function getAllTree() {
    return request({
        url: '/admin/region/getAllTree',
        method: 'get',
    })
}

//新增定点查询任务
export function add(data) {
    return request({
        url: '/track/trackquery/job/info/add',
        method: 'post',
        data,
    })
}
//计算任务详情结果统计查询
export function getList(query) {
    return request({
        url: '/track/trackquery/job/info/result/combine',
        method: 'get',
        params: query,
    })
}
//计算任务详情结果明细查询
export function getDetail(query) {
    return request({
        url: '/track/trackquery/job/info/result/detail',
        method: 'get',
        params: query,
    })
}

//导出统计
export function exportStat(query) {
    return request({
        url: '/track/trackquery/job/exportStat',
        method: 'get',
        params: query,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}

//导出明细
export function exportDetail(query) {
    return request({
        url: '/track/trackquery/job/exportDetail',
        method: 'get',
        params: query,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}
// 车辆树
export function getVehicleTree(data) {
    return request({
        url: '/erp/vehicle/statusTree',
         method: 'post',
        data,
    })

}
// 天气预警查询
export function getWeathers(type) {
    return request({
        url: `/admin/weather/alarms?type=${type}`,
        method: 'get',
    })
}
// 地图实况天气查询
export function getmapWeather(data) {
    return request({
        url: `/admin/weather/mapWeather`,
        method: 'post',
        data: data,
    })
}
// 城市GEO查询
export function queryGeoJSON(cityCode) {
    return request({
        url: `/admin/areaGeom/queryGeoJSON?cityCode=${cityCode}`,
        method: 'get',
    })
}
// 车辆收藏 1.单个车辆详情
export function getVehicleAndTerminalByCarNo(data) {
    return request({
        url: `/erp/Vehicle/getVehicleAndTerminalByCarNo`,
        method: 'get',
        params: data,
    })
}
// 车辆收藏 1.单个车辆收藏
export function vehicleCollectadd(imei) {
    return request({
        url: `/erp/vehicleCollect/add?imei=${imei}`,
        method: 'post'
    })
}
// 车辆收藏 2.全量车辆收藏
export function addAllVehicleCollect(data) {
    return request({
        url: '/erp/vehicleCollect/addAll',
        method: 'post',
        data
    })
}
// 车辆收藏 3.单个车辆取消收藏
export function removeVehicleCollect(imei) {
    return request({
        url: `/erp/vehicleCollect/remove?imei=${imei}`,
        method: 'post'
    })
}
// 车辆收藏 4.查询用户收藏车辆
export function geVehicleCollect() {
    return request({
        url: '/erp/vehicleCollect/get',
        method: 'get'
    })
}
// 车辆分组 5.添加分组 ==========================================================
export function addGroup(data) {
    return request({
        url: '/erp/vehicleGroup/addGroup',
        method: 'post',
        data
    })
}
// 车辆分组 6.修改分组
export function updateGroup(data) {
    return request({
        url: '/erp/vehicleGroup/updateGroup',
        method: 'post',
        data
    })
}
// 车辆分组 7.全量添加分组车辆
export function addVehicleGroup(data) {
    return request({
        url: '/erp/vehicleGroup/addVehicle',
        method: 'post',
        data
    })
}
// 车辆分组 8.删除分组
export function removeGroup(id) {
    return request({
        url: `/erp/vehicleGroup/remove/${id}`,
        method: 'post',
    })
}
// 车辆分组 9.查询用户分组车辆
export function getGroup() {
    return request({
        url: '/erp/vehicleGroup/list',
        method: 'get'
    })
}
// 车辆列表数据 实时定位车辆列表详细信息查询
export function getstatusDetails(data) {
    return request({
        url: '/erp/vehicle/statusDetails',
        method: 'post',
        data,
    })
}
//导出车辆列表详细
export function exportStatusDetails(data) {
    return request({
        url: '/erp/vehicle/exportStatusDetails',
        method: 'post',
        data,
    })
}
//  poi搜索
export function poiSearch(data) {
    return request({
        url: '/fleet/gis/api/poiSearch',
        method: 'get',
        params: data,
    })
}
// ======================================================================
//省市区三级联动查询接口
export function getRegion(query) {
    return request({
        url: '/admin/region/get',
        method: 'get',
         params: query,
    })
}

//  计算任务空间查询条件
export function getSpatials(query) {
    return request({
        url: '/track/trackquery/job/info/spatials',
        method: 'get',
         params: query,
    })
}

//重点人员
export function getMainPersonFun(data){
    return request({
        url: '/fleet/stressUser/queryStressUserImeiList',
        method: 'post',
        data,
    })
} 