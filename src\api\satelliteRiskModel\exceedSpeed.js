
import request from '@/router/axios'

export function fetchPageList(query) {
    return request({
        url: '/soc/overspeed/page',
        method: 'get',
        params: query
    })
}

export function add(data) {
    return request({
        url: '/soc/overspeed/add',
        method: 'post',
        data,
    })
}

export function update(data) {
    return request({
        url: '/soc/overspeed/update',
        method: 'post',
        data,
    })
}

export function del(id) {
    return request({
        url: '/soc/overspeed/del?id=' + id,
        method: 'delete'
    })
}

