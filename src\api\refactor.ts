import axios from '@/router/axios'

export { axios }

export function obj2fd(obj: Record<string, any>) {
  const fd = new FormData()
  Object.keys(obj).forEach(k => {
    fd.append(k, obj[k])
  })
  return fd
}

export type Res<T> = {
  code: number
  msg?: string
  data: T
}
export type Page<T> = Res<{
  countId?: null
  current: number
  hitCount?: false
  maxLimit?: null
  optimizeCountSql?: boolean
  orders?: []
  pages: number
  records: T[]
  searchCount: boolean
  size: number
  total: number
}>
export type PageParam = { pageCurrent: number; pageSize: number }
export type StrNum = string | number

export namespace admin {
  export namespace dict {
    export type Dict = {
      createTime: string //"2019-05-16 14:20:40"
      delFlag: string //"0"
      description: string //"系统类字典"
      dictId: number //13
      id: number //39
      label: string //"系统类"
      remarks: string //"不能修改删除"
      sort: number //0
      type: string //"dict_type"
      updateTime: string //"2019-05-16 14:20:40"
      value: string //"1"
    }
    export const type_ = (type_: string) => {
      return axios.get<Res<Dict[]>>('/admin/dict/type/' + type_)
    }
  }

  export namespace user {
    export type info_data = {
      sysUser: any
      roles: StrNum[]
      permissions: string[]
    }
    export const info = () => {
      return axios.get<Res<info_data>>('/admin/user/info')
    }
  }

  export namespace tenant {
    // 根据用户名获取租户id
    export function getUserTenant(params: { userName: string }) {
      return axios.get(`/admin/tenant/getUserTenant`, { params })
    }

    export function list() {
      return axios.get<Res<{ id: any; name: string }[]>>(`/admin/tenant/list`)
    }
  }
}

export namespace dmerp_base {
  export namespace openapi {
    export type vissOrgTree_item = {
      entId: string //"2"
      entName: string //"809接入测试"
      parentId?: string //"-1"
      children?: vissOrgTree_item[]
    }
    export const vissOrgTree = () => {
      return axios.get<Res<vissOrgTree_item[]>>(`/dmerp-base/openapi/vissOrgTree`)
    }
  }

  export namespace sysArea {
    // export type allAreaTree_item = {
    //   id: string;
    //   addressName: string;
    //   parentId: string;
    //   subAreas?: allAreaTree_item[];
    // };
    // export const allAreaTree = () => {
    //   return axios.get<Res<allAreaTree_item[]>>(`/dmerp-base/sysArea/allAreaTree`);
    // };
    export type allAreaList_item = {
      id: string
      addressName: string
      parentId: string
    }
    export const allAreaList = () => {
      return axios.get<Res<allAreaList_item[]>>(`/dmerp-base/sysArea/allAreaList`)
    }
  }

  export namespace openAk {
    export type getApiPage_params = { apiName: string }
    export type getApiPage_item = {
      apiName: string //接口名称	string
      apiPath: string //接口url	string
      createTime: string //创建时间	string
      dataScopeCheck: number //是否需要数据范围校验 0-无需，1-需要	integer
      id: number //integer
    }
    export const getApiPage = (params: getApiPage_params & PageParam) => {
      return axios.get<Page<getApiPage_item>>('/dmerp-base/openAk/getApiPage', { params })
    }

    export const getApiList = (params: getApiPage_params) => {
      return axios.get<Res<getApiPage_item[]>>('/dmerp-base/openAk/getApiList', { params })
    }
  }

  export namespace project {
    export type all_item = {
      id: string //"75"
      name: string //"业务平台同步项目"
    }
    export const all = () => {
      return axios.get<Res<all_item[]>>(`/dmerp-base/project/all`)
    }
    // ERP项目名称有权限控制的接口
    export const allByUser = () => {
      return axios.get<Res<all_item[]>>(`/dmerp-base/project/allByUser`)
    }
    // ERP项目名称没有权限控制的接口
    export const getAllProject = () => {
      return axios.get<Res<all_item[]>>(`/dmerp-base/project/getAllProject`)
    }
    // ERP保司权限接口
    export const getOrgInsurance = () => {
      return axios.get(`/dmerp-base/orgInsurance/orgChild?label=0&code=BX`)
    }
  }

  export namespace device {
    export const listProductType = () => {
      return axios.get<Res<string[]>>(`/dmerp-base/device/listProductType`)
    }
    export type getProductType_item = {
      accessPointId: string //"fengtu_iot_access"
      apn: string //"丰图IOT"
      apnCode: number //3
      modelType: number //2 ... 是否支持部标协议 1:支持,2:不支持
      productName: string //"锐承RC105"
      productType: string //"FT_T1"
      protocolType: number //10
      standardProtocol: number //1
      // (List<productAlias>  ProductAliasList   value ="产品名称集合")
      //   (productAlias  value ="产品名称")
      //   (productAliasId  value ="产品名称ID")
      //   (deviceType  value ="产品型号")
    }
    export const getProductType = () => {
      return axios.get<Res<getProductType_item[]>>(`/dmerp-base/device/getProductType`)
    }

    export type getCarColorEnum_item = {
      code: string
      desc: string
    }
    export const getCarColorEnum = () => {
      return axios.get<Res<getCarColorEnum_item[]>>(`/dmerp-base/device/getCarColorEnum`)
    }
  }

  export namespace productAlias {
    type getProductAlias_item = {
      productAlias: any // (productAlias  value ="产品名称")
      productAliasId: any // (productAliasId  value ="产品名称ID")
      deviceType: any // (deviceType  value ="产品型号")
    }
    export const getProductAlias = () => {
      return axios.post<Res<getProductAlias_item[]>>(`/dmerp-base/productAlias/getProductAlias`, {})
    }
  }

  export namespace prjInstallProgress {
    // 获取所有的 BU 列表
    export const selectBu = () => {
      return axios.post<Res<string[]>>(`/dmerp-base/prjInstallProgress/selectBu`, {})
    }
  }

  export namespace deviceRegister {
    export type getAllAccessCode_item = {
      entId: string
      name: string
      accessCode: string
    }
    // 返回一个对象:
    // operatorList(<list>):
    // entId 运营商id
    // name 运营商名称
    // accessCode  运营商编码
    export function getAllAccessCode() {
      return axios.get<Res<getAllAccessCode_item[]>>('/dmerp-base/deviceRegister/getAllAccessCode')
    }
  }

  export namespace org {
    // 根据一级节点查询客户树
    export function getChildById(arg0: { parentId: StrNum }) {
      return axios.get(`/dmerp-base/org/getChildById`, { params: arg0 })
    }
  }
}
