/*
 *    Copyright (c) 2018-2025, cloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloud
 */
import request from '@/router/axios';
import qs from 'qs';

const scope = 'server';

export const loginByUsername = (username, password, code, randomStr) => {
  let grant_type = 'password';
  let dataObj = qs.stringify({ username: username, password: password });

  return request({
    url: '/auth/oauth/token',
    headers: {
      isToken: false,
      'TENANT-ID': '1',
      Authorization: 'Basic Y25wYzplOTQ3NTZiODUxYjYzZmZmZjkzNjc3OWJjYmMzMTVhOA==',
    },
    method: 'post',
    params: { randomStr, code, grant_type },
    data: dataObj,
  });
};

export const refreshToken = refresh_token => {
  const grant_type = 'refresh_token';
  return request({
    url: '/auth/oauth/token',
    headers: {
      isToken: false,
      'TENANT-ID': '1',
      Authorization: 'Basic Y25wYzplOTQ3NTZiODUxYjYzZmZmZjkzNjc3OWJjYmMzMTVhOA==',
    },
    method: 'post',
    params: { refresh_token, grant_type, scope },
  });
};

export const loginByMobile = (mobile, code) => {
  const grant_type = 'mobile';
  return request({
    url: '/auth/mobile/token/sms',
    headers: {
      isToken: false,
      'TENANT-ID': '1',
      Authorization: 'Basic Y25wYzplOTQ3NTZiODUxYjYzZmZmZjkzNjc3OWJjYmMzMTVhOA==',
    },
    method: 'post',
    params: { mobile: 'SMS@' + mobile, code: code, grant_type },
  });
};

export const loginBySocial = (state, code, service) => {
  const grant_type = 'mobile';
  return request({
    url: '/auth/mobile/token/social',
    headers: {
      isToken: false,
      'TENANT-ID': '1',
      Authorization: 'Basic Y25wYzplOTQ3NTZiODUxYjYzZmZmZjkzNjc3OWJjYmMzMTVhOA==',
    },
    method: 'post',
    params: { mobile: state + '@' + code + '^' + service, grant_type },
  });
};

export const getUserInfo = () => {
  return request({
    url: '/admin/user/info',
    method: 'get',
  });
};

export const socGetToken = params => {
  return request({
    url: '/soc/openapi/getToken',
    method: 'get',
    params,
  });
};

export const logout = () => {
  return request({
    url: '/auth/token/logout',
    method: 'delete',
  });
};

export const getTenantByPhone = query => {
  return request({
    url: '/admin/user/getUserTenantByPhone',
    method: 'get',
    params: query,
  });
};
/**
 * 获取监控中心cookie
 * @returns
 */
 export const getMonitorCookieRe = () => {
  return request({
    url: '/dataCenter/openapi/jt808/token',
    // url: '/admin/user/info',
    method: 'get'
  });
};

export function checkTenantFlag(query) {
  return request({
    url: '/admin/tenant/checkTenantFlag',
    method: 'get',
    params: query
  })
}
