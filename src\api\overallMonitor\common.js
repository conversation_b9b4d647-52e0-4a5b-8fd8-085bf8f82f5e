import request from '@/router/axios'

//车牌号懒加载
export function getNextCarNoTreeList(query) {
  return request({
    url: '/ztcommon/getCarByOrg',
    method: 'post',
    params: query,
  })
}

//组织机构及车辆数懒加载
export function getNextOrgCountTreeList(query) {
  return request({
    url: '/ztcommon/getOrgCount',
    method: 'post',
    params: query,
  })
}

//本身组织机构及车辆数懒加载
export function getOwnOrgCountTreeList(query) {
  return request({
    url: '/ztcommon/getOwnOrgCount',
    method: 'post',
    params: query,
  })
}
