import request from '@/router/axios'

// 报警显示实时任务数据指标统计
export function queryAlarmPage(data) {
    return request({
        url: '/fleet/alarmshow/queryAlarmPage',
        method: 'post',
        data,
    })
}

// 报警显示实时任务数据指标统计
export function queryAlarmShow(data) {
    return request({
        url: '/fleet/alarmshow/queryAlarmShow',
        method: 'post',
        data,
    })
}
export function queryAlarmProcessLog(data) {
    return request({
        url: '/soc/alarm/queryAlarmProcessLog',
        method: 'post',
        data,
    })
}
