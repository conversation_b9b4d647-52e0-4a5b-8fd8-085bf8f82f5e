import request from '@/router/axios';

//查询车辆实时位置
export function queryCarCordinateInfo(query) {
    return request({
        url: '/fleet/ztdevicestatus/queryDeviceStatus',
        method: 'post',
        data: query,
    });
}

//查询车辆实时详情
export function queryCarDetailInfo(query) {
    return request({
        url: '/fleet/alarmRecent/queryStatusByCar',
        method: 'get',
        params: query,
    });
}

//查询车辆历史轨迹
export function queryHisTrackByCar(query) {
    return request({
        url: '/fleet/tarckSdk/queryHisTrackByCar',
        method: 'get',
        params: query,
    });
}

//查询车辆报警点list
export function queryAlarmListByCar(query) {
    return request({
        url: '/fleet/alarmRecent/queryAlarmByCar',
        method: 'get',
        params: query,
    });
}

//发送实时对内视频的命令
export function postBehindCamaraAction(params) {
    return request({
        url: '/fleet/video/live',
        method: 'get',
        params,
    });
}

//发送实时对外视频的命令
export function postFrontCamaraAction(params) {
    return request({
        url: '/fleet/deepViewApi/postFrontCamaraAction',
        method: 'get',
        params,
    });
}

// 查询可直播设备编号
export function getCamaraVideo(params) {
    return request({
        url: '/fleet/track/getCamaraVideo',
        method: 'get',
        params,
    });
}

// D1
export function getDyCamaraVideo(params) {
    return request({
        url: '/fleet/deepViewApi/getDyCamaraVideo',
        method: 'get',
        params,
    });
}

// 查询历史轨迹
export function queryHisTrack(data) {
    return request({
        url: '/fleet/szhistrack/queryHisTrack',
        method: 'post',
        data,
    });
}

export function queryAlarmByCar(params) {
    return request({
        url: '/fleet/alarmRecent/queryAlarmByCar',
        method: 'get',
        params,
    });
}

export function querySzStayPoints(data) {
    return request({
        url: '/fleet/szhistrack/querySzStayPoints',
        method: 'post',
        data,
    });
}

export function queryEfenceList() {
    return request({
        url: '/fleet/elec/queryElecFenceByOrg',
        method: 'get',
    });
}
