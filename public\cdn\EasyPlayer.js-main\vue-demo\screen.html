<!DOCTYPE HTML>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>多分屏演示</title>
  <script src="./js/EasyPlayer-pro.js"></script>
  <script src="./2.6.14_vue.min.js"></script>
</head>
<style>
  /* color: #07baf4; */
  * {
    margin: 0;
    padding: 0;
  }

  p {
    line-height: 24px;
  }

  #app {
    padding-top: 10px;
    margin: auto;
    max-width: 1200px;
  }

  .radio-container {
    padding: 10px 0;
  }

  .radio-item {
    cursor: pointer;
    display: inline-block;
    padding: 6px 12px;
    margin-right: 15px;
    border-radius: 4px;
    border: 1px #ccc solid;
  }

  .radio-active {
    color: #fff;
    background-color: #07baf4;
    border-color: #07baf4;
  }

  .player_container {
    display: grid;
  }

  .player_container_1 {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
  }

  .player_container_4 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }

  .player_container_9 {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
  }

  .player_container_16 {
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr 1fr;
  }

  .player_container_25 {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr 1fr 1fr;
  }

  .player_container_36 {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr 1fr 1fr 1fr;
  }

  .player_item {
    position: relative;
    padding-bottom: 56%;
    background-color: #000;
    border: 1px #fff solid;
  }

  .inputs {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    height: 36px;
    line-height: 36px;
    outline: none;
    padding: 0 15px;
    transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
    width: 100%;
    max-width: 600px;
    margin-right: 16px;
  }

  .player_box {
    height: 100%;

    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
  }

  .df {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .df span {
    margin-left: 4px;
  }

  .df form {
    margin-right: 4px;
  }
</style>

<body>
  <div id="app">
    <br>
    <h2>EasyPlayerPro案例演示</h2>
    <br>
    <div class="radio-container">
      <div :class="['radio-item',{'radio-active': radio==item.value}]" v-for="(item,index) in radioList" :key="index"
        @click="onRadio(item.value)">
        {{item.label}}
      </div>

    </div>

    <div :class="['player_container','player_container_'+radio]">
      <div class="player_item" v-for="(item,index) in playerList">
        <div class="player_box" :id="'player_box'+(index+1)">

        </div>
      </div>
    </div>
    <br>
    <div class="df">
      <div>
        <input @click="onUse('hasAudio')" type="checkbox" :checked="config.hasAudio" /><span
          @click="onUse('hasAudio')">音频</span>
      </div>

    </div>
    <div class="df">
      <div>播放地址：</div><input class="inputs" v-model="videoUrl">
    </div>
    <div class="df">
      <div class="radio-item" @click="onReplay()" v-if="isPlay">重播</div>
      <div class="radio-item" @click="onPlayer()" v-if="!isPlay">播放</div>
      <div class="radio-item" @click="onPause()">暂停</div>
      <div class="radio-item" @click="onMute()">静音</div>
      <div class="radio-item" @click="setFullscreen()">全屏</div>
      <div class="radio-item" @click="onStop()" v-if="isPlay">注销</div>
    </div>
  </div>

  <script>
    new Vue({
      el: "#app",
      data() {
        return {
          radio: 1,
          videoUrl: "ws://localhost:6230/ws_flv/live/stream_1_0.flv",
          radioList: [// 选择分屏
            { label: "单分屏", value: 1 },
            { label: "四分屏", value: 4 },
            { label: "九分屏", value: 9 },
          ],
          config: {
            hasAudio: true,
            MSE: false,
            WCS: false
          },
          isPlay: false,
          playerList: []
        }
      },
      mounted() {
        this.onCreate().then(() => {
          this.create()
        });
      },
      methods: {
        onUse(type) {
          if (type == 'hasAudio') {
            this.config.hasAudio = !this.config.hasAudio
          } else {
            this.config.MSE = false
            this.config.WCS = false
            if (type == 'MSE') this.config.MSE = true
            if (type == 'WCS') this.config.WCS = true
          }
          if (this.isPlay) {
            this.onDestroy().then(() => {
              this.onCreate().then(() => {
                this.create()
                this.onPlayer()
              });
            });
          }
        },
        setFullscreen() {
          this.playerList[0].player.setFullscreen(true)
        },
        onPause() {
          this.playerList[0].player.pause()
        },
        onMute() {
          this.playerList[0].player.setMute(true)
        },
        onPlayer() {
          this.isPlay = true
          for (let i = 0; i < this.radio; i++) {
            var id = i + 1;
            let player = this.playerList[i].player;
            if (this.videoUrl) {
              setTimeout((url) => {
                console.log(url);
                player && player.play(url).then(() => {

                }).catch((e) => {
                  console.error(e);
                });
              }, 0, this.videoUrl)
            }
          }
        },
        onStop() {
          this.isPlay = false
          this.onDestroy().then(() => {
            this.create()
          });
        },
        onCreate() {
          let _this = this
          return new Promise((resolve, reject) => {
            this.playerList = []
            for (let index = 0; index < this.radio; index++) {
              this.playerList.push({ index: index + 1 })
            }
            resolve();
          })
        },
        onDestroy() {
          let _this = this
          return new Promise((resolve, reject) => {
            for (let index = 0; index < this.radio; index++) {
              if (this.playerList[index].player) {
                this.playerList[index].player.destroy()
                this.playerList[index].player = null
              }
            }
            setTimeout(() => {
              resolve();
            }, 100);
          })
        },
        onReplay(type) {
          this.onDestroy().then(() => {
            this.onCreate().then(() => {
              this.create()
              this.onPlayer()
            });
          });
        },
        onRadio(val) {// 分屏选择
          this.onDestroy().then(() => {
            this.radio = val
            this.onCreate().then(() => {
              this.create()
            });
          });
        },
        create() {
          for (let i = 0; i < this.radio; i++) {
            this.playCreate(i);
          }
        },
        playCreate(index) {
          console.log('player_box' + this.playerList[index].index);
          var container = document.getElementById('player_box' + this.playerList[index].index);
          var easyplayer = new EasyPlayerPro(container,{
            bufferTime: 0.2, // 缓存时长
            stretch: false,
            MSE: this.config.MSE,
            WCS: this.config.WCS,
            hasAudio: this.config.hasAudio,
            watermark: {text: {content: 'easyplayer-pro'},right: 10,top: 10},
          });

          easyplayer.on("fullscreen", function (flag) {
            console.log('is fullscreen', id, flag)
          })
          this.playerList[index].player = easyplayer
        }
      }
    })
  </script>

</body>

</html>