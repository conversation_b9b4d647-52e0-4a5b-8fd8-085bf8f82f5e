import request from '@/router/axios'

export function queryAlarmRuleList(params) {
    return request({
        url: '/soc/riskmodel/queryList',
        method: 'post',
        data: params
    })
}

export function queryAlarmRulePage(params) {
    return request({
        url: '/soc/riskmodel/queryPageList',
        method: 'post',
        data: params
    })
}

export function saveOrUpdateAlarmRule(params) {
    return request({
        url: '/soc/riskmodel/saveOrUpdate',
        method: 'post',
        data: params
    })
}
export function delAlarmRule(params) {
    return request({
        url: '/soc/riskmodel/del',
        method: 'get',
        params
    })
}