import request from '@/router/axios'

/**
 * 导出 信息
 * @param {Object} params - 请求参数，包含id
 * @returns {Promise} - 响应Promise
 */
export function exportDriver(obj) {
    return request({
        url: '/fleet/stressUser/exportStressUser',
        method: 'post',
        responseType: 'blob',
        data: obj,
    })
}
//导出模板
export function getDownloadFile(params) {
    return request({
        url: 'fleet/attachment/downloadTemplateFile',
        method: 'get',
        responseType: 'blob',
        params,
    })
}

/**
 * 分页查询
 * @param {Object} params - 请求参数
 * @returns {Promise} - 响应Promise
 */
export function getDownloadPage(params) {
    return request({
        url: '/fleet/stressUser/page',
        method: 'get',
        params,
    })
}
//删除
export function delItemObj(id) {
    return request({
        url: '/fleet/stressUser/remove/' + id,
        method: 'delete',
    })
}
//修改
export function putItemObj(obj) {
    return request({
        url: '/fleet/stressUser',
        method: 'put',
        data: obj,
    })
}
export function dictStressType(query) {
    return request({
        url: '/admin/dict/type/stress_type',
        method: 'get',
        params: query,
    })
}
