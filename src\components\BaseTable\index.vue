<template>
    <div class="base-table">
        <el-table
            ref="table"
            v-loading="loading"
            :data="data"
            v-bind="tableProps"
            border
            @selection-change="(val) => $emit('selectionChange', val)"
            @select="(selection, row) => $emit('select', selection, row)"
            @select-all="(selection) => $emit('selectAll', selection)"
        >
            <template v-for="item in columns">
                <el-table-column
                    v-if="item.slotName"
                    :key="item.label"
                    :label="item.label"
                    :min-width="item.width"
                    align="center"
                    header-align="center"
                    v-bind="item"
                >
                    <template slot-scope="scope">
                        <slot :name="item.slotName" :row="scope.row"></slot>
                    </template>
                </el-table-column>
                <el-table-column
                    v-else-if="item.isClickCell"
                    :key="item.label"
                    align="center"
                    header-align="center"
                    v-bind="item"
                >
                    <template slot-scope="scope">
                        <!-- :class="{'table-click-cell': !!scope.row[item.prop] || !!item.cellText }"  || '-' -->
                        <span class="table-click-cell" @click="onAppointCellClick(scope.row, item)">
                            <template v-if="item.formatter">{{ item.formatter(scope.row) }}</template>
                            <template v-else>{{  item.cellText ||  scope.row[item.prop]  }}</template>
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    v-else
                    :key="item.label"
                    :min-width="item.width"
                    align="center"
                    header-align="center"
                    v-bind="item"
                />
            </template>
        </el-table>
        <el-pagination
            v-if="pagination"
            class="pagination"
            background
            v-bind="pagination"
            @size-change="(val) => $emit('sizeChange', val)"
            @current-change="(val) => $emit('currentChange', val)"
        >
        </el-pagination>
    </div>
</template>

<script>
export default {
    name: 'BaseTable',
    props: {
        loading: Boolean,
        tableProps: {
            type: Object,
            default: () => {},
        },
        data: {
            type: Array,
            default: () => [],
        },
        /**
         *  elementui table-column 配置项
         *
         *  扩展字段
         *  slotName 插槽名
         */
        columns: {
            type: Array,
            default: () => [],
        },
        // elementui Pagination 配置项
        pagination: {
            type: Object,
            default: () => {},
        },
    },
    methods: {
         onAppointCellClick(row, item) {
            this.$emit('appointCellClick', row, item);
        },
        clearSelection() {
            this.$refs.table.clearSelection()
        },
        async toggleRowSelection(rows, selected) {
            await this.$nextTick()
            rows.forEach((row) => {
                this.$refs.table.toggleRowSelection(row, selected)
            })
        },
        toggleAllSelection() {
            this.$refs.table.toggleAllSelection()
        },
    },
}
</script>

<style lang="scss" scoped>
.base-table {
    height: calc(100% - 124px);

    ::v-deep .el-table th {
        word-break: break-word;
        color: rgba(0, 0, 0, 0.85);
        background-color: #fafafa;
    }

    .pagination {
        padding-top: 20px;
        text-align: right;
    }
}
</style>
