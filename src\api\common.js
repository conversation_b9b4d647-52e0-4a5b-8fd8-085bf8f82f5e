import axios from 'axios'
import request from '@/router/axios'

// 图片回显
export const queryAnjiImgUrl = '/anjiPost/queryAnjiImg'
export function queryAnjiImg(fileName) {
    return request({
      url: '/fleet/anjiPost/queryAnjiImg',
      method: 'get',
      params: { fileName }
    })
}

// 严重司机报警统计周表-列表
export function queryAlarmBadDriverList(data) {
  return request({
    url: '/fleet/alarmBadDriverSta/queryList',
    method: 'post',
    data
  })
}

// 月度工作台 - 查询每周的试点人员
export function queryExperimentalDriverList(params) {
  return request({
    url: '/fleet/experimental-focus-personnel/queryList',
    method: 'get',
    params
  })
}

const UPLOAD_TIMEOUT = 30*60 * 1000

// 上传文件
export function uploadfile(params) {
    return request({
        url: 'fleet/attachment/upload', // 上传文件接口/fleet/education/uploadfile
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        method: 'post',
        timeout: UPLOAD_TIMEOUT,
        data: params
    })
}

// 获取上传文件
export function queryHWCloudFile(params) {
    return request({
        url: 'fleet/attachment/query',//fleet/education/queryHWCloudFile
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        method: 'get',
        timeout: UPLOAD_TIMEOUT,
        params
    })
}



// args carplate
// {
//   "id": "163774",
//   "imei": "S534100001500",
//   "carplate": "鄂A72E8C",
//   "orgCode": "P027Y",
//   "orgName": "中南仓配中心"
// }
export function device_allBrief(params) {
  return request({
    url: '/fleet/device/allBrief',
    method: 'get',
    params,
  })
}

// args driverName
// {
//   "id": "244918",
//   "driverName": "张荣宽",
//   "orgCode": "HTBD95667",
//   "orgName": "深圳市运达搅拌车运输有限公司"
// }
export function driver_allBrief(params) {
  return request({
    url: '/fleet/driver/allBrief',
    method: 'get',
    params,
  })
}

export { remote as dict_type } from './admin/dict';
