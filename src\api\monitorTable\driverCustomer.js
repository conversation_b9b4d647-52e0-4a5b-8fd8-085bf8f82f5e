import request from '@/router/axios'

//司机维度
export function queryDriverList(data) {
    return request({
        url: '/rbi/driverMotorcad/queryDriver',
        method: 'post',
        data
    })
}
//司机维度-导出
export function exportDriverList(data) {
    return request({
        url: '/rbi/driverMotorcad/exportDriver',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}

//车队维度
export function queryMotorcadList(data) {
    return request({
        url: '/rbi/driverMotorcad/queryMotorcad',
        method: 'post',
        data
    })
}
//车队维度-导出
export function exportMotorcadList(data) {
    return request({
        url: '/rbi/driverMotorcad/exportMotorcad',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}


//司机触达
export function queryDriverReachList(data) {
    return request({
        url: '/rbi/driverMotorcad/queryMotorcad',
        method: 'post',
        data
    })
}
//司机触达-导出
export function exportDriverReachList(data) {
    return request({
        url: '/rbi/driverMotorcad/exportMotorcad',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}