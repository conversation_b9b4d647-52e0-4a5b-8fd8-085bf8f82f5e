import store from '@/store'
import { getStore, setStore, getStoreSession } from '@/util/store';
/**
 * 单点登录
 */

const reload = (href) => {
    window.location.href = href
    //window.open(href)
}

const init = (callback) => {
    if (window.location.href.indexOf('no_cas=1') > -1) {
        callback && callback() 
        return 
    }
    if (window._CONFIG.useCas) {
        console.log("-------单点登录开始-------");
        //let token = Vue.ls.get(ACCESS_TOKEN);
        let token = getStoreSession({ name: 'access_token' })
        let st = getUrlParam("ticket");
        let ph = getUrlParam("phone");
        let redirect = location.hash ? '/' + location.hash : location.pathname;
        let service = location.protocol + "//" + window.location.host + `${redirect}`;
        if (token) {
            loginSuccess(callback);
        } else {
            if (st) {
                validateSt(st, ph, service, callback);
            } else {
                let serviceUrl = encodeURIComponent(service);
                window.location.href = window._CONFIG['casPrefixUrl'] + "/login?service=" + serviceUrl;
            }
        }
        console.log("-------单点登录结束-------");
    } else {
        callback && callback()
    }
};
const SSO = {
    init: init
};

function getUrlParam(paraName, href) {
    let url = href || document.location.toString();
    let arrObj = url.split("?");

    if (arrObj.length > 1) {
        let arrPara = arrObj[1].split("&");
        let arr;

        for (let i = 0; i < arrPara.length; i++) {
            arr = arrPara[i].split("=");

            if (arr != null && arr[0] == paraName) {
                return arr[1];
            }
        }
        return "";
    }
    else {
        return "";
    }
}

function validateSt(ticket, phone, service, callback) {
    const hasTicket = (url) => {
        const p1 = url.indexOf('?ticket')
        const p2 = url.indexOf('&ticket')
        return p1 > -1 ? p1 : p2
    }
    let params = {
        ticket: ticket,
        phone: phone,
        service: getUrlParam('ticket', service) ?
            service.substring(0, hasTicket(service)) : service,
        state: 'CAS',
    };
    store.dispatch('ValidateLogin', params).then(res => {
        //this.departConfirm(res)
        if (res.success) {
            if (getUrlParam("ticket")) {
                reload(params.service)
                loginSuccess(callback);
                return
            }
            loginSuccess(callback);
        } else {
            let service = "http://" + window.location.host + "/";
            let serviceUrl = encodeURIComponent(service);
            window.location.href = window._CONFIG['casPrefixUrl'] + "/login?service=" + serviceUrl;
        }
    }).catch((err) => {
        console.log(err);
        //that.requestFailed(err);
        // 重新登录
        var sevice = "http://" + window.location.host + "/";
        var serviceUrl = encodeURIComponent(sevice);
        window.location.href = window._CONFIG['casPrefixUrl'] + "/logout?service=" + serviceUrl;
    });
}

function loginSuccess(callback) {
    callback();
}
export default SSO;
