<template>
  <video ref="video_ele" style="height: 100%; width: 100%; object-fit: contain;"></video>
</template>

<script lang="ts">
import { ref, shallowRef, computed, watch, getCurrentInstance, onMounted, onBeforeUnmount } from 'vue';
import flvjs from 'flv.js';
import DPlayer from 'dplayer';

import { useInterval, useTimeout } from '@/refs';

export default {
  props: {
    init_src: {
      type: String,
      required: true,
    },
  },
  emits: {
    create_player() {},
    decode_frame() {},
  },
  setup(props, ctx) {
    const { init_src } = props;
    const video_ele = shallowRef<HTMLVideoElement>();

    const fp = shallowRef<flvjs.Player>();

    const decode_frame_interval = useInterval();

    onMounted(() => {
      create_player();
      onBeforeUnmount(() => {
        destroy_player();
      });
    });

    return { video_ele };

    function create_player() {

      destroy_player();
      const flvjs_player = flvjs.createPlayer({ type: 'flv', url: init_src, isLive: true }, { enableStashBuffer: false });
      fp.value = flvjs_player;
      flvjs_player.attachMediaElement(video_ele.value!);
      flvjs_player.load();

      ctx.emit('create_player');

      let [decode_frame_a, decode_frame_b] = [0, 0];
      // 不能用 flvjs_player.on(flvjs.Events.STATISTICS_INFO ， 因为它会在初始 设置 b=undefined，然后如果之后出错，根本不会 emit(STATISTICS_INFO), 那就永远都 a!==b 了
      // 其实也可以设置 b=e.decodedFrames || 0;
      // flvjs_player.on(flvjs.Events.STATISTICS_INFO, e => {
      //   // if (!!e.decodedFrames) loading.value = false;
      //   // console.log(decode_frame_a, decode_frame_b, e)
      //   decode_frame_a = decode_frame_b;
      //   decode_frame_b = e.decodedFrames;
      // });
      decode_frame_interval.set(() => {
        decode_frame_a = decode_frame_b;
        decode_frame_b = flvjs_player.statisticsInfo.decodedFrames || 0;
        if (decode_frame_a !== decode_frame_b) {
          ctx.emit('decode_frame');
          return;
        }
        create_player();
      }, 5e3);
    }

    function destroy_player() {
      let flvjs_player = fp.value;
      if (flvjs_player) {
        flvjs_player.unload();
        flvjs_player.detachMediaElement();
        flvjs_player.destroy();
      }
    }
  },
};
</script>
