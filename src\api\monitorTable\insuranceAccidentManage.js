import request from '@/router/axios'

//保险事故报表
export function queryGuaranteeCustomerByDate(data) {
    return request({
        url: '/rbi/guaranteeFact/queryGuaranteeCustomerByDate',
        method: 'post',
        data,
    })
}

//保险事故报表--导出
export function exportGuaranteeCustomerByDate(data) {
    return request({
        url: '/rbi/guaranteeFact/exportGuaranteeCustomerByDate',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}

//事故预测报表
export function queryDetectCustomerByDate(data) {
    return request({
        url: '/rbi/guaranteeFact/queryDetectCustomerByDate',
        method: 'post',
        data,
    })
}