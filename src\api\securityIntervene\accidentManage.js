import request from '@/router/axios'

//事故管理--分页查询
export function queryAccidentList(obj) {
    return request({
        url: '/soc/accident/info/page',
        method: 'get',
        params: obj
    })
}

//事故管理--详情查询
export function queryAccidentDetail(obj) {
    return request({
        url: '/fleet/accident/info/detail',
        method: 'get',
        params: obj
    })
}

//事故管理--新增
export function addAccidentItem(data) {
    return request({
        url: '/soc/accident/info/add',
        method: 'post',
        data
    })
}

//事故管理--编辑
export function editAccidentItem(data) {
    return request({
        url: '/soc/accident/info/update',
        method: 'post',
        data
    })
}

//事故管理--删除
export function delAccidentItem(id) {
    return request({
        url: '/soc/accident/info/del/' + id,
        method: 'delete'
    })
}

// 查询事故时间前30分钟轨迹和报警
export function queryAccidentTrack30Min(obj) {
    return request({
        url: '/fleet/accident/getTrackAlarm',
        method: 'get',
        params: obj
    })
}

//通过车牌获取设备、客户信息
export function queryDeviceInfoByCarno(obj) {
    return request({
        url: '/soc/device/getByCarNo',
        method: 'get',
        params: obj
    })
}

// 视频调取-在线播放
export function queryAccidentVideoPlay(data) {
    return request({
        url: '/soc/accident/videoPlay',
        method: 'post',
        data
    })
}
// 原始视频工单提取
export function queryAccidentVideoOrder(data) {
    return request({
        url: '/soc/accident/videoOrder',
        method: 'post',
        data
    })
}

// 事故导出
export function exportAccident(data) {
    return request({
        url: '/soc/accident/export',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}

// 删除事故已上传视频
export function delUploadVideo(data) {
    return request({
        url: '/soc/accident/delUpload',
        method: 'post',
        data
    })
}

const UPLOAD_TIMEOUT = 2 * 60 * 1000;

// 上传视频
export function importVideo(query, formData) {
    const { cameraId, accidentId, imei } = query;
    return request({
        url: `/rbi/accident/videoUpload?accidentId=${accidentId}&cameraId=${cameraId}&imei=${imei}`,
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        method: 'post',
        timeout: UPLOAD_TIMEOUT,
        data: formData
    })
}
