import request from '@/router/axios'

// 升级护航
export function upgradeDefend(data) {
  return request({
    data,
    method: 'post',
    url: '/platform/defend/upgradeDefend',
  })
}

// 开始接单
export function receiveOrder(data) {
  return request({
    data,
    method: 'post',
    url: '/platform/defend/receiveOrder',
  })
}
// 请求护航心跳
export function defendHeartbeat(params) {
  return request({
    params,
    method: 'get',
    url: '/platform/defend/defendHeartbeat',
  })
}
// 暂停/恢复接单
export function suspendOrder(params) {
  return request({
    params,
    method: 'get',
    url: '/platform/defend/suspendOrder',
  })
}

// 实时修改页面数据
export function realtimeOrder(params) {
  return request({
    params,
    method: 'get',
    url: '/platform/defend/realtimeOrder',
  })
}

// 查询分配的单列表
export function queryOrderList(data) {
  return request({
    data,
    method: 'post',
    url: '/platform/defend/queryOrderList',
  })
}

// 结束工作
export function endOrder(data) {
  return request({
    data,
    method: 'post',
    url: '/platform/defend/endOrder',
  })
}

// 护航结束
export function endDefend(params) {
  return request({
    params,
    method: 'get',
    url: '/platform/defend/endDefend',
  })
}

export function endDefendNew(params) {
  return request({
    data: params,
    method: 'post',
    url: '/platform/defend/endDefendNew',
  })
}

// 按车辆查询30分钟前的报警记录
export function queryAlarmBeforeTimePage(data) {
  return request({
    data,
    method: 'post',
    url: '/platform/defend/queryAlarmBeforeTimePage',
  })
}

// 按设备号查询实时位置
export function queryStatusByImei(params) {
  return request({
    params,
    method: 'get',
    url: '/platform/defend/queryStatusByImei',
  })
}

// 直播
export function postBehindCamaraAction(params) {
  return request({
    params,
    method: 'get',
    // url: '/platform/defend/postBehindCamaraAction',
    url: '/fleet/video/live',
  })
}

// 下发语音
export function sendTTSMessage(data) {
  return request({
    data,
    method: 'post',
    url: '/platform/alarm/sendTTSMessage',
  })
}

// 按设备号查询停车点
export function querySzStayPoints(data) {
  return request({
    data,
    method: 'post',
    url: '/platform/defend/querySzStayPoints',
  })
}

// 按报警类型查询报警
export function queryAlarmByType(data) {
  return request({
    data,
    method: 'post',
    url: '/platform/defend/queryAlarmByType',
  })
}

// 统计今日护航车辆数
export function countDefendCar() {
  return request({
    data: {},
    method: 'post',
    url: '/platform/defend/countDefendCar',
  })
}

// 查询车管信息列表
export function queryManagerList(params) {
  return request({
    params,
    method: 'get',
    url: '/platform/defend/queryManagerList',
  })
}

// 查询报警三方接口相关信息
export function getAlarmAllInfo(params) {
  return request({
    params,
    method: 'get',
    url: '/platform/defend/getAlarmAllInfo',
  })
}

// 根据坐标+半径查询附近服务区
export function queryServiceArea(params) {
  return request({
    params,
    method: 'get',
    url: '/platform/defend/queryServiceArea',
  })
}

// 查询置顶报警消息
export function queryAlarmTop(params) {
  return request({
    params,
    method: 'get',
    url: '/platform/defend/queryAlarmTop',
  })
}

// 地图关键字模糊搜索
export function queryMap(params) {
  return request({
    params,
    method: 'get',
    url: '/platform/alarm/map/query',
  })
}

// 护航备注，单独添加
export function defendRemark(data) {
  return request({
    data,
    method: 'post',
    url: '/platform/defend/remark',
  })
}

// 保持对讲记录操作
/* 
"opType":2, -- 操作类型：1干预 2护航 
"connectFlag":1,  --1点击发起对讲 2接通 3未接通
*/
export function saveIntercomLog(data) {
  return request({
    data,
    method: 'post',
    url: '/platform/alarm/saveIntercomLog',
  })
}

export const IntercomLogOptType = {
  INTERVENE: 1,
  DEFEND: 2,
}

export const IntercomLogConnect = {
  START: 1,
  CONNECT: 2,
  UNCONNECT: 3,
}
//长期未活跃提醒
export function defendRemind() {
  return request({
    method: 'get',
    url: '/platform/defend/defendRemind',
  })
}
// 查询设备通道列表
export function queryChannelList(params) {
  return request({
    params,
    method: 'get',
    url: ' erp/Terminal/channel/' + params.imei,
  })
}


//根据报警大小类进行TTS模板筛选
export function getVoiceMessages(data) {
    return request({
        url: '/soc/voice_message/getVoiceMessages',
        method: 'post',
        data
    })
}

export default {
  upgradeDefend, // 升级护航
  receiveOrder, // 开始接单
  suspendOrder, // 暂停接单
  realtimeOrder, // 实时修改页面数据
  queryOrderList, // 查询分配的单列表
  endOrder, // 结束工作
  endDefend, // 护航结束
  queryAlarmBeforeTimePage, // 按车辆查询30分钟前的报警记录
  queryStatusByImei, // 按设备号查询实时位置
  postBehindCamaraAction, // 直播
  sendTTSMessage, // 下发语音
  querySzStayPoints, // 按设备号查询停车点
  queryAlarmByType, // 按报警类型查询报警
  countDefendCar, // 统计今日护航车辆数
  queryManagerList, // 查询车管信息列表
  getAlarmAllInfo, // 查询报警三方接口相关信息
  queryServiceArea, // 根据坐标+半径查询附近服务区
  defendRemind, // 长期未活跃提醒
  queryChannelList, // 查询设备通道列表
getVoiceMessages,//根据报警大小类进行TTS模板筛选
}
