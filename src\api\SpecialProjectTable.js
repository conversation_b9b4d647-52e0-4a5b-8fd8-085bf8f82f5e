// import request from '@/router/axios'

// // 人效--总体情况
// export function queryTotalyAuditor(query) {
//     return request({
//         url: '/rbi/auditor/queryTotalyAuditor',
//         method: 'get',
//         params: query
//     })
// }
// // 人效--总体情况--导出
// export function exportTotalyAuditor(data) {
//     return request({
//         url: '/rbi/auditor/exportTotalyAuditor',
//         method: 'post',
//         data,
//         timeout: 1000000,
//         responseType: 'arraybuffer'
//     })
// }

// // 人效--每人当天处理情况
// export function queryDayAuditor(query) {
//     return request({
//         url: '/rbi/auditor/queryDayAuditor',
//         method: 'get',
//         params: query
//     })
// }
// // 人效--每人当天处理情况--导出
// export function exportDayAuditor(data) {
//     return request({
//         url: '/rbi/auditor/exportDayAuditor',
//         method: 'post',
//         data,
//         timeout: 1000000,
//         responseType: 'arraybuffer'
//     })
// }

// // 人效--每小时工作负荷
// export function queryHourAuditor(query) {
//     return request({
//         url: '/rbi/auditor/queryHourAuditor',
//         method: 'get',
//         params: query
//     })
// }
// // 人效--每小时工作负荷--导出
// export function exportHourAuditor(data) {
//     return request({
//         url: '/rbi/auditor/exportHourAuditor',
//         method: 'post',
//         data,
//         timeout: 1000000,
//         responseType: 'arraybuffer'
//     })
// }



// //护航日报表
// export function queryDefendFactList(query) {
//     return request({
//         url: '/rbi/defendFact/selectDefendFact',
//         method: 'get',
//         params: query
//     })
// }

// //护航日报表--导出
// export function exportDefendFactList(data) {
//     return request({
//         url: '/rbi/defendFact/exportSelectDefendFact',
//         method: 'post',
//         data,
//         timeout: 1000000,
//         responseType: 'arraybuffer'
//     })
// }









// //摄像头质量-导出
// export function exportCameraQualityList(id) {
//     return request({
//         url: '/soc/tcalarmrule/' + id,
//         method: 'get'
//     })
// }


// //摄像头遮挡
// export function queryCameraBaffleList(query) {
//     return request({
//         url: '/rbi/devicehealth/queryCameraOcclusionList',
//         method: 'get',
//         params: query
//     })
// }
// //摄像头遮挡-导出
// export function exportCameraBaffleList(id) {
//     return request({
//         url: '/soc/tcalarmrule/' + id,
//         method: 'get'
//     })
// }


// //设备异常
// export function queryAbnormalDeviceDetectionList(query) {
//     return request({
//         url: '/rbi/devicehealth/queryAbnormalDeviceDetectionList',
//         method: 'get',
//         params: query
//     })
// }
// //设备异常-导出
// export function exportAbnormalDeviceDetectionList(id) {
//     return request({
//         url: '/soc/tcalarmrule/' + id,
//         method: 'get'
//     })
// }


// //设备总体异常
// export function queryDeviceExceptionCollectList(query) {
//     return request({
//         url: '/rbi/devicehealth/queryEquipmentAbnormalList',
//         method: 'get',
//         params: query
//     })
// }
// //设备总体异常-导出
// export function exportDeviceExceptionCollectList(id) {
//     return request({
//         url: '/soc/tcalarmrule/' + id,
//         method: 'get'
//     })
// }