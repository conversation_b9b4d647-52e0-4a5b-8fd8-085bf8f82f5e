import request from '@/router/axios'

// 查询运营记录管理
export function queryDeviceRecordPage(params) {
    return request({
        url: '/rbi/deviceRecord/page',
        method: 'Post',
        data: params
    })
}

export function exportDeviceRecord(data) {
    return request({
        url: '/rbi/deviceRecord/export',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}