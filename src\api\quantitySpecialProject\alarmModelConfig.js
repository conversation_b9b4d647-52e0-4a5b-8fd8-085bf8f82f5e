import request from '@/router/axios'

// 分页查询
export function fetchPageList(params) {
    return request({
        url: '/qm/ruleSceneConfig/page',
        method: 'get',
        params,
    })
}

export function add(params) {
    return request({
        url: '/qm/ruleSceneConfig/add',
        method: 'post',
        data: params,
    })
}

export function update(params) {
    return request({
        url: '/qm/ruleSceneConfig/update',
        method: 'post',
        data: params,
    })
}

export function deleteRule(ruleId) {
    return request({
        url: '/qm/ruleSceneConfig/' + ruleId,
        method: 'delete',
    })
}

// 详情查询
export function queryDetail(ruleId) {
    return request({
        url: `/qm/ruleSceneConfig/${ruleId}`,
        methods: 'get',
    })
}

// 报警类型
export function fetchListInPlatform(query) {
    return request({
        url: '/soc/tcalarmrule/list',
        method: 'get',
        params: query,
    })
}

export function exportInspectList(data) {
    return request({
        url: '/soc/inspection/exportOrder',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer',
    })
}
