!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):((t=t||self).SFMap=t.SFMap||{},t.SFMap.DrawControl=e())}(this,(function(){"use strict";var t=function(t,e){var n={drag:[],click:[],mousemove:[],mousedown:[],mouseup:[],mouseout:[],keydown:[],keyup:[],touchstart:[],touchmove:[],touchend:[],tap:[]},o={on:function(t,e,o){if(void 0===n[t])throw new Error("Invalid event type: "+t);n[t].push({selector:e,fn:o})},render:function(t){e.store.featureChanged(t)}},r=function(t,r){for(var i=n[t],a=i.length;a--;){var s=i[a];if(s.selector(r)){s.fn.call(o,r)||e.store.render(),e.ui.updateMapClasses();break}}};return t.start.call(o),{render:t.render,stop:function(){t.stop&&t.stop()},trash:function(){t.trash&&(t.trash(),e.store.render())},combineFeatures:function(){t.combineFeatures&&t.combineFeatures()},uncombineFeatures:function(){t.uncombineFeatures&&t.uncombineFeatures()},drag:function(t){r("drag",t)},click:function(t){r("click",t)},mousemove:function(t){r("mousemove",t)},mousedown:function(t){r("mousedown",t)},mouseup:function(t){r("mouseup",t)},mouseout:function(t){r("mouseout",t)},keydown:function(t){r("keydown",t)},keyup:function(t){r("keyup",t)},touchstart:function(t){r("touchstart",t)},touchmove:function(t){r("touchmove",t)},touchend:function(t){r("touchend",t)},tap:function(t){r("tap",t)}}},e=6378137;function n(t){var e=0;if(t&&t.length>0){e+=Math.abs(o(t[0]));for(var n=1;n<t.length;n++)e-=Math.abs(o(t[n]))}return e}function o(t){var n,o,i,a,s,c,u=0,l=t.length;if(l>2){for(c=0;c<l;c++)c===l-2?(i=l-2,a=l-1,s=0):c===l-1?(i=l-1,a=0,s=1):(i=c,a=c+1,s=c+2),n=t[i],o=t[a],u+=(r(t[s][0])-r(n[0]))*Math.sin(r(o[1]));u=u*e*e/2}return u}function r(t){return t*Math.PI/180}var i={geometry:function t(e){var o,r=0;switch(e.type){case"Polygon":return n(e.coordinates);case"MultiPolygon":for(o=0;o<e.coordinates.length;o++)r+=n(e.coordinates[o]);return r;case"Point":case"MultiPoint":case"LineString":case"MultiLineString":return 0;case"GeometryCollection":for(o=0;o<e.geometries.length;o++)r+=t(e.geometries[o]);return r}},ring:o},a="mapboxgl-ctrl",s="mapbox-gl-draw_ctrl-draw-btn",c="mapbox-gl-draw_line",u="mapbox-gl-draw_polygon",l="mapbox-gl-draw_point",d="mapbox-gl-draw_trash",p="mapbox-gl-draw_combine",f="mapbox-gl-draw_uncombine",h="mapboxgl-ctrl-group",g="active",m="mapbox-gl-draw_boxselect",y="mapbox-gl-draw-hot",v="mapbox-gl-draw-cold",b="add",_="move",w="drag",x="pointer",C="none",M={POLYGON:"polygon",LINE:"line_string",POINT:"point"},S="Feature",E="Polygon",P="LineString",L="Point",I="FeatureCollection",k="Multi",F="MultiPoint",T="MultiLineString",O="MultiPolygon",A={DRAW_LINE_STRING:"draw_line_string",DRAW_POLYGON:"draw_polygon",DRAW_POINT:"draw_point",DRAW_RECTANGLE:"draw_rectangle",DRAW_FREEHANDPOLYGON:"draw_freehandpolygon",DRAW_CIRCLE:"draw_circle",DRAG_ELLIPSE:"drag_ellipse",DRAG_CIRCLE:"drag_circle",SIMPLE_SELECT:"simple_select",DIRECT_SELECT:"direct_select",STATIC:"static"},j="draw.create",D="draw.delete",N="draw.update",R="draw.selectionchange",U="draw.modechange",V="draw.actionable",G="draw.render",B="draw.combine",z="draw.uncombine",J="move",W="change_coordinates",q="feature",Z="midpoint",$="vertex",Y="true",K="false",H=["scrollZoom","boxZoom","dragRotate","dragPan","keyboard","doubleClickZoom","touchZoomRotate"],Q={Point:0,LineString:1,Polygon:2};function X(t,e){var n=Q[t.geometry.type]-Q[e.geometry.type];return 0===n&&t.geometry.type===E?t.area-e.area:n}function tt(t){if(this._items={},this._nums={},this._length=t?t.length:0,t)for(var e=0,n=t.length;e<n;e++)this.add(t[e]),void 0!==t[e]&&("string"==typeof t[e]?this._items[t[e]]=e:this._nums[t[e]]=e)}tt.prototype.add=function(t){return this.has(t)||(this._length++,"string"==typeof t?this._items[t]=this._length:this._nums[t]=this._length),this},tt.prototype.delete=function(t){return!1===this.has(t)||(this._length--,delete this._items[t],delete this._nums[t]),this},tt.prototype.has=function(t){return("string"==typeof t||"number"==typeof t)&&(void 0!==this._items[t]||void 0!==this._nums[t])},tt.prototype.values=function(){var t=this,e=[];return Object.keys(this._items).forEach((function(n){e.push({k:n,v:t._items[n]})})),Object.keys(this._nums).forEach((function(n){e.push({k:JSON.parse(n),v:t._nums[n]})})),e.sort((function(t,e){return t.v-e.v})).map((function(t){return t.k}))},tt.prototype.clear=function(){return this._length=0,this._items={},this._nums={},this};var et=[q,Z,$],nt={click:function(t,e,n){return ot(t,e,n,n.options.clickBuffer)},touch:function(t,e,n){return ot(t,e,n,n.options.touchBuffer)}};function ot(t,e,n,o){if(null===n.map)return[];var r=t?function(t,e){return void 0===e&&(e=0),[[t.point.x-e,t.point.y-e],[t.point.x+e,t.point.y+e]]}(t,o):e,a={};n.options.styles&&(a.layers=n.options.styles.map((function(t){return t.id})));var s=n.map.queryRenderedFeatures(r,a).filter((function(t){return-1!==et.indexOf(t.properties.meta)})),c=new tt,u=[];return s.forEach((function(t){var e=t.properties.id;c.has(e)||(c.add(e),u.push(t))})),function(t){return t.map((function(t){return t.geometry.type===E&&(t.area=i.geometry({type:S,property:{},geometry:t.geometry})),t})).sort(X).map((function(t){return delete t.area,t}))}(u)}function rt(t,e){var n=nt.click(t,null,e),o={mouse:C};return n[0]&&(o.mouse=n[0].properties.active===Y?_:x,o.feature=n[0].properties.meta),-1!==e.events.currentModeName().indexOf("draw")&&(o.mouse=b),e.ui.queueMapClasses(o),e.ui.updateMapClasses(),n[0]}function it(t,e){var n=t.x-e.x,o=t.y-e.y;return Math.sqrt(n*n+o*o)}function at(t,e,n){void 0===n&&(n={});var o=null!=n.fineTolerance?n.fineTolerance:4,r=null!=n.grossTolerance?n.grossTolerance:12,i=null!=n.interval?n.interval:500;t.point=t.point||e.point,t.time=t.time||e.time;var a=it(t.point,e.point);return a<o||a<r&&e.time-t.time<i}function st(t,e,n){void 0===n&&(n={});var o=null!=n.tolerance?n.tolerance:25,r=null!=n.interval?n.interval:250;return t.point=t.point||e.point,t.time=t.time||e.time,it(t.point,e.point)<o&&e.time-t.time<r}function ct(t,e){return t(e={exports:{}},e.exports),e.exports}var ut=ct((function(t){var e=t.exports=function(t,n){if(n||(n=16),void 0===t&&(t=128),t<=0)return"0";for(var o=Math.log(Math.pow(2,t))/Math.log(n),r=2;o===1/0;r*=2)o=Math.log(Math.pow(2,t/r))/Math.log(n)*r;var i=o-Math.floor(o),a="";for(r=0;r<Math.floor(o);r++){a=Math.floor(Math.random()*n).toString(n)+a}if(i){var s=Math.pow(n,i);a=Math.floor(Math.random()*s).toString(n)+a}var c=parseInt(a,n);return c!==1/0&&c>=Math.pow(2,t)?e(t,n):a};e.rack=function(t,n,o){var r=function(r){var a=0;do{if(a++>10){if(!o)throw new Error("too many ID collisions, use more bits");t+=o}var s=e(t,n)}while(Object.hasOwnProperty.call(i,s));return i[s]=r,s},i=r.hats={};return r.get=function(t){return r.hats[t]},r.set=function(t,e){return r.hats[t]=e,r},r.bits=t||128,r.base=n||16,r}})),lt=function(t,e){this.ctx=t,this.properties=e.properties||{},this.coordinates=e.geometry.coordinates,this.id=e.id||ut(),this.type=e.geometry.type};lt.prototype.changed=function(){this.ctx.store.featureChanged(this.id)},lt.prototype.incomingCoords=function(t){this.setCoordinates(t)},lt.prototype.setCoordinates=function(t){this.coordinates=t,this.changed()},lt.prototype.getCoordinates=function(){return JSON.parse(JSON.stringify(this.coordinates))},lt.prototype.setProperty=function(t,e){this.properties[t]=e},lt.prototype.toGeoJSON=function(){return JSON.parse(JSON.stringify({id:this.id,type:S,properties:this.properties,geometry:{coordinates:this.getCoordinates(),type:this.type}}))},lt.prototype.internal=function(t){var e={id:this.id,meta:q,"meta:type":this.type,active:K,mode:t};if(this.ctx.options.userProperties)for(var n in this.properties)e["user_"+n]=this.properties[n];return{type:S,properties:e,geometry:{coordinates:this.getCoordinates(),type:this.type}}};var dt=function(t,e){lt.call(this,t,e)};(dt.prototype=Object.create(lt.prototype)).isValid=function(){return"number"==typeof this.coordinates[0]&&"number"==typeof this.coordinates[1]},dt.prototype.updateCoordinate=function(t,e,n){this.coordinates=3===arguments.length?[e,n]:[t,e],this.changed()},dt.prototype.getCoordinate=function(){return this.getCoordinates()};var pt=function(t,e){lt.call(this,t,e)};(pt.prototype=Object.create(lt.prototype)).isValid=function(){return this.coordinates.length>1},pt.prototype.addCoordinate=function(t,e,n){this.changed();var o=parseInt(t,10);this.coordinates.splice(o,0,[e,n])},pt.prototype.getCoordinate=function(t){var e=parseInt(t,10);return JSON.parse(JSON.stringify(this.coordinates[e]))},pt.prototype.removeCoordinate=function(t){this.changed(),this.coordinates.splice(parseInt(t,10),1)},pt.prototype.updateCoordinate=function(t,e,n){var o=parseInt(t,10);this.coordinates[o]=[e,n],this.changed()};var ft=function(t,e){lt.call(this,t,e),this.coordinates=this.coordinates.map((function(t){return t.slice(0,-1)}))};(ft.prototype=Object.create(lt.prototype)).isValid=function(){return 0!==this.coordinates.length&&this.coordinates.every((function(t){return t.length>2}))},ft.prototype.incomingCoords=function(t){this.coordinates=t.map((function(t){return t.slice(0,-1)})),this.changed()},ft.prototype.setCoordinates=function(t){this.coordinates=t,this.changed()},ft.prototype.addCoordinate=function(t,e,n){this.changed();var o=t.split(".").map((function(t){return parseInt(t,10)}));this.coordinates[o[0]].splice(o[1],0,[e,n])},ft.prototype.removeCoordinate=function(t){this.changed();var e=t.split(".").map((function(t){return parseInt(t,10)})),n=this.coordinates[e[0]];n&&(n.splice(e[1],1),n.length<3&&this.coordinates.splice(e[0],1))},ft.prototype.getCoordinate=function(t){var e=t.split(".").map((function(t){return parseInt(t,10)})),n=this.coordinates[e[0]];return JSON.parse(JSON.stringify(n[e[1]]))},ft.prototype.getCoordinates=function(){return this.coordinates.map((function(t){return t.concat([t[0]])}))},ft.prototype.updateCoordinate=function(t,e,n){this.changed();var o=t.split("."),r=parseInt(o[0],10),i=parseInt(o[1],10);void 0===this.coordinates[r]&&(this.coordinates[r]=[]),this.coordinates[r][i]=[e,n]};var ht={MultiPoint:dt,MultiLineString:pt,MultiPolygon:ft},gt=function(t,e,n,o,r){var i=n.split("."),a=parseInt(i[0],10),s=i[1]?i.slice(1).join("."):null;return t[a][e](s,o,r)},mt=function(t,e){if(lt.call(this,t,e),delete this.coordinates,this.model=ht[e.geometry.type],void 0===this.model)throw new TypeError(e.geometry.type+" is not a valid type");this.features=this._coordinatesToFeatures(e.geometry.coordinates)};function yt(t){this.map=t.map,this.drawConfig=JSON.parse(JSON.stringify(t.options||{})),this._ctx=t}(mt.prototype=Object.create(lt.prototype))._coordinatesToFeatures=function(t){var e=this,n=this.model.bind(this);return t.map((function(t){return new n(e.ctx,{id:ut(),type:S,properties:{},geometry:{coordinates:t,type:e.type.replace("Multi","")}})}))},mt.prototype.isValid=function(){return this.features.every((function(t){return t.isValid()}))},mt.prototype.setCoordinates=function(t){this.features=this._coordinatesToFeatures(t),this.changed()},mt.prototype.getCoordinate=function(t){return gt(this.features,"getCoordinate",t)},mt.prototype.getCoordinates=function(){return JSON.parse(JSON.stringify(this.features.map((function(t){return t.type===E?t.getCoordinates():t.coordinates}))))},mt.prototype.updateCoordinate=function(t,e,n){gt(this.features,"updateCoordinate",t,e,n),this.changed()},mt.prototype.addCoordinate=function(t,e,n){gt(this.features,"addCoordinate",t,e,n),this.changed()},mt.prototype.removeCoordinate=function(t){gt(this.features,"removeCoordinate",t),this.changed()},mt.prototype.getFeatures=function(){return this.features},yt.prototype.setSelected=function(t){return this._ctx.store.setSelected(t)},yt.prototype.setSelectedCoordinates=function(t){var e=this;this._ctx.store.setSelectedCoordinates(t),t.reduce((function(t,n){return void 0===t[n.feature_id]&&(t[n.feature_id]=!0,e._ctx.store.get(n.feature_id).changed()),t}),{})},yt.prototype.getSelected=function(){return this._ctx.store.getSelected()},yt.prototype.getSelectedIds=function(){return this._ctx.store.getSelectedIds()},yt.prototype.isSelected=function(t){return this._ctx.store.isSelected(t)},yt.prototype.getFeature=function(t){return this._ctx.store.get(t)},yt.prototype.select=function(t){return this._ctx.store.select(t)},yt.prototype.deselect=function(t){return this._ctx.store.deselect(t)},yt.prototype.deleteFeature=function(t,e){return void 0===e&&(e={}),this._ctx.store.delete(t,e)},yt.prototype.addFeature=function(t){return this._ctx.store.add(t)},yt.prototype.clearSelectedFeatures=function(){return this._ctx.store.clearSelected()},yt.prototype.clearSelectedCoordinates=function(){return this._ctx.store.clearSelectedCoordinates()},yt.prototype.setActionableState=function(t){void 0===t&&(t={});var e={trash:t.trash||!1,combineFeatures:t.combineFeatures||!1,uncombineFeatures:t.uncombineFeatures||!1};return this._ctx.events.actionable(e)},yt.prototype.changeMode=function(t,e,n){return void 0===e&&(e={}),void 0===n&&(n={}),this._ctx.events.changeMode(t,e,n)},yt.prototype.updateUIClasses=function(t){return this._ctx.ui.queueMapClasses(t)},yt.prototype.activateUIButton=function(t){return this._ctx.ui.setActiveButton(t)},yt.prototype.featuresAt=function(t,e,n){if(void 0===n&&(n="click"),"click"!==n&&"touch"!==n)throw new Error("invalid buffer type");return nt[n](t,e,this._ctx)},yt.prototype.newFeature=function(t){var e=t.geometry.type;return e===L?new dt(this._ctx,t):e===P?new pt(this._ctx,t):e===E?new ft(this._ctx,t):new mt(this._ctx,t)},yt.prototype.isInstanceOf=function(t,e){if(t===L)return e instanceof dt;if(t===P)return e instanceof pt;if(t===E)return e instanceof ft;if("MultiFeature"===t)return e instanceof mt;throw new Error("Unknown feature class: "+t)},yt.prototype.doRender=function(t){return this._ctx.store.featureChanged(t)},yt.prototype.onSetup=function(){},yt.prototype.onDrag=function(){},yt.prototype.onClick=function(){},yt.prototype.onMouseMove=function(){},yt.prototype.onMouseDown=function(){},yt.prototype.onMouseUp=function(){},yt.prototype.onMouseOut=function(){},yt.prototype.onKeyUp=function(){},yt.prototype.onKeyDown=function(){},yt.prototype.onTouchStart=function(){},yt.prototype.onTouchMove=function(){},yt.prototype.onTouchEnd=function(){},yt.prototype.onTap=function(){},yt.prototype.onStop=function(){},yt.prototype.onTrash=function(){},yt.prototype.onCombineFeature=function(){},yt.prototype.onUncombineFeature=function(){},yt.prototype.toDisplayFeatures=function(){throw new Error("You must overwrite toDisplayFeatures")};var vt={drag:"onDrag",click:"onClick",mousemove:"onMouseMove",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseout:"onMouseOut",keyup:"onKeyUp",keydown:"onKeyDown",touchstart:"onTouchStart",touchmove:"onTouchMove",touchend:"onTouchEnd",tap:"onTap"},bt=Object.keys(vt);function _t(t){var e=Object.keys(t);return function(n,o){void 0===o&&(o={});var r={},i=e.reduce((function(e,n){return e[n]=t[n],e}),new yt(n));return{start:function(){var e=this;r=i.onSetup(o),bt.forEach((function(n){var o,a=vt[n],s=function(){return!1};t[a]&&(s=function(){return!0}),e.on(n,s,(o=a,function(t){return i[o](r,t)}))}))},stop:function(){i.onStop(r)},trash:function(){i.onTrash(r)},combineFeatures:function(){i.onCombineFeatures(r)},uncombineFeatures:function(){i.onUncombineFeatures(r)},render:function(t,e){i.toDisplayFeatures(r,t,e)}}}}var wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xt=window.device,Ct={},Mt=[];window.device=Ct;var St=window.document.documentElement,Et=window.navigator.userAgent.toLowerCase(),Pt=["googletv","viera","smarttv","internet.tv","netcast","nettv","appletv","boxee","kylo","roku","dlnadoc","pov_tv","hbbtv","ce-html"];function Lt(t,e){return-1!==t.indexOf(e)}function It(t){return Lt(Et,t)}function kt(t){return St.className.match(new RegExp(t,"i"))}function Ft(t){var e=null;kt(t)||(e=St.className.replace(/^\s+|\s+$/g,""),St.className=e+" "+t)}function Tt(t){kt(t)&&(St.className=St.className.replace(" "+t,""))}function Ot(){Ct.landscape()?(Tt("portrait"),Ft("landscape"),At("landscape")):(Tt("landscape"),Ft("portrait"),At("portrait")),Nt()}function At(t){for(var e=0;e<Mt.length;e++)Mt[e](t)}Ct.macos=function(){return It("mac")},Ct.ios=function(){return Ct.iphone()||Ct.ipod()||Ct.ipad()},Ct.iphone=function(){return!Ct.windows()&&It("iphone")},Ct.ipod=function(){return It("ipod")},Ct.ipad=function(){var t="MacIntel"===navigator.platform&&navigator.maxTouchPoints>1;return It("ipad")||t},Ct.android=function(){return!Ct.windows()&&It("android")},Ct.androidPhone=function(){return Ct.android()&&It("mobile")},Ct.androidTablet=function(){return Ct.android()&&!It("mobile")},Ct.blackberry=function(){return It("blackberry")||It("bb10")},Ct.blackberryPhone=function(){return Ct.blackberry()&&!It("tablet")},Ct.blackberryTablet=function(){return Ct.blackberry()&&It("tablet")},Ct.windows=function(){return It("windows")},Ct.windowsPhone=function(){return Ct.windows()&&It("phone")},Ct.windowsTablet=function(){return Ct.windows()&&It("touch")&&!Ct.windowsPhone()},Ct.fxos=function(){return(It("(mobile")||It("(tablet"))&&It(" rv:")},Ct.fxosPhone=function(){return Ct.fxos()&&It("mobile")},Ct.fxosTablet=function(){return Ct.fxos()&&It("tablet")},Ct.meego=function(){return It("meego")},Ct.cordova=function(){return window.cordova&&"file:"===location.protocol},Ct.nodeWebkit=function(){return"object"===wt(window.process)},Ct.mobile=function(){return Ct.androidPhone()||Ct.iphone()||Ct.ipod()||Ct.windowsPhone()||Ct.blackberryPhone()||Ct.fxosPhone()||Ct.meego()},Ct.tablet=function(){return Ct.ipad()||Ct.androidTablet()||Ct.blackberryTablet()||Ct.windowsTablet()||Ct.fxosTablet()},Ct.desktop=function(){return!Ct.tablet()&&!Ct.mobile()},Ct.television=function(){for(var t=0;t<Pt.length;){if(It(Pt[t]))return!0;t++}return!1},Ct.portrait=function(){return screen.orientation&&Object.prototype.hasOwnProperty.call(window,"onorientationchange")?Lt(screen.orientation.type,"portrait"):Ct.ios()&&Object.prototype.hasOwnProperty.call(window,"orientation")?90!==Math.abs(window.orientation):window.innerHeight/window.innerWidth>1},Ct.landscape=function(){return screen.orientation&&Object.prototype.hasOwnProperty.call(window,"onorientationchange")?Lt(screen.orientation.type,"landscape"):Ct.ios()&&Object.prototype.hasOwnProperty.call(window,"orientation")?90===Math.abs(window.orientation):window.innerHeight/window.innerWidth<1},Ct.noConflict=function(){return window.device=xt,this},Ct.ios()?Ct.ipad()?Ft("ios ipad tablet"):Ct.iphone()?Ft("ios iphone mobile"):Ct.ipod()&&Ft("ios ipod mobile"):Ct.macos()?Ft("macos desktop"):Ct.android()?Ct.androidTablet()?Ft("android tablet"):Ft("android mobile"):Ct.blackberry()?Ct.blackberryTablet()?Ft("blackberry tablet"):Ft("blackberry mobile"):Ct.windows()?Ct.windowsTablet()?Ft("windows tablet"):Ct.windowsPhone()?Ft("windows mobile"):Ft("windows desktop"):Ct.fxos()?Ct.fxosTablet()?Ft("fxos tablet"):Ft("fxos mobile"):Ct.meego()?Ft("meego mobile"):Ct.nodeWebkit()?Ft("node-webkit"):Ct.television()?Ft("television"):Ct.desktop()&&Ft("desktop"),Ct.cordova()&&Ft("cordova"),Ct.onChangeOrientation=function(t){"function"==typeof t&&Mt.push(t)};var jt="resize";function Dt(t){for(var e=0;e<t.length;e++)if(Ct[t[e]]())return t[e];return"unknown"}function Nt(){Ct.orientation=Dt(["portrait","landscape"])}function Rt(t){return[].concat(t).filter((function(t){return void 0!==t}))}function Ut(){var t=this;if(!(t.ctx.map&&void 0!==t.ctx.map.getSource(y)))return c();var e=t.ctx.events.currentModeName();t.ctx.ui.queueMapClasses({mode:e});var n=[],o=[];t.isDirty?o=t.getAllIds():(n=t.getChangedIds().filter((function(e){return void 0!==t.get(e)})),o=t.sources.hot.filter((function(e){return e.properties.id&&-1===n.indexOf(e.properties.id)&&void 0!==t.get(e.properties.id)})).map((function(t){return t.properties.id}))),t.sources.hot=[];var r=t.sources.cold.length;t.sources.cold=t.isDirty?[]:t.sources.cold.filter((function(t){var e=t.properties.id||t.properties.parent;return-1===n.indexOf(e)}));var i=r!==t.sources.cold.length||o.length>0;function a(n,o){var r=t.get(n).internal(e);t.ctx.events.currentModeRender(r,(function(e){t.sources[o].push(e)}))}if(n.forEach((function(t){return a(t,"hot")})),o.forEach((function(t){return a(t,"cold")})),i&&t.ctx.map.getSource(v).setData({type:I,features:t.sources.cold}),t.ctx.map.getSource(y).setData({type:I,features:t.sources.hot}),t._emitSelectionChange&&(t.ctx.map.fire(R,{features:t.getSelected().map((function(t){return t.toGeoJSON()})),points:t.getSelectedCoordinates().map((function(t){return{type:S,properties:{},geometry:{type:L,coordinates:t.coordinates}}}))}),t._emitSelectionChange=!1,t.ctx._selectionChangeCallback&&t.ctx._selectionChangeCallback(t.getSelected())),t._deletedFeaturesToEmit.length){var s=t._deletedFeaturesToEmit.map((function(t){return t.toGeoJSON()}));t._deletedFeaturesToEmit=[],t.ctx.map.fire(D,{features:s}),t.ctx._deleteFeatureCallback&&t.ctx._deleteFeatureCallback(s.map((function(t){return t.id})))}function c(){t.isDirty=!1,t.clearChangedIds()}c(),t.ctx.map.fire(G,{})}function Vt(t){var e,n=this;this._features={},this._featureIds=new tt,this._selectedFeatureIds=new tt,this._selectedCoordinates=[],this._changedFeatureIds=new tt,this._deletedFeaturesToEmit=[],this._emitSelectionChange=!1,this._mapInitialConfig={},this.ctx=t,this.sources={hot:[],cold:[]},this.render=function(){e||(e=requestAnimationFrame((function(){e=null,Ut.call(n)})))},this.isDirty=!1}function Gt(t,e){var n=t._selectedCoordinates.filter((function(e){return t._selectedFeatureIds.has(e.feature_id)}));t._selectedCoordinates.length===n.length||e.silent||(t._emitSelectionChange=!0),t._selectedCoordinates=n}Object.prototype.hasOwnProperty.call(window,"onorientationchange")&&(jt="orientationchange"),window.addEventListener?window.addEventListener(jt,Ot,!1):window.attachEvent?window.attachEvent(jt,Ot):window[jt]=Ot,Ot(),Ct.type=Dt(["mobile","tablet","desktop"]),Ct.os=Dt(["ios","iphone","ipad","ipod","android","blackberry","macos","windows","fxos","meego","television"]),Nt(),Vt.prototype.createRenderBatch=function(){var t=this,e=this.render,n=0;return this.render=function(){n++},function(){t.render=e,n>0&&t.render()}},Vt.prototype.setDirty=function(){return this.isDirty=!0,this},Vt.prototype.featureChanged=function(t){return this._changedFeatureIds.add(t),this},Vt.prototype.getChangedIds=function(){return this._changedFeatureIds.values()},Vt.prototype.clearChangedIds=function(){return this._changedFeatureIds.clear(),this},Vt.prototype.getAllIds=function(){return this._featureIds.values()},Vt.prototype.add=function(t){return this.featureChanged(t.id),this._features[t.id]=t,this._featureIds.add(t.id),this},Vt.prototype.delete=function(t,e){var n=this;return void 0===e&&(e={}),Rt(t).forEach((function(t){n._featureIds.has(t)&&(n._featureIds.delete(t),n._selectedFeatureIds.delete(t),e.silent||-1===n._deletedFeaturesToEmit.indexOf(n._features[t])&&n._deletedFeaturesToEmit.push(n._features[t]),delete n._features[t],n.isDirty=!0)})),Gt(this,e),this},Vt.prototype.get=function(t){return this._features[t]},Vt.prototype.getAll=function(){var t=this;return Object.keys(this._features).map((function(e){return t._features[e]}))},Vt.prototype.select=function(t,e){var n=this;return void 0===e&&(e={}),Rt(t).forEach((function(t){n._selectedFeatureIds.has(t)||(n._selectedFeatureIds.add(t),n._changedFeatureIds.add(t),e.silent||(n._emitSelectionChange=!0))})),this},Vt.prototype.deselect=function(t,e){var n=this;return void 0===e&&(e={}),Rt(t).forEach((function(t){n._selectedFeatureIds.has(t)&&(n._selectedFeatureIds.delete(t),n._changedFeatureIds.add(t),e.silent||(n._emitSelectionChange=!0))})),Gt(this,e),this},Vt.prototype.clearSelected=function(t){return void 0===t&&(t={}),this.deselect(this._selectedFeatureIds.values(),{silent:t.silent}),this},Vt.prototype.setSelected=function(t,e){var n=this;return void 0===e&&(e={}),t=Rt(t),this.deselect(this._selectedFeatureIds.values().filter((function(e){return-1===t.indexOf(e)})),{silent:e.silent}),this.select(t.filter((function(t){return!n._selectedFeatureIds.has(t)})),{silent:e.silent}),this},Vt.prototype.setSelectedCoordinates=function(t){return this._selectedCoordinates=t,this._emitSelectionChange=!0,this},Vt.prototype.clearSelectedCoordinates=function(){return this._selectedCoordinates=[],this._emitSelectionChange=!0,this},Vt.prototype.getSelectedIds=function(){return this._selectedFeatureIds.values()},Vt.prototype.getSelected=function(){var t=this;return this._selectedFeatureIds.values().map((function(e){return t.get(e)}))},Vt.prototype.getSelectedCoordinates=function(){var t=this;return this._selectedCoordinates.map((function(e){return{coordinates:t.get(e.feature_id).getCoordinate(e.coord_path)}}))},Vt.prototype.isSelected=function(t){return this._selectedFeatureIds.has(t)},Vt.prototype.setFeatureProperty=function(t,e,n){this.get(t).setProperty(e,n),this.featureChanged(t)},Vt.prototype.storeMapConfig=function(){var t=this;H.forEach((function(e){t.ctx.map[e]&&(t._mapInitialConfig[e]=t.ctx.map[e].isEnabled())}))},Vt.prototype.restoreMapConfig=function(){var t=this;Object.keys(this._mapInitialConfig).forEach((function(e){t._mapInitialConfig[e]?t.ctx.map[e].enable():t.ctx.map[e].disable()}))},Vt.prototype.getInitialConfigValue=function(t){return void 0===this._mapInitialConfig[t]||this._mapInitialConfig[t]};var Bt=function(){for(var t=arguments,e={},n=0;n<arguments.length;n++){var o=t[n];for(var r in o)zt.call(o,r)&&(e[r]=o[r])}return e},zt=Object.prototype.hasOwnProperty;var Jt=["mode","feature","mouse"];var Wt={map:null,draw:null,options:{html:'<p style="margin: 0;">绘制提示</p>',className:"draw-tip-popup"}};function qt(){Ht()}function Zt(){Ht()}var $t=null,Yt={removeTip:Ht};function Kt(t){void 0===t&&(t=data);var e=t.id,n=t.lngLat;Wt.map&&(Yt.id=e,null==$t?($t=new SFMap.Popup(Object.assign({},{closeOnClick:!1,closeButton:!1},Wt.options)).setLngLat(n).setHTML(Wt.options.html).addTo(Wt.map),"function"==typeof Wt.options.onAdd&&(Yt.id=e,Yt.popup=$t,Wt.options.onAdd(Yt))):$t.setLngLat(n))}function Ht(){null!=$t&&("function"==typeof Wt.options.beforeRemove&&Wt.options.beforeRemove(Yt),$t.remove(),$t=null),Yt.id=null,Yt.popup=null}function Qt(e){var n=null,o=null,r={onRemove:function(){return e.map.off("load",r.connect),clearInterval(o),r.removeLayers(),e.store.restoreMapConfig(),e.ui.removeButtons(),e.events.removeEventListeners(),e.ui.clearMapClasses(),e.map=null,e.container=null,e.store=null,n&&n.parentNode&&n.parentNode.removeChild(n),n=null,Wt.map&&(Wt.map.off("draw.create",qt),Wt.map.off("draw.modechange",Zt)),Wt.draw=null,Wt.map=null,Wt.options={},this},connect:function(){e.map.off("load",r.connect),clearInterval(o),r.addLayers(),e.store.storeMapConfig(),e.events.addEventListeners()},onAdd:function(i){var m=i.fire;return i.fire=function(t,e){var n=arguments;return 1===m.length&&1!==arguments.length&&(n=[Bt({},{type:t},e)]),m.apply(i,n)},e.map=i,e.events=function(e){var n=Object.keys(e.options.modes).reduce((function(t,n){return t[n]=_t(e.options.modes[n]),t}),{}),o={},r={},i={},a=null,s=null;i.drag=function(t,n){n({point:t.point,time:(new Date).getTime()})?(e.ui.queueMapClasses({mouse:w}),s.drag(t)):t.originalEvent.stopPropagation()},i.mousedrag=function(t){i.drag(t,(function(t){return!at(o,t)}))},i.touchdrag=function(t){i.drag(t,(function(t){return!st(r,t)}))},i.mousemove=function(t){if(1===(void 0!==t.originalEvent.buttons?t.originalEvent.buttons:t.originalEvent.which))return i.mousedrag(t);var n=rt(t,e);t.featureTarget=n,s.mousemove(t)},i.mousedown=function(t){o={time:(new Date).getTime(),point:t.point};var n=rt(t,e);t.featureTarget=n,s.mousedown(t)},i.mouseup=function(t){var n=rt(t,e);t.featureTarget=n,at(o,{point:t.point,time:(new Date).getTime()})?s.click(t):s.mouseup(t)},i.mouseout=function(t){s.mouseout(t)};var c=!1;function u(){return!0!==c}Ct.mobile()&&(e._selectionChangeCallback=function(t){[A.SIMPLE_SELECT,A.DIRECT_SELECT].indexOf(a)>=0?t.length>0?(e.map._touchZoomRotateEnabled=!0,e.map.touchZoomRotate.disable(),c=!0):(c=!1,!0===e.map._touchZoomRotateEnabled&&(e.map.touchZoomRotate.enable(),delete e.map._touchZoomRotateEnabled)):c=!1},e._deleteFeatureCallback=function(t){c=!1,!0===e.map._touchZoomRotateEnabled&&(e.map.touchZoomRotate.enable(),delete e.map._touchZoomRotateEnabled)}),i.touchstart=function(t){if(!u()&&(t.originalEvent.preventDefault(),e.options.touchEnabled)){r={time:(new Date).getTime(),point:t.point};var n=nt.touch(t,null,e)[0];t.featureTarget=n,s.touchstart(t)}},i.touchmove=function(t){if(!u()&&(t.originalEvent.preventDefault(),e.options.touchEnabled))return s.touchmove(t),i.touchdrag(t)},i.touchend=function(t){if(!u()&&(t.originalEvent.preventDefault(),e.options.touchEnabled)){var n=nt.touch(t,null,e)[0];t.featureTarget=n,st(r,{time:(new Date).getTime(),point:t.point})?s.tap(t):s.touchend(t)}};var l=function(t){return!(8===t||46===t||t>=48&&t<=57)};function d(o,r,i){void 0===i&&(i={}),s.stop();var c=n[o];if(void 0===c)throw new Error(o+" is not valid");a=o;var u=c(e,r);s=t(u,e),i.silent||e.map.fire(U,{mode:o}),e.store.setDirty(),e.store.render()}i.keydown=function(t){"mapboxgl-canvas"===(t.srcElement||t.target).classList[0]&&(8!==t.keyCode&&46!==t.keyCode||!e.options.controls.trash?l(t.keyCode)?s.keydown(t):49===t.keyCode&&e.options.controls.point?d(A.DRAW_POINT):50===t.keyCode&&e.options.controls.line_string?d(A.DRAW_LINE_STRING):51===t.keyCode&&e.options.controls.polygon&&d(A.DRAW_POLYGON):(t.preventDefault(),s.trash()))},i.keyup=function(t){l(t.keyCode)&&s.keyup(t)},i.zoomend=function(){e.store.changeZoom()},i.data=function(t){if("style"===t.dataType){var n=e.setup,o=e.options,r=e.store;o.styles.some((function(t){return e.map.getLayer(t.id)}))||(n.addLayers(),r.setDirty(),r.render())}};var p={trash:!1,combineFeatures:!1,uncombineFeatures:!1};return{start:function(){a=e.options.defaultMode,s=t(n[a](e),e)},changeMode:d,actionable:function(t){var n=!1;Object.keys(t).forEach((function(e){if(void 0===p[e])throw new Error("Invalid action type");p[e]!==t[e]&&(n=!0),p[e]=t[e]})),n&&e.map.fire(V,{actions:p})},currentModeName:function(){return a},currentModeRender:function(t,e){return s.render(t,e)},fire:function(t,e){i[t]&&i[t](e)},addEventListeners:function(){e.map.on("mousemove",i.mousemove),e.map.on("mousedown",i.mousedown),e.map.on("mouseup",i.mouseup),e.map.on("data",i.data),e.map.on("touchmove",i.touchmove),e.map.on("touchstart",i.touchstart),e.map.on("touchend",i.touchend),e.container.addEventListener("mouseout",i.mouseout),e.options.keybindings&&(e.container.addEventListener("keydown",i.keydown),e.container.addEventListener("keyup",i.keyup))},removeEventListeners:function(){e.map.off("mousemove",i.mousemove),e.map.off("mousedown",i.mousedown),e.map.off("mouseup",i.mouseup),e.map.off("data",i.data),e.map.off("touchmove",i.touchmove),e.map.off("touchstart",i.touchstart),e.map.off("touchend",i.touchend),e.container.removeEventListener("mouseout",i.mouseout),e.options.keybindings&&(e.container.removeEventListener("keydown",i.keydown),e.container.removeEventListener("keyup",i.keyup))},trash:function(t){s.trash(t)},combineFeatures:function(){s.combineFeatures()},uncombineFeatures:function(){s.uncombineFeatures()},getMode:function(){return a}}}(e),e.ui=function(t){var e={},n=null,o={mode:null,feature:null,mouse:null},r={mode:null,feature:null,mouse:null};function i(t){r=Bt(r,t)}function m(){var e,n;if(t.container){var i=[],a=[];Jt.forEach((function(t){r[t]!==o[t]&&(i.push(t+"-"+o[t]),null!==r[t]&&a.push(t+"-"+r[t]))})),i.length>0&&(e=t.container.classList).remove.apply(e,i),a.length>0&&(n=t.container.classList).add.apply(n,a),o=Bt(o,r)}}function y(t,e){void 0===e&&(e={});var o=document.createElement("button");return o.className=s+" "+e.className,o.setAttribute("title",e.title),e.container.appendChild(o),o.addEventListener("click",(function(o){if(o.preventDefault(),o.stopPropagation(),o.target===n)return v(),void e.onDeactivate();b(t),e.onActivate()}),!0),o}function v(){n&&(n.classList.remove(g),n=null)}function b(t){v();var o=e[t];o&&o&&"trash"!==t&&(o.classList.add(g),n=o)}return{setActiveButton:b,queueMapClasses:i,updateMapClasses:m,clearMapClasses:function(){i({mode:null,feature:null,mouse:null}),m()},addButtons:function(){var n=t.options.controls,o=document.createElement("div");return o.className=h+" "+a,n?(n[M.LINE]&&(e[M.LINE]=y(M.LINE,{container:o,className:c,title:"LineString tool "+(t.options.keybindings?"(l)":""),onActivate:function(){return t.events.changeMode(A.DRAW_LINE_STRING)},onDeactivate:function(){return t.events.trash()}})),n[M.POLYGON]&&(e[M.POLYGON]=y(M.POLYGON,{container:o,className:u,title:"Polygon tool "+(t.options.keybindings?"(p)":""),onActivate:function(){return t.events.changeMode(A.DRAW_POLYGON)},onDeactivate:function(){return t.events.trash()}})),n[M.POINT]&&(e[M.POINT]=y(M.POINT,{container:o,className:l,title:"Marker tool "+(t.options.keybindings?"(m)":""),onActivate:function(){return t.events.changeMode(A.DRAW_POINT)},onDeactivate:function(){return t.events.trash()}})),n.trash&&(e.trash=y("trash",{container:o,className:d,title:"Delete",onActivate:function(){t.events.trash()}})),n.combine_features&&(e.combine_features=y("combineFeatures",{container:o,className:p,title:"Combine",onActivate:function(){t.events.combineFeatures()}})),n.uncombine_features&&(e.uncombine_features=y("uncombineFeatures",{container:o,className:f,title:"Uncombine",onActivate:function(){t.events.uncombineFeatures()}})),o):o},removeButtons:function(){Object.keys(e).forEach((function(t){var n=e[t];n.parentNode&&n.parentNode.removeChild(n),delete e[t]}))}}}(e),e.container=i.getContainer(),e.store=new Vt(e),e.options.tip&&function(t){Object.assign(Wt,t),Wt.map&&(Wt.map.on("draw.create",qt),Wt.map.on("draw.modechange",Zt))}({map:i,draw:this,options:e.options.tip}),n=e.ui.addButtons(),e.options.boxSelect&&(i.boxZoom.disable(),i.dragPan.disable(),i.dragPan.enable()),i.loaded()?r.connect():(i.on("load",r.connect),o=setInterval((function(){i.loaded()&&r.connect()}),16)),e.events.start(),n},addLayers:function(){e.map.addSource(v,{data:{type:I,features:[]},type:"geojson"}),e.map.addSource(y,{data:{type:I,features:[]},type:"geojson"}),e.options.styles.forEach((function(t){e.map.addLayer(t)})),e.store.setDirty(!0),e.store.render()},removeLayers:function(){e.options.styles.forEach((function(t){e.map.getLayer(t.id)&&e.map.removeLayer(t.id)})),e.map.getSource(v)&&e.map.removeSource(v),e.map.getSource(y)&&e.map.removeSource(y)}};return e.setup=r,r}function Xt(t){return function(e){var n=e.featureTarget;return!!n&&(!!n.properties&&n.properties.meta===t)}}function te(t){return!!t.featureTarget&&(!!t.featureTarget.properties&&(t.featureTarget.properties.active===Y&&t.featureTarget.properties.meta===q))}function ee(t){return!!t.featureTarget&&(!!t.featureTarget.properties&&(t.featureTarget.properties.active===K&&t.featureTarget.properties.meta===q))}function ne(t){return void 0===t.featureTarget}function oe(t){var e=t.featureTarget;return!!e&&(!!e.properties&&e.properties.meta===$)}function re(t){return!!t.originalEvent&&!0===t.originalEvent.shiftKey}function ie(t){return 27===t.keyCode}function ae(t){return 13===t.keyCode}var se=ce;function ce(t,e){this.x=t,this.y=e}function ue(t,e){var n=e.getBoundingClientRect();return new se(t.clientX-n.left-(e.clientLeft||0),t.clientY-n.top-(e.clientTop||0))}function le(t,e,n,o){return{type:S,properties:{meta:$,parent:t,coord_path:n,active:o?Y:K},geometry:{type:L,coordinates:e}}}function de(t,e,n){void 0===e&&(e={}),void 0===n&&(n=null);var o,r=t.geometry,i=r.type,a=r.coordinates,s=t.properties&&t.properties.id,c=[];function u(t,n){var o="",r=null;t.forEach((function(t,i){var a=null!=n?n+"."+i:String(i),u=le(s,t,a,l(a));if(e.midpoints&&r){var d=function(t,e,n){var o=e.geometry.coordinates,r=n.geometry.coordinates;if(o[1]>85||o[1]<-85||r[1]>85||r[1]<-85)return null;var i={lng:(o[0]+r[0])/2,lat:(o[1]+r[1])/2};return{type:S,properties:{meta:Z,parent:t,lng:i.lng,lat:i.lat,coord_path:n.properties.coord_path},geometry:{type:L,coordinates:[i.lng,i.lat]}}}(s,r,u);d&&c.push(d)}r=u;var p=JSON.stringify(t);o!==p&&c.push(u),0===i&&(o=p)}))}function l(t){return!!e.selectedPaths&&-1!==e.selectedPaths.indexOf(t)}return i===L?c.push(le(s,a,n,l(n))):i===E?a.forEach((function(t,e){u(t,null!==n?n+"."+e:String(e))})):i===P?u(a,n):0===i.indexOf(k)&&(o=i.replace(k,""),a.forEach((function(n,r){var i={type:S,properties:t.properties,geometry:{type:o,coordinates:n}};c=c.concat(de(i,e,r))}))),c}ce.prototype={clone:function(){return new ce(this.x,this.y)},add:function(t){return this.clone()._add(t)},sub:function(t){return this.clone()._sub(t)},multByPoint:function(t){return this.clone()._multByPoint(t)},divByPoint:function(t){return this.clone()._divByPoint(t)},mult:function(t){return this.clone()._mult(t)},div:function(t){return this.clone()._div(t)},rotate:function(t){return this.clone()._rotate(t)},rotateAround:function(t,e){return this.clone()._rotateAround(t,e)},matMult:function(t){return this.clone()._matMult(t)},unit:function(){return this.clone()._unit()},perp:function(){return this.clone()._perp()},round:function(){return this.clone()._round()},mag:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},equals:function(t){return this.x===t.x&&this.y===t.y},dist:function(t){return Math.sqrt(this.distSqr(t))},distSqr:function(t){var e=t.x-this.x,n=t.y-this.y;return e*e+n*n},angle:function(){return Math.atan2(this.y,this.x)},angleTo:function(t){return Math.atan2(this.y-t.y,this.x-t.x)},angleWith:function(t){return this.angleWithSep(t.x,t.y)},angleWithSep:function(t,e){return Math.atan2(this.x*e-this.y*t,this.x*t+this.y*e)},_matMult:function(t){var e=t[0]*this.x+t[1]*this.y,n=t[2]*this.x+t[3]*this.y;return this.x=e,this.y=n,this},_add:function(t){return this.x+=t.x,this.y+=t.y,this},_sub:function(t){return this.x-=t.x,this.y-=t.y,this},_mult:function(t){return this.x*=t,this.y*=t,this},_div:function(t){return this.x/=t,this.y/=t,this},_multByPoint:function(t){return this.x*=t.x,this.y*=t.y,this},_divByPoint:function(t){return this.x/=t.x,this.y/=t.y,this},_unit:function(){return this._div(this.mag()),this},_perp:function(){var t=this.y;return this.y=this.x,this.x=-t,this},_rotate:function(t){var e=Math.cos(t),n=Math.sin(t),o=e*this.x-n*this.y,r=n*this.x+e*this.y;return this.x=o,this.y=r,this},_rotateAround:function(t,e){var n=Math.cos(t),o=Math.sin(t),r=e.x+n*(this.x-e.x)-o*(this.y-e.y),i=e.y+o*(this.x-e.x)+n*(this.y-e.y);return this.x=r,this.y=i,this},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}},ce.convert=function(t){return t instanceof ce?t:Array.isArray(t)?new ce(t[0],t[1]):t};var pe=function(t){setTimeout((function(){t.map&&t.map.doubleClickZoom&&t._ctx&&t._ctx.store&&t._ctx.store.getInitialConfigValue&&t._ctx.store.getInitialConfigValue("doubleClickZoom")&&t.map.doubleClickZoom.enable()}),0)},fe=function(t){setTimeout((function(){t.map&&t.map.doubleClickZoom&&t.map.doubleClickZoom.disable()}),0)},he=function(t){if(!t||!t.type)return null;var e=ge[t.type];if(!e)return null;if("geometry"===e)return{type:"FeatureCollection",features:[{type:"Feature",properties:{},geometry:t}]};if("feature"===e)return{type:"FeatureCollection",features:[t]};if("featurecollection"===e)return t},ge={Point:"geometry",MultiPoint:"geometry",LineString:"geometry",MultiLineString:"geometry",Polygon:"geometry",MultiPolygon:"geometry",GeometryCollection:"geometry",Feature:"feature",FeatureCollection:"featurecollection"};var me=function t(e){switch(e&&e.type||null){case"FeatureCollection":return e.features=e.features.reduce((function(e,n){return e.concat(t(n))}),[]),e;case"Feature":return e.geometry?t(e.geometry).map((function(t){var n={type:"Feature",properties:JSON.parse(JSON.stringify(e.properties)),geometry:t};return void 0!==e.id&&(n.id=e.id),n})):[e];case"MultiPoint":return e.coordinates.map((function(t){return{type:"Point",coordinates:t}}));case"MultiPolygon":return e.coordinates.map((function(t){return{type:"Polygon",coordinates:t}}));case"MultiLineString":return e.coordinates.map((function(t){return{type:"LineString",coordinates:t}}));case"GeometryCollection":return e.geometries.map(t).reduce((function(t,e){return t.concat(e)}),[]);case"Point":case"Polygon":case"LineString":return[e]}};me instanceof Function||(me=me.default);var ye=function(t){if(!t)return[];var e=me(he(t)),n=[];return e.features.forEach((function(t){t.geometry&&(n=n.concat(function t(e){return Array.isArray(e)&&e.length&&"number"==typeof e[0]?[e]:e.reduce((function(e,n){return Array.isArray(n)&&Array.isArray(n[0])?e.concat(t(n)):(e.push(n),e)}),[])}(t.geometry.coordinates)))})),n};function ve(t){return Object.prototype.toString.call(t)}var be=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function _e(t,e){if(t.forEach)return t.forEach(e);for(var n=0;n<t.length;n++)e(t[n],n,t)}var we=Object.keys||function(t){var e=[];for(var n in t)e.push(n);return e},xe=Object.prototype.hasOwnProperty||function(t,e){return e in t};function Ce(t){if("object"==typeof t&&null!==t){var e;if(be(t))e=[];else if("[object Date]"===ve(t))e=new Date(t.getTime?t.getTime():t);else if(function(t){return"[object RegExp]"===ve(t)}(t))e=new RegExp(t);else if(function(t){return"[object Error]"===ve(t)}(t))e={message:t.message};else if(function(t){return"[object Boolean]"===ve(t)}(t)||function(t){return"[object Number]"===ve(t)}(t)||function(t){return"[object String]"===ve(t)}(t))e=Object(t);else if(Object.create&&Object.getPrototypeOf)e=Object.create(Object.getPrototypeOf(t));else if(t.constructor===Object)e={};else{var n=t.constructor&&t.constructor.prototype||t.__proto__||{},o=function(){};o.prototype=n,e=new o}return _e(we(t),(function(n){e[n]=t[n]})),e}return t}function Me(t,e,n){var o=[],r=[],i=!0;return function t(a){var s=n?Ce(a):a,c={},u=!0,l={node:s,node_:a,path:[].concat(o),parent:r[r.length-1],parents:r,key:o[o.length-1],isRoot:0===o.length,level:o.length,circular:null,update:function(t,e){l.isRoot||(l.parent.node[l.key]=t),l.node=t,e&&(u=!1)},delete:function(t){delete l.parent.node[l.key],t&&(u=!1)},remove:function(t){be(l.parent.node)?l.parent.node.splice(l.key,1):delete l.parent.node[l.key],t&&(u=!1)},keys:null,before:function(t){c.before=t},after:function(t){c.after=t},pre:function(t){c.pre=t},post:function(t){c.post=t},stop:function(){i=!1},block:function(){u=!1}};if(!i)return l;function d(){if("object"==typeof l.node&&null!==l.node){l.keys&&l.node_===l.node||(l.keys=we(l.node)),l.isLeaf=0===l.keys.length;for(var t=0;t<r.length;t++)if(r[t].node_===a){l.circular=r[t];break}}else l.isLeaf=!0,l.keys=null;l.notLeaf=!l.isLeaf,l.notRoot=!l.isRoot}d();var p=e.call(l,l.node);return void 0!==p&&l.update&&l.update(p),c.before&&c.before.call(l,l.node),u?("object"!=typeof l.node||null===l.node||l.circular||(r.push(l),d(),_e(l.keys,(function(e,r){o.push(e),c.pre&&c.pre.call(l,l.node[e],e);var i=t(l.node[e]);n&&xe.call(l.node,e)&&(l.node[e]=i.node),i.isLast=r===l.keys.length-1,i.isFirst=0===r,c.post&&c.post.call(l,i),o.pop()})),r.pop()),c.after&&c.after.call(l,l.node),l):l}(t).node}function Se(t){this.value=t}function Ee(t){return new Se(t)}Se.prototype.get=function(t){for(var e=this.value,n=0;n<t.length;n++){var o=t[n];if(!e||!xe.call(e,o))return;e=e[o]}return e},Se.prototype.has=function(t){for(var e=this.value,n=0;n<t.length;n++){var o=t[n];if(!e||!xe.call(e,o))return!1;e=e[o]}return!0},Se.prototype.set=function(t,e){for(var n=this.value,o=0;o<t.length-1;o++){var r=t[o];xe.call(n,r)||(n[r]={}),n=n[r]}return n[t[o]]=e,e},Se.prototype.map=function(t){return Me(this.value,t,!0)},Se.prototype.forEach=function(t){return this.value=Me(this.value,t,!1),this.value},Se.prototype.reduce=function(t,e){var n=1===arguments.length,o=n?this.value:e;return this.forEach((function(e){this.isRoot&&n||(o=t.call(this,o,e))})),o},Se.prototype.paths=function(){var t=[];return this.forEach((function(){t.push(this.path)})),t},Se.prototype.nodes=function(){var t=[];return this.forEach((function(){t.push(this.node)})),t},Se.prototype.clone=function(){var t=[],e=[];return function n(o){for(var r=0;r<t.length;r++)if(t[r]===o)return e[r];if("object"==typeof o&&null!==o){var i=Ce(o);return t.push(o),e.push(i),_e(we(o),(function(t){i[t]=n(o[t])})),t.pop(),e.pop(),i}return o}(this.value)},_e(we(Se.prototype),(function(t){Ee[t]=function(e){var n=[].slice.call(arguments,1),o=new Se(e);return o[t].apply(o,n)}}));var Pe=Ee,Le=Ie;function Ie(t){if(!(this instanceof Ie))return new Ie(t);this._bbox=t||[1/0,1/0,-1/0,-1/0],this._valid=!!t}Ie.prototype.include=function(t){return this._valid=!0,this._bbox[0]=Math.min(this._bbox[0],t[0]),this._bbox[1]=Math.min(this._bbox[1],t[1]),this._bbox[2]=Math.max(this._bbox[2],t[0]),this._bbox[3]=Math.max(this._bbox[3],t[1]),this},Ie.prototype.equals=function(t){var e;return e=t instanceof Ie?t.bbox():t,this._bbox[0]==e[0]&&this._bbox[1]==e[1]&&this._bbox[2]==e[2]&&this._bbox[3]==e[3]},Ie.prototype.center=function(t){return this._valid?[(this._bbox[0]+this._bbox[2])/2,(this._bbox[1]+this._bbox[3])/2]:null},Ie.prototype.union=function(t){var e;return this._valid=!0,e=t instanceof Ie?t.bbox():t,this._bbox[0]=Math.min(this._bbox[0],e[0]),this._bbox[1]=Math.min(this._bbox[1],e[1]),this._bbox[2]=Math.max(this._bbox[2],e[2]),this._bbox[3]=Math.max(this._bbox[3],e[3]),this},Ie.prototype.bbox=function(){return this._valid?this._bbox:null},Ie.prototype.contains=function(t){if(!t)return this._fastContains();if(!this._valid)return null;var e=t[0],n=t[1];return this._bbox[0]<=e&&this._bbox[1]<=n&&this._bbox[2]>=e&&this._bbox[3]>=n},Ie.prototype.intersect=function(t){return this._valid?(e=t instanceof Ie?t.bbox():t,!(this._bbox[0]>e[2]||this._bbox[2]<e[0]||this._bbox[3]<e[1]||this._bbox[1]>e[3])):null;var e},Ie.prototype._fastContains=function(){if(!this._valid)return new Function("return null;");var t="return "+this._bbox[0]+"<= ll[0] &&"+this._bbox[1]+"<= ll[1] &&"+this._bbox[2]+">= ll[0] &&"+this._bbox[3]+">= ll[1]";return new Function("ll",t)},Ie.prototype.polygon=function(){return this._valid?{type:"Polygon",coordinates:[[[this._bbox[0],this._bbox[1]],[this._bbox[2],this._bbox[1]],[this._bbox[2],this._bbox[3]],[this._bbox[0],this._bbox[3]],[this._bbox[0],this._bbox[1]]]]}:null};var ke={features:["FeatureCollection"],coordinates:["Point","MultiPoint","LineString","MultiLineString","Polygon","MultiPolygon"],geometry:["Feature"],geometries:["GeometryCollection"]},Fe=Object.keys(ke),Te=function(t){return Oe(t).bbox()};function Oe(t){for(var e=Le(),n=ye(t),o=0;o<n.length;o++)e.include(n[o]);return e}Te.polygon=function(t){return Oe(t).polygon()},Te.bboxify=function(t){return Pe(t).map((function(t){t&&(Fe.some((function(e){return!!t[e]&&-1!==ke[e].indexOf(t.type)}))&&(t.bbox=Oe(t).bbox(),this.update(t)))}))};function Ae(t,e){var n=-90,o=90,r=-90,i=90,a=270,s=-270;t.forEach((function(t){var e=Te(t),c=e[1],u=e[3],l=e[0],d=e[2];c>n&&(n=c),u<o&&(o=u),u>r&&(r=u),c<i&&(i=c),l<a&&(a=l),d>s&&(s=d)}));var c=e;return n+c.lat>85&&(c.lat=85-n),r+c.lat>90&&(c.lat=90-r),o+c.lat<-85&&(c.lat=-85-o),i+c.lat<-90&&(c.lat=-90-i),a+c.lng<=-270&&(c.lng+=360*Math.ceil(Math.abs(c.lng)/360)),s+c.lng>=270&&(c.lng-=360*Math.ceil(Math.abs(c.lng)/360)),c}function je(t,e){var n=Ae(t.map((function(t){return t.toGeoJSON()})),e);t.forEach((function(t){var e,o=t.getCoordinates(),r=function(t){var e={lng:t[0]+n.lng,lat:t[1]+n.lat};return[e.lng,e.lat]},i=function(t){return t.map((function(t){return r(t)}))};t.type===L?e=r(o):t.type===P||t.type===F?e=o.map(r):t.type===E||t.type===T?e=o.map(i):t.type===O&&(e=o.map((function(t){return t.map((function(t){return i(t)}))}))),t.incomingCoords(e)}))}var De={onSetup:function(t){var e=this,n={dragMoveLocation:null,boxSelectStartLocation:null,boxSelectElement:void 0,boxSelecting:!1,canBoxSelect:!1,dragMoving:!1,canDragMove:!1,initiallySelectedFeatureIds:t.featureIds||[]};return this.setSelected(n.initiallySelectedFeatureIds.filter((function(t){return void 0!==e.getFeature(t)}))),this.fireActionable(),this.setActionableState({combineFeatures:!0,uncombineFeatures:!0,trash:!0}),n},fireUpdate:function(){this.map.fire(N,{action:J,features:this.getSelected().map((function(t){return t.toGeoJSON()}))})},fireCharting:function(t){this.map.fire("draw.draging",{features:this.getSelected().map((function(t){return t.toGeoJSON()})),lngLat:t.lngLat})},fireActionable:function(){var t=this,e=this.getSelected(),n=e.filter((function(e){return t.isInstanceOf("MultiFeature",e)})),o=!1;if(e.length>1){o=!0;var r=e[0].type.replace("Multi","");e.forEach((function(t){t.type.replace("Multi","")!==r&&(o=!1)}))}var i=n.length>0,a=e.length>0;this.setActionableState({combineFeatures:o,uncombineFeatures:i,trash:a})},getUniqueIds:function(t){return t.length?t.map((function(t){return t.properties.id})).filter((function(t){return void 0!==t})).reduce((function(t,e){return t.add(e),t}),new tt).values():[]},stopExtendedInteractions:function(t){t.boxSelectElement&&(t.boxSelectElement.parentNode&&t.boxSelectElement.parentNode.removeChild(t.boxSelectElement),t.boxSelectElement=null),this.map.dragPan.enable(),t.boxSelecting=!1,t.canBoxSelect=!1,t.dragMoving=!1,t.canDragMove=!1},onStop:function(){pe(this)},onMouseMove:function(t){return this.stopExtendedInteractions(t),!0},onMouseOut:function(t){return!t.dragMoving||this.fireUpdate()}};De.onTap=De.onClick=function(t,e){return ne(e)?this.clickAnywhere(t,e):Xt($)(e)?this.clickOnVertex(t,e):function(t){return!!t.featureTarget&&(!!t.featureTarget.properties&&t.featureTarget.properties.meta===q)}(e)?this.clickOnFeature(t,e):void 0},De.clickAnywhere=function(t){var e=this,n=this.getSelectedIds();n.length&&(this.clearSelectedFeatures(),n.forEach((function(t){return e.doRender(t)}))),pe(this),this.stopExtendedInteractions(t)},De.clickOnVertex=function(t,e){this.changeMode(A.DIRECT_SELECT,{featureId:e.featureTarget.properties.parent,coordPath:e.featureTarget.properties.coord_path,startPos:e.lngLat}),this.updateUIClasses({mouse:_})},De.startOnActiveFeature=function(t,e){this.stopExtendedInteractions(t),this.map.dragPan.disable(),this.doRender(e.featureTarget.properties.id),t.canDragMove=!0,t.dragMoveLocation=e.lngLat},De.clickOnFeature=function(t,e){var n=this;fe(this),this.stopExtendedInteractions(t);var o=re(e),r=this.getSelectedIds(),i=e.featureTarget.properties.id,a=this.isSelected(i);if(!o&&a&&this.getFeature(i).type!==L)return this.changeMode(A.DIRECT_SELECT,{featureId:i});a&&o?(this.deselect(i),this.updateUIClasses({mouse:x}),1===r.length&&pe(this)):!a&&o?(this.select(i),this.updateUIClasses({mouse:_})):a||o||(r.forEach((function(t){return n.doRender(t)})),this.setSelected(i),this.updateUIClasses({mouse:_})),this.doRender(i)},De.onMouseDown=function(t,e){return te(e)?this.startOnActiveFeature(t,e):this.drawConfig.boxSelect&&function(t){return!!t.originalEvent&&(!!t.originalEvent.shiftKey&&0===t.originalEvent.button)}(e)?this.startBoxSelect(t,e):void 0},De.startBoxSelect=function(t,e){this.stopExtendedInteractions(t),this.map.dragPan.disable(),t.boxSelectStartLocation=ue(e.originalEvent,this.map.getContainer()),t.canBoxSelect=!0},De.onTouchStart=function(t,e){if(te(e))return this.startOnActiveFeature(t,e)},De.onDrag=function(t,e){return t.canDragMove?(this.fireCharting(e),this.dragMove(t,e)):this.drawConfig.boxSelect&&t.canBoxSelect?this.whileBoxSelect(t,e):void 0},De.whileBoxSelect=function(t,e){t.boxSelecting=!0,this.updateUIClasses({mouse:b}),t.boxSelectElement||(t.boxSelectElement=document.createElement("div"),t.boxSelectElement.classList.add(m),this.map.getContainer().appendChild(t.boxSelectElement));var n=ue(e.originalEvent,this.map.getContainer()),o=Math.min(t.boxSelectStartLocation.x,n.x),r=Math.max(t.boxSelectStartLocation.x,n.x),i=Math.min(t.boxSelectStartLocation.y,n.y),a=Math.max(t.boxSelectStartLocation.y,n.y),s="translate("+o+"px, "+i+"px)";t.boxSelectElement.style.transform=s,t.boxSelectElement.style.WebkitTransform=s,t.boxSelectElement.style.width=r-o+"px",t.boxSelectElement.style.height=a-i+"px"},De.dragMove=function(t,e){t.dragMoving=!0,e.originalEvent.stopPropagation();var n={lng:e.lngLat.lng-t.dragMoveLocation.lng,lat:e.lngLat.lat-t.dragMoveLocation.lat};je(this.getSelected(),n),t.dragMoveLocation=e.lngLat},De.onTouchEnd=De.onMouseUp=function(t,e){var n=this;if(t.dragMoving)this.fireUpdate();else if(t.boxSelecting){var o=[t.boxSelectStartLocation,ue(e.originalEvent,this.map.getContainer())],r=this.featuresAt(null,o,"click"),i=this.getUniqueIds(r).filter((function(t){return!n.isSelected(t)}));i.length&&(this.select(i),i.forEach((function(t){return n.doRender(t)})),this.updateUIClasses({mouse:_}))}this.stopExtendedInteractions(t)},De.toDisplayFeatures=function(t,e,n){e.properties.active=this.isSelected(e.properties.id)?Y:K,n(e),this.fireActionable(),e.properties.active===Y&&e.geometry.type!==L&&de(e).forEach(n)},De.onTrash=function(){this.deleteFeature(this.getSelectedIds()),this.fireActionable()},De.onCombineFeatures=function(){var t=this.getSelected();if(!(0===t.length||t.length<2)){for(var e=[],n=[],o=t[0].type.replace("Multi",""),r=0;r<t.length;r++){var i=t[r];if(i.type.replace("Multi","")!==o)return;i.type.includes("Multi")?i.getCoordinates().forEach((function(t){e.push(t)})):e.push(i.getCoordinates()),n.push(i.toGeoJSON())}if(n.length>1){var a=this.newFeature({type:S,properties:n[0].properties,geometry:{type:"Multi"+o,coordinates:e}});this.addFeature(a),this.deleteFeature(this.getSelectedIds(),{silent:!0}),this.setSelected([a.id]),this.map.fire(B,{createdFeatures:[a.toGeoJSON()],deletedFeatures:n})}this.fireActionable()}},De.onUncombineFeatures=function(){var t=this,e=this.getSelected();if(0!==e.length){for(var n=[],o=[],r=function(r){var i=e[r];t.isInstanceOf("MultiFeature",i)&&(i.getFeatures().forEach((function(e){t.addFeature(e),e.properties=i.properties,n.push(e.toGeoJSON()),t.select([e.id])})),t.deleteFeature(i.id,{silent:!0}),o.push(i.toGeoJSON()))},i=0;i<e.length;i++)r(i);n.length>1&&this.map.fire(z,{createdFeatures:n,deletedFeatures:o}),this.fireActionable()}};var Ne=Xt($),Re=Xt(Z),Ue={fireUpdate:function(){this.map.fire(N,{action:W,features:this.getSelected().map((function(t){return t.toGeoJSON()}))})},fireActionable:function(t){this.setActionableState({combineFeatures:!1,uncombineFeatures:!1,trash:t.selectedCoordPaths.length>0})},startDragging:function(t,e){this.map.dragPan.disable(),t.canDragMove=!0,t.dragMoveLocation=e.lngLat},stopDragging:function(t){this.map.dragPan.enable(),t.dragMoving=!1,t.canDragMove=!1,t.dragMoveLocation=null},onVertex:function(t,e){this.startDragging(t,e);var n=e.featureTarget.properties,o=t.selectedCoordPaths.indexOf(n.coord_path);re(e)||-1!==o?re(e)&&-1===o&&t.selectedCoordPaths.push(n.coord_path):t.selectedCoordPaths=[n.coord_path];var r=this.pathsToCoordinates(t.featureId,t.selectedCoordPaths);this.setSelectedCoordinates(r)},onMidpoint:function(t,e){this.startDragging(t,e);var n=e.featureTarget.properties;t.feature.addCoordinate(n.coord_path,n.lng,n.lat),this.fireUpdate(),t.selectedCoordPaths=[n.coord_path]},pathsToCoordinates:function(t,e){return e.map((function(e){return{feature_id:t,coord_path:e}}))},onFeature:function(t,e){0===t.selectedCoordPaths.length?this.startDragging(t,e):this.stopDragging(t)},dragFeature:function(t,e,n){je(this.getSelected(),n),t.dragMoveLocation=e.lngLat},dragVertex:function(t,e,n){for(var o=t.selectedCoordPaths.map((function(e){return t.feature.getCoordinate(e)})),r=Ae(o.map((function(t){return{type:S,properties:{},geometry:{type:L,coordinates:t}}})),n),i=0;i<o.length;i++){var a=o[i];t.feature.updateCoordinate(t.selectedCoordPaths[i],a[0]+r.lng,a[1]+r.lat)}},clickNoTarget:function(){this.changeMode(A.SIMPLE_SELECT)},clickInactive:function(){this.changeMode(A.SIMPLE_SELECT)},clickActiveFeature:function(t){t.selectedCoordPaths=[],this.clearSelectedCoordinates(),t.feature.changed()},onSetup:function(t){var e=t.featureId,n=this.getFeature(e);if(!n)throw new Error("You must provide a featureId to enter direct_select mode");if(n.type===L)throw new TypeError("direct_select mode doesn't handle point features");var o={featureId:e,feature:n,dragMoveLocation:t.startPos||null,dragMoving:!1,canDragMove:!1,selectedCoordPaths:t.coordPath?[t.coordPath]:[]};return this.setSelectedCoordinates(this.pathsToCoordinates(e,o.selectedCoordPaths)),this.setSelected(e),fe(this),this.setActionableState({trash:!0}),o},onStop:function(){pe(this),this.clearSelectedCoordinates()},toDisplayFeatures:function(t,e,n){t.featureId===e.properties.id?(e.properties.active=Y,n(e),de(e,{map:this.map,midpoints:!0,selectedPaths:t.selectedCoordPaths}).forEach(n)):(e.properties.active=K,n(e)),this.fireActionable(t)},onTrash:function(t){t.selectedCoordPaths.sort((function(t,e){return e.localeCompare(t,"en",{numeric:!0})})).forEach((function(e){return t.feature.removeCoordinate(e)})),this.fireUpdate(),t.selectedCoordPaths=[],this.clearSelectedCoordinates(),this.fireActionable(t),!1===t.feature.isValid()&&(this.deleteFeature([t.featureId]),this.changeMode(A.SIMPLE_SELECT,{}))},onMouseMove:function(t,e){var n=te(e),o=Ne(e),r=0===t.selectedCoordPaths.length;return n&&r||o&&!r?this.updateUIClasses({mouse:_}):this.updateUIClasses({mouse:C}),this.stopDragging(t),!0},onMouseOut:function(t){return t.dragMoving&&this.fireUpdate(),!0}};Ue.onTouchStart=Ue.onMouseDown=function(t,e){return Ne(e)?this.onVertex(t,e):te(e)?this.onFeature(t,e):Re(e)?this.onMidpoint(t,e):void 0},Ue.onDrag=function(t,e){if(!0===t.canDragMove){t.dragMoving=!0,e.originalEvent.stopPropagation();var n={lng:e.lngLat.lng-t.dragMoveLocation.lng,lat:e.lngLat.lat-t.dragMoveLocation.lat};t.selectedCoordPaths.length>0?this.dragVertex(t,e,n):this.dragFeature(t,e,n),t.dragMoveLocation=e.lngLat}},Ue.onClick=function(t,e){return ne(e)?this.clickNoTarget(t,e):te(e)?this.clickActiveFeature(t,e):ee(e)?this.clickInactive(t,e):void this.stopDragging(t)},Ue.onTap=function(t,e){return ne(e)?this.clickNoTarget(t,e):te(e)?this.clickActiveFeature(t,e):ee(e)?this.clickInactive(t,e):void 0},Ue.onTouchEnd=Ue.onMouseUp=function(t){t.dragMoving&&this.fireUpdate(),this.stopDragging(t)};var Ve={};function Ge(t,e){return!!t.lngLat&&(t.lngLat.lng===e[0]&&t.lngLat.lat===e[1])}Ve.onSetup=function(){var t=this.newFeature({type:S,properties:{},geometry:{type:L,coordinates:[]}});return this.addFeature(t),this.clearSelectedFeatures(),this.updateUIClasses({mouse:b}),this.activateUIButton(M.POINT),this.setActionableState({trash:!0}),{point:t}},Ve.stopDrawingAndRemove=function(t){this.deleteFeature([t.point.id],{silent:!0}),this.changeMode(A.SIMPLE_SELECT)},Ve.onTap=Ve.onClick=function(t,e){this.updateUIClasses({mouse:_}),t.point.updateCoordinate("",e.lngLat.lng,e.lngLat.lat),this.map.fire(j,{features:[t.point.toGeoJSON()]}),this.changeMode(A.SIMPLE_SELECT,{featureIds:[t.point.id]})},Ve.onStop=function(t){this.activateUIButton(),t.point.getCoordinate().length||this.deleteFeature([t.point.id],{silent:!0})},Ve.toDisplayFeatures=function(t,e,n){var o=e.properties.id===t.point.id;if(e.properties.active=o?Y:K,!o)return n(e)},Ve.onTrash=Ve.stopDrawingAndRemove,Ve.onKeyUp=function(t,e){if(ie(e)||ae(e))return this.stopDrawingAndRemove(t,e)};var Be={onSetup:function(){var t=this.newFeature({type:S,properties:{},geometry:{type:E,coordinates:[[]]}});return this.addFeature(t),this.clearSelectedFeatures(),fe(this),this.updateUIClasses({mouse:b}),this.activateUIButton(M.POLYGON),this.setActionableState({trash:!0}),{polygon:t,currentVertexPosition:0}},clickAnywhere:function(t,e){if(t.currentVertexPosition>0&&Ge(e,t.polygon.coordinates[0][t.currentVertexPosition-1]))return this.changeMode(A.SIMPLE_SELECT,{featureIds:[t.polygon.id]});this.updateUIClasses({mouse:b}),t.polygon.updateCoordinate("0."+t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat),t.currentVertexPosition++,t.polygon.updateCoordinate("0."+t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat);try{Kt({id:t.polygon.id,lngLat:e.lngLat})}catch(e){console.warn(e)}},clickOnVertex:function(t){return this.changeMode(A.SIMPLE_SELECT,{featureIds:[t.polygon.id]})},onMouseMove:function(t,e){t.polygon.updateCoordinate("0."+t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat),oe(e)&&this.updateUIClasses({mouse:x})}};Be.onTap=Be.onClick=function(t,e){return oe(e)?this.clickOnVertex(t,e):this.clickAnywhere(t,e)},Be.onKeyUp=function(t,e){ie(e)?(this.deleteFeature([t.polygon.id],{silent:!0}),this.changeMode(A.SIMPLE_SELECT)):ae(e)&&this.changeMode(A.SIMPLE_SELECT,{featureIds:[t.polygon.id]})},Be.onStop=function(t){this.updateUIClasses({mouse:C}),pe(this),this.activateUIButton(),void 0!==this.getFeature(t.polygon.id)&&(t.polygon.removeCoordinate("0."+t.currentVertexPosition),t.polygon.isValid()?this.map.fire(j,{features:[t.polygon.toGeoJSON()]}):(this.deleteFeature([t.polygon.id],{silent:!0}),this.changeMode(A.SIMPLE_SELECT,{},{silent:!0})))},Be.toDisplayFeatures=function(t,e,n){var o=e.properties.id===t.polygon.id;if(e.properties.active=o?Y:K,!o)return n(e);if(0!==e.geometry.coordinates.length){var r=e.geometry.coordinates[0].length;if(!(r<3)){if(e.properties.meta=q,n(le(t.polygon.id,e.geometry.coordinates[0][0],"0.0",!1)),r>3){var i=e.geometry.coordinates[0].length-3;n(le(t.polygon.id,e.geometry.coordinates[0][i],"0."+i,!1))}if(r<=4){var a=[[e.geometry.coordinates[0][0][0],e.geometry.coordinates[0][0][1]],[e.geometry.coordinates[0][1][0],e.geometry.coordinates[0][1][1]]];if(n({type:S,properties:e.properties,geometry:{coordinates:a,type:P}}),3===r)return}return n(e)}}},Be.onTrash=function(t){this.deleteFeature([t.polygon.id],{silent:!0}),this.changeMode(A.SIMPLE_SELECT),Ht()};var ze={onSetup:function(t){var e,n,o=(t=t||{}).featureId,r="forward";if(o){if(!(e=this.getFeature(o)))throw new Error("Could not find a feature with the provided featureId");var i=t.from;if(i&&"Feature"===i.type&&i.geometry&&"Point"===i.geometry.type&&(i=i.geometry),i&&"Point"===i.type&&i.coordinates&&2===i.coordinates.length&&(i=i.coordinates),!i||!Array.isArray(i))throw new Error("Please use the `from` property to indicate which point to continue the line from");var a=e.coordinates.length-1;if(e.coordinates[a][0]===i[0]&&e.coordinates[a][1]===i[1])n=a+1,e.addCoordinate.apply(e,[n].concat(e.coordinates[a]));else{if(e.coordinates[0][0]!==i[0]||e.coordinates[0][1]!==i[1])throw new Error("`from` should match the point at either the start or the end of the provided LineString");r="backwards",n=0,e.addCoordinate.apply(e,[n].concat(e.coordinates[0]))}}else e=this.newFeature({type:S,properties:{},geometry:{type:P,coordinates:[]}}),n=0,this.addFeature(e);return this.clearSelectedFeatures(),fe(this),this.updateUIClasses({mouse:b}),this.activateUIButton(M.LINE),this.setActionableState({trash:!0}),{line:e,currentVertexPosition:n,direction:r}},clickAnywhere:function(t,e){if(t.currentVertexPosition>0&&Ge(e,t.line.coordinates[t.currentVertexPosition-1])||"backwards"===t.direction&&Ge(e,t.line.coordinates[t.currentVertexPosition+1]))return this.changeMode(A.SIMPLE_SELECT,{featureIds:[t.line.id]});this.updateUIClasses({mouse:b}),t.line.updateCoordinate(t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat),"forward"===t.direction?(t.currentVertexPosition++,t.line.updateCoordinate(t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat)):t.line.addCoordinate(0,e.lngLat.lng,e.lngLat.lat);try{Kt({id:t.line.id,lngLat:e.lngLat})}catch(e){console.warn(e)}},clickOnVertex:function(t){return this.changeMode(A.SIMPLE_SELECT,{featureIds:[t.line.id]})},onMouseMove:function(t,e){t.line.updateCoordinate(t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat),oe(e)&&this.updateUIClasses({mouse:x})}};ze.onTap=ze.onClick=function(t,e){if(oe(e))return this.clickOnVertex(t,e);this.clickAnywhere(t,e)},ze.onKeyUp=function(t,e){ae(e)?this.changeMode(A.SIMPLE_SELECT,{featureIds:[t.line.id]}):ie(e)&&(this.deleteFeature([t.line.id],{silent:!0}),this.changeMode(A.SIMPLE_SELECT))},ze.onStop=function(t){pe(this),this.activateUIButton(),void 0!==this.getFeature(t.line.id)&&(t.line.removeCoordinate(""+t.currentVertexPosition),t.line.isValid()?this.map.fire(j,{features:[t.line.toGeoJSON()]}):(this.deleteFeature([t.line.id],{silent:!0}),this.changeMode(A.SIMPLE_SELECT,{},{silent:!0})))},ze.onTrash=function(t){this.deleteFeature([t.line.id],{silent:!0}),this.changeMode(A.SIMPLE_SELECT),Ht()},ze.toDisplayFeatures=function(t,e,n){var o=e.properties.id===t.line.id;if(e.properties.active=o?Y:K,!o)return n(e);e.geometry.coordinates.length<2||(e.properties.meta=q,n(le(t.line.id,e.geometry.coordinates["forward"===t.direction?e.geometry.coordinates.length-2:1],""+("forward"===t.direction?e.geometry.coordinates.length-2:1),!1)),n(e))};var Je=function(t){setTimeout((function(){t.map&&t.map.doubleClickZoom&&t._ctx&&t._ctx.store&&t._ctx.store.getInitialConfigValue&&t._ctx.store.getInitialConfigValue("doubleClickZoom")&&t.map.doubleClickZoom.enable()}),0)},We=function(t){setTimeout((function(){t.map&&t.map.doubleClickZoom&&t.map.doubleClickZoom.disable()}),0)},qe={onSetup:function(t){var e=this.newFeature({type:"Feature",properties:{},geometry:{type:"Polygon",coordinates:[[]]}});return this.addFeature(e),this.clearSelectedFeatures(),We(this),this.updateUIClasses({mouse:"add"}),this.setActionableState({trash:!0}),{rectangle:e}},onTap:function(t,e){t.startPoint&&this.onMouseMove(t,e),this.onClick(t,e)},onClick:function(t,e){t.startPoint&&t.startPoint[0]!==e.lngLat.lng&&t.startPoint[1]!==e.lngLat.lat&&(this.updateUIClasses({mouse:"pointer"}),t.endPoint=[e.lngLat.lng,e.lngLat.lat],this.changeMode("simple_select",{featuresId:t.rectangle.id}));var n=[e.lngLat.lng,e.lngLat.lat];t.startPoint=n},onMouseMove:function(t,e){t.startPoint&&(t.rectangle.updateCoordinate("0.0",t.startPoint[0],t.startPoint[1]),t.rectangle.updateCoordinate("0.1",e.lngLat.lng,t.startPoint[1]),t.rectangle.updateCoordinate("0.2",e.lngLat.lng,e.lngLat.lat),t.rectangle.updateCoordinate("0.3",t.startPoint[0],e.lngLat.lat),t.rectangle.updateCoordinate("0.4",t.startPoint[0],t.startPoint[1]))},onKeyUp:function(t,e){if(27===e.keyCode)return this.changeMode("simple_select")},onStop:function(t){Je(this),this.updateUIClasses({mouse:"none"}),this.activateUIButton(),void 0!==this.getFeature(t.rectangle.id)&&(t.rectangle.removeCoordinate("0.4"),t.rectangle.isValid()?this.map.fire("draw.create",{features:[t.rectangle.toGeoJSON()]}):(this.deleteFeature([t.rectangle.id],{silent:!0}),this.changeMode("simple_select",{},{silent:!0})))},toDisplayFeatures:function(t,e,n){var o=e.properties.id===t.rectangle.id;return e.properties.active=o?"true":"false",o?t.startPoint?n(e):void 0:n(e)},onTrash:function(t){this.deleteFeature([t.rectangle.id],{silent:!0}),this.changeMode("simple_select")}},Ze={centimeters:637100880,centimetres:637100880,degrees:6371008.8/111325,feet:20902260.511392,inches:6371008.8*39.37,kilometers:6371.0088,kilometres:6371.0088,meters:6371008.8,metres:6371008.8,miles:3958.761333810546,millimeters:6371008800,millimetres:6371008800,nauticalmiles:6371008.8/1852,radians:1,yards:6371008.8*1.0936};function $e(t,e,n){void 0===n&&(n={});var o={type:"Feature"};return(0===n.id||n.id)&&(o.id=n.id),n.bbox&&(o.bbox=n.bbox),o.properties=e||{},o.geometry=t,o}function Ye(t,e,n){if(void 0===n&&(n={}),!t)throw new Error("coordinates is required");if(!Array.isArray(t))throw new Error("coordinates must be an Array");if(t.length<2)throw new Error("coordinates must be at least 2 numbers long");if(!nn(t[0])||!nn(t[1]))throw new Error("coordinates must contain numbers");return $e({type:"Point",coordinates:t},e,n)}function Ke(t,e,n){void 0===n&&(n={});for(var o=0,r=t;o<r.length;o++){var i=r[o];if(i.length<4)throw new Error("Each LinearRing of a Polygon must have 4 or more Positions.");for(var a=0;a<i[i.length-1].length;a++)if(i[i.length-1][a]!==i[0][a])throw new Error("First and last Position are not equivalent.")}return $e({type:"Polygon",coordinates:t},e,n)}function He(t,e){void 0===e&&(e="kilometers");var n=Ze[e];if(!n)throw new Error(e+" units is invalid");return t*n}function Qe(t,e){void 0===e&&(e="kilometers");var n=Ze[e];if(!n)throw new Error(e+" units is invalid");return t/n}function Xe(t){return 180*(t%(2*Math.PI))/Math.PI}function tn(t){return t%360*Math.PI/180}function en(t,e,n){if(void 0===e&&(e="kilometers"),void 0===n&&(n="kilometers"),!(t>=0))throw new Error("length must be a positive number");return He(Qe(t,e),n)}function nn(t){return!isNaN(t)&&null!==t&&!Array.isArray(t)}function on(t){return!!t&&t.constructor===Object}function rn(t){if(!t)throw new Error("coord is required");if(!Array.isArray(t)){if("Feature"===t.type&&null!==t.geometry&&"Point"===t.geometry.type)return t.geometry.coordinates;if("Point"===t.type)return t.coordinates}if(Array.isArray(t)&&t.length>=2&&!Array.isArray(t[0])&&!Array.isArray(t[1]))return t;throw new Error("coord must be GeoJSON Point or an Array of numbers")}function an(t){if(Array.isArray(t))return t;if("Feature"===t.type){if(null!==t.geometry)return t.geometry.coordinates}else if(t.coordinates)return t.coordinates;throw new Error("coords must be GeoJSON Feature, Geometry Object or an Array")}function sn(t){var e=an(t);if(2===e.length&&!cn(e[0],e[1]))return e;var n=[],o=e.length-1,r=n.length;n.push(e[0]);for(var i=1;i<o;i++){var a=n[n.length-1];e[i][0]===a[0]&&e[i][1]===a[1]||(n.push(e[i]),(r=n.length)>2&&un(n[r-3],n[r-1],n[r-2])&&n.splice(n.length-2,1))}if(n.push(e[e.length-1]),r=n.length,cn(e[0],e[e.length-1])&&r<4)throw new Error("invalid polygon");return un(n[r-3],n[r-1],n[r-2])&&n.splice(n.length-2,1),n}function cn(t,e){return t[0]===e[0]&&t[1]===e[1]}function un(t,e,n){var o=n[0],r=n[1],i=t[0],a=t[1],s=e[0],c=e[1],u=s-i,l=c-a;return 0===(o-i)*l-(r-a)*u&&(Math.abs(u)>=Math.abs(l)?u>0?i<=o&&o<=s:s<=o&&o<=i:l>0?a<=r&&r<=c:c<=r&&r<=a)}function ln(t){if(!t)throw new Error("geojson is required");switch(t.type){case"Feature":return dn(t);case"FeatureCollection":return function(t){var e={type:"FeatureCollection"};return Object.keys(t).forEach((function(n){switch(n){case"type":case"features":return;default:e[n]=t[n]}})),e.features=t.features.map((function(t){return dn(t)})),e}(t);case"Point":case"LineString":case"Polygon":case"MultiPoint":case"MultiLineString":case"MultiPolygon":case"GeometryCollection":return pn(t);default:throw new Error("unknown GeoJSON type")}}function dn(t){var e={type:"Feature"};return Object.keys(t).forEach((function(n){switch(n){case"type":case"properties":case"geometry":return;default:e[n]=t[n]}})),e.properties=function t(e){var n={};if(!e)return n;return Object.keys(e).forEach((function(o){var r=e[o];"object"==typeof r?null===r?n[o]=null:Array.isArray(r)?n[o]=r.map((function(t){return t})):n[o]=t(r):n[o]=r})),n}(t.properties),e.geometry=pn(t.geometry),e}function pn(t){var e={type:t.type};return t.bbox&&(e.bbox=t.bbox),"GeometryCollection"===t.type?(e.geometries=t.geometries.map((function(t){return pn(t)})),e):(e.coordinates=function t(e){var n=e;if("object"!=typeof n[0])return n.slice();return n.map((function(e){return t(e)}))}(t.coordinates),e)}function fn(t,e,n){if(null!==t)for(var o,r,i,a,s,c,u,l,d=0,p=0,f=t.type,h="FeatureCollection"===f,g="Feature"===f,m=h?t.features.length:1,y=0;y<m;y++){s=(l=!!(u=h?t.features[y].geometry:g?t.geometry:t)&&"GeometryCollection"===u.type)?u.geometries.length:1;for(var v=0;v<s;v++){var b=0,_=0;if(null!==(a=l?u.geometries[v]:u)){c=a.coordinates;var w=a.type;switch(d=!n||"Polygon"!==w&&"MultiPolygon"!==w?0:1,w){case null:break;case"Point":if(!1===e(c,p,y,b,_))return!1;p++,b++;break;case"LineString":case"MultiPoint":for(o=0;o<c.length;o++){if(!1===e(c[o],p,y,b,_))return!1;p++,"MultiPoint"===w&&b++}"LineString"===w&&b++;break;case"Polygon":case"MultiLineString":for(o=0;o<c.length;o++){for(r=0;r<c[o].length-d;r++){if(!1===e(c[o][r],p,y,b,_))return!1;p++}"MultiLineString"===w&&b++,"Polygon"===w&&_++}"Polygon"===w&&b++;break;case"MultiPolygon":for(o=0;o<c.length;o++){for(_=0,r=0;r<c[o].length;r++){for(i=0;i<c[o][r].length-d;i++){if(!1===e(c[o][r][i],p,y,b,_))return!1;p++}_++}b++}break;case"GeometryCollection":for(o=0;o<a.geometries.length;o++)if(!1===fn(a.geometries[o],e,n))return!1;break;default:throw new Error("Unknown Geometry Type")}}}}}function hn(t,e,n){var o=e.x,r=e.y,i=n.x-o,a=n.y-r;if(0!==i||0!==a){var s=((t.x-o)*i+(t.y-r)*a)/(i*i+a*a);s>1?(o=n.x,r=n.y):s>0&&(o+=i*s,r+=a*s)}return(i=t.x-o)*i+(a=t.y-r)*a}function gn(t,e){var n=t.length-1,o=[t[0]];return function t(e,n,o,r,i){for(var a,s=r,c=n+1;c<o;c++){var u=hn(e[c],e[n],e[o]);u>s&&(a=c,s=u)}s>r&&(a-n>1&&t(e,n,a,r,i),i.push(e[a]),o-a>1&&t(e,a,o,r,i))}(t,0,n,e,o),o.push(t[n]),o}function mn(t,e,n){if(t.length<=2)return t;var o=void 0!==e?e*e:1;return t=gn(t=n?t:function(t,e){for(var n,o,r,i,a,s=t[0],c=[s],u=1,l=t.length;u<l;u++)n=t[u],r=s,i=void 0,a=void 0,i=(o=n).x-r.x,a=o.y-r.y,i*i+a*a>e&&(c.push(n),s=n);return s!==n&&c.push(n),c}(t,o),o)}function yn(t,e){if(!on(e=e||{}))throw new Error("options is invalid");var n=void 0!==e.tolerance?e.tolerance:1,o=e.highQuality||!1,r=e.mutate||!1;if(!t)throw new Error("geojson is required");if(n&&n<0)throw new Error("invalid tolerance");return!0!==r&&(t=ln(t)),function(t,e){var n,o,r,i,a,s,c,u,l,d,p=0,f="FeatureCollection"===t.type,h="Feature"===t.type,g=f?t.features.length:1;for(n=0;n<g;n++){for(s=f?t.features[n].geometry:h?t.geometry:t,u=f?t.features[n].properties:h?t.properties:{},l=f?t.features[n].bbox:h?t.bbox:void 0,d=f?t.features[n].id:h?t.id:void 0,a=(c=!!s&&"GeometryCollection"===s.type)?s.geometries.length:1,r=0;r<a;r++)if(null!==(i=c?s.geometries[r]:s))switch(i.type){case"Point":case"LineString":case"MultiPoint":case"Polygon":case"MultiLineString":case"MultiPolygon":if(!1===e(i,p,u,l,d))return!1;break;case"GeometryCollection":for(o=0;o<i.geometries.length;o++)if(!1===e(i.geometries[o],p,u,l,d))return!1;break;default:throw new Error("Unknown Geometry Type")}else if(!1===e(null,p,u,l,d))return!1;p++}}(t,(function(t){!function(t,e,n){var o=t.type;if("Point"===o||"MultiPoint"===o)return t;!function(t,e){void 0===e&&(e={});var n="object"==typeof e?e.mutate:e;if(!t)throw new Error("geojson is required");var o=function(t,e){return"FeatureCollection"===t.type?"FeatureCollection":"GeometryCollection"===t.type?"GeometryCollection":"Feature"===t.type&&null!==t.geometry?t.geometry.type:t.type}(t),r=[];switch(o){case"LineString":r=sn(t);break;case"MultiLineString":case"Polygon":an(t).forEach((function(t){r.push(sn(t))}));break;case"MultiPolygon":an(t).forEach((function(t){var e=[];t.forEach((function(t){e.push(sn(t))})),r.push(e)}));break;case"Point":return t;case"MultiPoint":var i={};an(t).forEach((function(t){var e=t.join("-");Object.prototype.hasOwnProperty.call(i,e)||(r.push(t),i[e]=!0)}));break;default:throw new Error(o+" geometry not supported")}t.coordinates?!0===n&&(t.coordinates=r):!0===n?t.geometry.coordinates=r:$e({type:o,coordinates:r},t.properties,{bbox:t.bbox,id:t.id})}(t,!0);var r=t.coordinates;switch(o){case"LineString":t.coordinates=vn(r,e,n);break;case"MultiLineString":t.coordinates=r.map((function(t){return vn(t,e,n)}));break;case"Polygon":t.coordinates=bn(r,e,n);break;case"MultiPolygon":t.coordinates=r.map((function(t){return bn(t,e,n)}))}}(t,n,o)})),t}function vn(t,e,n){return mn(t.map((function(t){return{x:t[0],y:t[1],z:t[2]}})),e,n).map((function(t){return t.z?[t.x,t.y,t.z]:[t.x,t.y]}))}function bn(t,e,n){return t.map((function(t){var o=t.map((function(t){return{x:t[0],y:t[1]}}));if(o.length<4)throw new Error("invalid polygon");for(var r=mn(o,e,n).map((function(t){return[t.x,t.y]}));!_n(r);)r=mn(o,e-=.01*e,n).map((function(t){return[t.x,t.y]}));return r[r.length-1][0]===r[0][0]&&r[r.length-1][1]===r[0][1]||r.push(r[0]),r}))}function _n(t){return!(t.length<3)&&!(3===t.length&&t[2][0]===t[0][0]&&t[2][1]===t[0][1])}var wn=Object.assign({},Be);function xn(t,e,n){void 0===n&&(n={});var o=rn(t),r=rn(e),i=tn(r[1]-o[1]),a=tn(r[0]-o[0]),s=tn(o[1]),c=tn(r[1]),u=Math.pow(Math.sin(i/2),2)+Math.pow(Math.sin(a/2),2)*Math.cos(s)*Math.cos(c);return He(2*Math.atan2(Math.sqrt(u),Math.sqrt(1-u)),n.units)}function Cn(t,e,n,o){void 0===o&&(o={});var r=rn(t),i=tn(r[0]),a=tn(r[1]),s=tn(n),c=Qe(e,o.units),u=Math.asin(Math.sin(a)*Math.cos(c)+Math.cos(a)*Math.sin(c)*Math.cos(s));return Ye([Xe(i+Math.atan2(Math.sin(s)*Math.sin(c)*Math.cos(a),Math.cos(c)-Math.sin(a)*Math.sin(u))),Xe(u)],o.properties)}function Mn(t,e,n){void 0===n&&(n={});for(var o=n.steps||64,r=n.properties?n.properties:!Array.isArray(t)&&"Feature"===t.type&&t.properties?t.properties:{},i=[],a=0;a<o;a++)i.push(Cn(t,e,-360*a/o,n).geometry.coordinates);return i.push(i[0]),Ke([i],r)}wn.onSetup=function(){var t=this,e=this.newFeature({type:S,properties:{},geometry:{type:E,coordinates:[[]]}});return this.addFeature(e),this.clearSelectedFeatures(),setTimeout((function(){t.map&&t.map.dragPan&&t.map.dragPan.disable()}),0),this.updateUIClasses({mouse:b}),this.activateUIButton(M.POLYGON),this.setActionableState({trash:!0}),{polygon:e,currentVertexPosition:0,dragMoving:!1}},wn.onDrag=wn.onTouchMove=function(t,e){t.dragMoving=!0,this.updateUIClasses({mouse:b}),t.polygon.updateCoordinate("0."+t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat),t.currentVertexPosition++,t.polygon.updateCoordinate("0."+t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat)},wn.onMouseUp=function(t,e){t.dragMoving&&(this.simplify(t.polygon),this.fireUpdate(),this.changeMode(A.SIMPLE_SELECT,{featureIds:[t.polygon.id]}))},wn.onTouchEnd=function(t,e){this.onMouseUp(t,e)},wn.fireUpdate=function(){this.map.fire(N,{action:J,features:this.getSelected().map((function(t){return t.toGeoJSON()}))})},wn.simplify=function(t){yn(t,{mutate:!0,tolerance:1/Math.pow(1.05,10*this.map.getZoom()),highQuality:!0})};var Sn=function(t){setTimeout((function(){t.map&&t.map.doubleClickZoom&&t._ctx&&t._ctx.store&&t._ctx.store.getInitialConfigValue&&t._ctx.store.getInitialConfigValue("doubleClickZoom")&&t.map.doubleClickZoom.enable()}),0)},En=function(t){setTimeout((function(){t.map&&t.map.doubleClickZoom&&t.map.doubleClickZoom.disable()}),0)},Pn={onSetup:function(t){var e=this.newFeature({type:S,properties:{isCircle:!0,center:[]},geometry:{type:E,coordinates:[[]]}});return this.addFeature(e),this.clearSelectedFeatures(),En(this),this.updateUIClasses({mouse:b}),this.setActionableState({trash:!0}),{DRAW_CIRCLE:e}},onTap:function(t,e){t.startPoint&&this.onMouseMove(t,e),this.onClick(t,e)},onClick:function(t,e){t.startPoint&&t.startPoint[0]!==e.lngLat.lng&&t.startPoint[1]!==e.lngLat.lat&&(this.updateUIClasses({mouse:"pointer"}),t.endPoint=[e.lngLat.lng,e.lngLat.lat],this.changeMode("simple_select",{featuresId:t.DRAW_CIRCLE.id}));var n=[e.lngLat.lng,e.lngLat.lat];t.startPoint=n,0===t.DRAW_CIRCLE.properties.center.length&&(t.DRAW_CIRCLE.properties.center=[e.lngLat.lng,e.lngLat.lat])},onMouseMove:function(t,e){var n=t.DRAW_CIRCLE.properties.center;if(n.length>0){var o=xn(Ye(n),Ye([e.lngLat.lng,e.lngLat.lat]),{units:"kilometers"}),r=Mn(n,o);t.DRAW_CIRCLE.incomingCoords(r.geometry.coordinates),t.DRAW_CIRCLE.properties.radiusInKm=o}},onKeyUp:function(t,e){if(27===e.keyCode)return this.changeMode("simple_select")},onStop:function(t){Sn(this),this.updateUIClasses({mouse:"none"}),this.activateUIButton(),void 0!==this.getFeature(t.DRAW_CIRCLE.id)&&(t.DRAW_CIRCLE.removeCoordinate("0.4"),t.DRAW_CIRCLE.isValid()?this.map.fire("draw.create",{features:[t.DRAW_CIRCLE.toGeoJSON()]}):(this.deleteFeature([t.DRAW_CIRCLE.id],{silent:!0}),this.changeMode("simple_select",{},{silent:!0})))},toDisplayFeatures:function(t,e,n){var o=e.properties.id===t.DRAW_CIRCLE.id;return e.properties.active=o?"true":"false",o?t.startPoint?n(e):void 0:n(e)},onTrash:function(t){this.deleteFeature([t.DRAW_CIRCLE.id],{silent:!0}),this.changeMode("simple_select")}},Ln=function(t){setTimeout((function(){t.map&&t.map.dragPan&&t._ctx&&t._ctx.store&&t._ctx.store.getInitialConfigValue&&t._ctx.store.getInitialConfigValue("dragPan")&&t.map.dragPan.enable()}),0)},In=function(t){setTimeout((function(){t.map&&t.map.doubleClickZoom&&t.map.dragPan.disable()}),0)},kn=Object.assign({},Be);function Fn(t,e,n,o){void 0===o&&(o={});var r=e<0,i=en(Math.abs(e),o.units,"meters");r&&(i=-Math.abs(i));var a=rn(t),s=function(t,e,n,o){o=void 0===o?6371008.8:Number(o);var r=e/o,i=t[0]*Math.PI/180,a=tn(t[1]),s=tn(n),c=r*Math.cos(s),u=a+c;Math.abs(u)>Math.PI/2&&(u=u>0?Math.PI-u:-Math.PI-u);var l=Math.log(Math.tan(u/2+Math.PI/4)/Math.tan(a/2+Math.PI/4)),d=Math.abs(l)>1e-11?c/l:Math.cos(a),p=r*Math.sin(s)/d;return[(180*(i+p)/Math.PI+540)%360-180,180*u/Math.PI]}(a,i,n);return s[0]+=s[0]-a[0]>180?-360:a[0]-s[0]>180?360:0,Ye(s,o.properties)}function Tn(t,e){var n=tn(t[1]),o=tn(e[1]),r=tn(e[0]-t[0]);r>Math.PI&&(r-=2*Math.PI),r<-Math.PI&&(r+=2*Math.PI);var i=Math.log(Math.tan(o/2+Math.PI/4)/Math.tan(n/2+Math.PI/4));return(Xe(Math.atan2(r,i))+360)%360}function On(t,e,n){void 0===n&&(n={});var o=rn(t),r=rn(e);return r[0]+=r[0]-o[0]>180?-360:o[0]-r[0]>180?360:0,en(function(t,e,n){var o=n=void 0===n?6371008.8:Number(n),r=t[1]*Math.PI/180,i=e[1]*Math.PI/180,a=i-r,s=Math.abs(e[0]-t[0])*Math.PI/180;s>Math.PI&&(s-=2*Math.PI);var c=Math.log(Math.tan(i/2+Math.PI/4)/Math.tan(r/2+Math.PI/4)),u=Math.abs(c)>1e-11?a/c:Math.cos(r);return Math.sqrt(a*a+u*u*s*s)*o}(o,r),"meters",n.units)}function An(t,e,n){if(!on(n=n||{}))throw new Error("options is invalid");var o=n.pivot,r=n.mutate;if(!t)throw new Error("geojson is required");if(null==e||isNaN(e))throw new Error("angle is required");return 0===e||(o||(o=function(t,e){void 0===e&&(e={});var n=0,o=0,r=0;return fn(t,(function(t){n+=t[0],o+=t[1],r++}),!0),Ye([n/r,o/r],e.properties)}(t)),!1!==r&&void 0!==r||(t=ln(t)),fn(t,(function(t){var n=function(t,e,n){var o;return void 0===n&&(n={}),(o=n.final?Tn(rn(e),rn(t)):Tn(rn(t),rn(e)))>180?-(360-o):o}(o,t)+e,r=On(o,t),i=an(Fn(o,r,n));t[0]=i[0],t[1]=i[1]}))),t}function jn(t){var e=t*Math.PI/180;return Math.tan(e)}kn.onSetup=function(t){var e=this.newFeature({type:S,properties:{isCircle:!0,center:[]},geometry:{type:E,coordinates:[[0,0]]}});return this.addFeature(e),this.clearSelectedFeatures(),fe(this),In(this),this.updateUIClasses({mouse:b}),this.activateUIButton(M.POLYGON),this.setActionableState({trash:!0}),{polygon:e,currentVertexPosition:0}},kn.onMouseDown=kn.onTouchStart=function(t,e){0===t.polygon.properties.center.length&&(t.polygon.properties.center=[e.lngLat.lng,e.lngLat.lat])},kn.onDrag=kn.onMouseMove=kn.onTouchMove=function(t,e){var n=t.polygon.properties.center;if(n.length>0){var o=xn(Ye(n),Ye([e.lngLat.lng,e.lngLat.lat]),{units:"kilometers"}),r=Mn(n,o);t.polygon.incomingCoords(r.geometry.coordinates),t.polygon.properties.radiusInKm=o}},kn.onMouseUp=kn.onTouchEnd=function(t,e){return Ln(this),this.changeMode(A.SIMPLE_SELECT,{featureIds:[t.polygon.id]})},kn.onClick=kn.onTap=function(t,e){t.polygon.properties.center=[]},kn.toDisplayFeatures=function(t,e,n){var o=e.properties.id===t.polygon.id;return e.properties.active=o?Y:K,n(e)};var Dn=Object.assign({},Be);function Nn(t){var e=t.properties,n=t.geometry;if(!e.user_isCircle)return null;for(var o=[],r=n.coordinates[0].slice(0,-1),i=0;i<r.length;i+=Math.round(r.length/4))o.push(le(e.id,r[i],"0."+i,!1));return o}Dn.onSetup=function(t){var e=this.newFeature({type:S,properties:{isEllipse:!0,center:[],semiMajorAxis:0,semiMinorAxis:0,bearing:0},geometry:{type:E,coordinates:[[0,0]]}});return this.addFeature(e),this.clearSelectedFeatures(),fe(this),In(this),this.updateUIClasses({mouse:b}),this.activateUIButton(M.POLYGON),this.setActionableState({trash:!0}),{polygon:e,currentVertexPosition:0}},Dn.onMouseDown=Dn.onTouchStart=function(t,e){0===t.polygon.properties.center.length&&(t.polygon.properties.center=[e.lngLat.lng,e.lngLat.lat])},Dn.onDrag=Dn.onMouseMove=Dn.onTouchMove=function(t,e){var n=t.polygon.properties.center;if(0!==n.length){var o=Ye(n),r=(Ye([e.lngLat.lng,e.lngLat.lat]),Ye([e.lngLat.lng,n[1]])),i=Ye([n[0],e.lngLat.lat]),a=xn(o,r,{units:"kilometers"}),s=xn(o,i,{units:"kilometers"}),c=function(t,e,n,o){var r=(o=o||{}).steps||64,i=o.units||"kilometers",a=o.angle||0,s=o.pivot||t,c=o.properties||t.properties||{};if(!t)throw new Error("center is required");if(!e)throw new Error("xSemiAxis is required");if(!n)throw new Error("ySemiAxis is required");if(!on(o))throw new Error("options must be an object");if(!nn(r))throw new Error("steps must be a number");if(!nn(a))throw new Error("angle must be a number");var u=rn(t);if("degrees"===i)var l=tn(a);else e=Fn(t,e,90,{units:i}),n=Fn(t,n,0,{units:i}),e=rn(e)[0]-u[0],n=rn(n)[1]-u[1];for(var d=[],p=0;p<r;p+=1){var f=-360*p/r,h=e*n/Math.sqrt(Math.pow(n,2)+Math.pow(e,2)*Math.pow(jn(f),2)),g=e*n/Math.sqrt(Math.pow(e,2)+Math.pow(n,2)/Math.pow(jn(f),2));if(f<-90&&f>=-270&&(h=-h),f<-180&&f>=-360&&(g=-g),"degrees"===i){var m=h*Math.cos(l)+g*Math.sin(l),y=g*Math.cos(l)-h*Math.sin(l);h=m,g=y}d.push([h+u[0],g+u[1]])}return d.push(d[0]),"degrees"===i?Ke([d],c):An(Ke([d],c),a,{pivot:s})}(n,a,s,{bearing:0,units:"kilometers"});t.polygon.incomingCoords(c.geometry.coordinates),t.polygon.properties.semiMajorAxis=a,t.polygon.properties.semiMinorAxis=s,t.polygon.properties.bearing=0}},Dn.onMouseUp=Dn.onTouchEnd=function(t,e){return Ln(this),this.changeMode(A.SIMPLE_SELECT,{featureIds:[t.polygon.id]})},Dn.onClick=Dn.onTap=function(t,e){t.polygon.properties.center=[]},Dn.toDisplayFeatures=function(t,e,n){var o=e.properties.id===t.polygon.id;return e.properties.active=o?Y:K,e.properties.shapeType="ellipse",e.properties.semiMajorAxis=t.polygon.properties.semiMajorAxis,e.properties.semiMinorAxis=t.polygon.properties.semiMinorAxis,e.properties.bearing=0,n(e)};var Rn=De;Rn.dragMove=function(t,e){t.dragMoving=!0,e.originalEvent.stopPropagation();var n={lng:e.lngLat.lng-t.dragMoveLocation.lng,lat:e.lngLat.lat-t.dragMoveLocation.lat};je(this.getSelected(),n),this.getSelected().filter((function(t){return t.properties.isCircle})).map((function(t){return t.properties.center})).forEach((function(t){t[0]+=n.lng,t[1]+=n.lat})),t.dragMoveLocation=e.lngLat},Rn.toDisplayFeatures=function(t,e,n){(e.properties.active=this.isSelected(e.properties.id)?Y:K,n(e),this.fireActionable(),e.properties.active===Y&&e.geometry.type!==L)&&(e.properties.user_isCircle?Nn(e):de(e)).forEach(n)};var Un=Ue;Un.dragFeature=function(t,e,n){je(this.getSelected(),n),this.getSelected().filter((function(t){return t.properties.isCircle})).map((function(t){return t.properties.center})).forEach((function(t){t[0]+=n.lng,t[1]+=n.lat})),t.dragMoveLocation=e.lngLat},Un.dragVertex=function(t,e,n){if(t.feature.properties.isCircle){var o=t.feature.properties.center,r=[e.lngLat.lng,e.lngLat.lat],i=xn(Ye(o),Ye(r),{units:"kilometers"}),a=Mn(o,i);t.feature.incomingCoords(a.geometry.coordinates),t.feature.properties.radiusInKm=i}else for(var s=t.selectedCoordPaths.map((function(e){return t.feature.getCoordinate(e)})),c=Ae(s.map((function(t){return{type:S,properties:{},geometry:{type:L,coordinates:t}}})),n),u=0;u<s.length;u++){var l=s[u];t.feature.updateCoordinate(t.selectedCoordPaths[u],l[0]+c.lng,l[1]+c.lat)}},Un.toDisplayFeatures=function(t,e,n){t.featureId===e.properties.id?(e.properties.active=Y,n(e),(e.properties.user_isCircle?Nn(e):de(e,{map:this.map,midpoints:!0,selectedPaths:t.selectedCoordPaths})).forEach(n)):(e.properties.active=K,n(e));this.fireActionable(t)};var Vn={simple_select:Rn,direct_select:Un,draw_point:Ve,draw_polygon:Be,draw_line_string:ze,draw_rectangle:qe,draw_freehandpolygon:wn,draw_circle:Pn,drag_circle:kn,drag_ellipse:Dn},Gn={defaultMode:A.SIMPLE_SELECT,keybindings:!0,touchEnabled:!0,clickBuffer:2,touchBuffer:25,boxSelect:!0,displayControlsDefault:!0,styles:[{id:"gl-draw-polygon-fill-inactive",type:"fill",filter:["all",["==","active","false"],["==","$type","Polygon"],["!=","mode","static"]],paint:{"fill-color":"#3bb2d0","fill-outline-color":"#3bb2d0","fill-opacity":.1}},{id:"gl-draw-polygon-fill-active",type:"fill",filter:["all",["==","active","true"],["==","$type","Polygon"]],paint:{"fill-color":"#fbb03b","fill-outline-color":"#fbb03b","fill-opacity":.1}},{id:"gl-draw-polygon-midpoint",type:"circle",filter:["all",["==","$type","Point"],["==","meta","midpoint"]],paint:{"circle-radius":3,"circle-color":"#fbb03b"}},{id:"gl-draw-polygon-stroke-inactive",type:"line",filter:["all",["==","active","false"],["==","$type","Polygon"],["!=","mode","static"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#3bb2d0","line-width":2}},{id:"gl-draw-polygon-stroke-active",type:"line",filter:["all",["==","active","true"],["==","$type","Polygon"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#fbb03b","line-dasharray":[.2,2],"line-width":2}},{id:"gl-draw-line-inactive",type:"line",filter:["all",["==","active","false"],["==","$type","LineString"],["!=","mode","static"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#3bb2d0","line-width":2}},{id:"gl-draw-line-active",type:"line",filter:["all",["==","$type","LineString"],["==","active","true"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#fbb03b","line-dasharray":[.2,2],"line-width":2}},{id:"gl-draw-polygon-and-line-vertex-stroke-inactive",type:"circle",filter:["all",["==","meta","vertex"],["==","$type","Point"],["!=","mode","static"]],paint:{"circle-radius":5,"circle-color":"#fff"}},{id:"gl-draw-polygon-and-line-vertex-inactive",type:"circle",filter:["all",["==","meta","vertex"],["==","$type","Point"],["!=","mode","static"]],paint:{"circle-radius":3,"circle-color":"#fbb03b"}},{id:"gl-draw-point-point-stroke-inactive",type:"circle",filter:["all",["==","active","false"],["==","$type","Point"],["==","meta","feature"],["!=","mode","static"]],paint:{"circle-radius":5,"circle-opacity":1,"circle-color":"#fff"}},{id:"gl-draw-point-inactive",type:"circle",filter:["all",["==","active","false"],["==","$type","Point"],["==","meta","feature"],["!=","mode","static"]],paint:{"circle-radius":3,"circle-color":"#3bb2d0"}},{id:"gl-draw-point-stroke-active",type:"circle",filter:["all",["==","$type","Point"],["==","active","true"],["!=","meta","midpoint"]],paint:{"circle-radius":7,"circle-color":"#fff"}},{id:"gl-draw-point-active",type:"circle",filter:["all",["==","$type","Point"],["!=","meta","midpoint"],["==","active","true"]],paint:{"circle-radius":5,"circle-color":"#fbb03b"}},{id:"gl-draw-polygon-fill-static",type:"fill",filter:["all",["==","mode","static"],["==","$type","Polygon"]],paint:{"fill-color":"#404040","fill-outline-color":"#404040","fill-opacity":.1}},{id:"gl-draw-polygon-stroke-static",type:"line",filter:["all",["==","mode","static"],["==","$type","Polygon"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#404040","line-width":2}},{id:"gl-draw-line-static",type:"line",filter:["all",["==","mode","static"],["==","$type","LineString"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#404040","line-width":2}},{id:"gl-draw-point-static",type:"circle",filter:["all",["==","mode","static"],["==","$type","Point"]],paint:{"circle-radius":5,"circle-color":"#404040"}}],modes:Vn,controls:{},userProperties:!1},Bn={point:!0,line_string:!0,polygon:!0,trash:!0,combine_features:!0,uncombine_features:!0},zn={point:!1,line_string:!1,polygon:!1,trash:!1,combine_features:!1,uncombine_features:!1};function Jn(t,e){return t.map((function(t){return t.source?t:Bt(t,{id:t.id+"."+e,source:"hot"===e?y:v})}))}var Wn=ct((function(t,e){var n="[object Arguments]",o="[object Map]",r="[object Object]",i="[object Set]",a=/^\[object .+?Constructor\]$/,s=/^(?:0|[1-9]\d*)$/,c={};c["[object Float32Array]"]=c["[object Float64Array]"]=c["[object Int8Array]"]=c["[object Int16Array]"]=c["[object Int32Array]"]=c["[object Uint8Array]"]=c["[object Uint8ClampedArray]"]=c["[object Uint16Array]"]=c["[object Uint32Array]"]=!0,c[n]=c["[object Array]"]=c["[object ArrayBuffer]"]=c["[object Boolean]"]=c["[object DataView]"]=c["[object Date]"]=c["[object Error]"]=c["[object Function]"]=c[o]=c["[object Number]"]=c[r]=c["[object RegExp]"]=c[i]=c["[object String]"]=c["[object WeakMap]"]=!1;var u="object"==typeof global&&global&&global.Object===Object&&global,l="object"==typeof self&&self&&self.Object===Object&&self,d=u||l||Function("return this")(),p=e&&!e.nodeType&&e,f=p&&t&&!t.nodeType&&t,h=f&&f.exports===p,g=h&&u.process,m=function(){try{return g&&g.binding&&g.binding("util")}catch(t){}}(),y=m&&m.isTypedArray;function v(t,e){for(var n=-1,o=null==t?0:t.length;++n<o;)if(e(t[n],n,t))return!0;return!1}function b(t){var e=-1,n=Array(t.size);return t.forEach((function(t,o){n[++e]=[o,t]})),n}function _(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}var w,x,C,M=Array.prototype,S=Function.prototype,E=Object.prototype,P=d["__core-js_shared__"],L=S.toString,I=E.hasOwnProperty,k=(w=/[^.]+$/.exec(P&&P.keys&&P.keys.IE_PROTO||""))?"Symbol(src)_1."+w:"",F=E.toString,T=RegExp("^"+L.call(I).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),O=h?d.Buffer:void 0,A=d.Symbol,j=d.Uint8Array,D=E.propertyIsEnumerable,N=M.splice,R=A?A.toStringTag:void 0,U=Object.getOwnPropertySymbols,V=O?O.isBuffer:void 0,G=(x=Object.keys,C=Object,function(t){return x(C(t))}),B=mt(d,"DataView"),z=mt(d,"Map"),J=mt(d,"Promise"),W=mt(d,"Set"),q=mt(d,"WeakMap"),Z=mt(Object,"create"),$=_t(B),Y=_t(z),K=_t(J),H=_t(W),Q=_t(q),X=A?A.prototype:void 0,tt=X?X.valueOf:void 0;function et(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var o=t[e];this.set(o[0],o[1])}}function nt(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var o=t[e];this.set(o[0],o[1])}}function ot(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var o=t[e];this.set(o[0],o[1])}}function rt(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new ot;++e<n;)this.add(t[e])}function it(t){var e=this.__data__=new nt(t);this.size=e.size}function at(t,e){var n=Ct(t),o=!n&&xt(t),r=!n&&!o&&Mt(t),i=!n&&!o&&!r&&It(t),a=n||o||r||i,s=a?function(t,e){for(var n=-1,o=Array(t);++n<t;)o[n]=e(n);return o}(t.length,String):[],c=s.length;for(var u in t)!e&&!I.call(t,u)||a&&("length"==u||r&&("offset"==u||"parent"==u)||i&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||bt(u,c))||s.push(u);return s}function st(t,e){for(var n=t.length;n--;)if(wt(t[n][0],e))return n;return-1}function ct(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":R&&R in Object(t)?function(t){var e=I.call(t,R),n=t[R];try{t[R]=void 0;var o=!0}catch(t){}var r=F.call(t);o&&(e?t[R]=n:delete t[R]);return r}(t):function(t){return F.call(t)}(t)}function ut(t){return Lt(t)&&ct(t)==n}function lt(t,e,a,s,c){return t===e||(null==t||null==e||!Lt(t)&&!Lt(e)?t!=t&&e!=e:function(t,e,a,s,c,u){var l=Ct(t),d=Ct(e),p=l?"[object Array]":vt(t),f=d?"[object Array]":vt(e),h=(p=p==n?r:p)==r,g=(f=f==n?r:f)==r,m=p==f;if(m&&Mt(t)){if(!Mt(e))return!1;l=!0,h=!1}if(m&&!h)return u||(u=new it),l||It(t)?ft(t,e,a,s,c,u):function(t,e,n,r,a,s,c){switch(n){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!s(new j(t),new j(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return wt(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case o:var u=b;case i:var l=1&r;if(u||(u=_),t.size!=e.size&&!l)return!1;var d=c.get(t);if(d)return d==e;r|=2,c.set(t,e);var p=ft(u(t),u(e),r,a,s,c);return c.delete(t),p;case"[object Symbol]":if(tt)return tt.call(t)==tt.call(e)}return!1}(t,e,p,a,s,c,u);if(!(1&a)){var y=h&&I.call(t,"__wrapped__"),v=g&&I.call(e,"__wrapped__");if(y||v){var w=y?t.value():t,x=v?e.value():e;return u||(u=new it),c(w,x,a,s,u)}}if(!m)return!1;return u||(u=new it),function(t,e,n,o,r,i){var a=1&n,s=ht(t),c=s.length,u=ht(e).length;if(c!=u&&!a)return!1;var l=c;for(;l--;){var d=s[l];if(!(a?d in e:I.call(e,d)))return!1}var p=i.get(t);if(p&&i.get(e))return p==e;var f=!0;i.set(t,e),i.set(e,t);var h=a;for(;++l<c;){d=s[l];var g=t[d],m=e[d];if(o)var y=a?o(m,g,d,e,t,i):o(g,m,d,t,e,i);if(!(void 0===y?g===m||r(g,m,n,o,i):y)){f=!1;break}h||(h="constructor"==d)}if(f&&!h){var v=t.constructor,b=e.constructor;v==b||!("constructor"in t)||!("constructor"in e)||"function"==typeof v&&v instanceof v&&"function"==typeof b&&b instanceof b||(f=!1)}return i.delete(t),i.delete(e),f}(t,e,a,s,c,u)}(t,e,a,s,lt,c))}function dt(t){return!(!Pt(t)||function(t){return!!k&&k in t}(t))&&(St(t)?T:a).test(_t(t))}function pt(t){if(n=(e=t)&&e.constructor,o="function"==typeof n&&n.prototype||E,e!==o)return G(t);var e,n,o,r=[];for(var i in Object(t))I.call(t,i)&&"constructor"!=i&&r.push(i);return r}function ft(t,e,n,o,r,i){var a=1&n,s=t.length,c=e.length;if(s!=c&&!(a&&c>s))return!1;var u=i.get(t);if(u&&i.get(e))return u==e;var l=-1,d=!0,p=2&n?new rt:void 0;for(i.set(t,e),i.set(e,t);++l<s;){var f=t[l],h=e[l];if(o)var g=a?o(h,f,l,e,t,i):o(f,h,l,t,e,i);if(void 0!==g){if(g)continue;d=!1;break}if(p){if(!v(e,(function(t,e){if(a=e,!p.has(a)&&(f===t||r(f,t,n,o,i)))return p.push(e);var a}))){d=!1;break}}else if(f!==h&&!r(f,h,n,o,i)){d=!1;break}}return i.delete(t),i.delete(e),d}function ht(t){return function(t,e,n){var o=e(t);return Ct(t)?o:function(t,e){for(var n=-1,o=e.length,r=t.length;++n<o;)t[r+n]=e[n];return t}(o,n(t))}(t,kt,yt)}function gt(t,e){var n,o,r=t.__data__;return("string"==(o=typeof(n=e))||"number"==o||"symbol"==o||"boolean"==o?"__proto__"!==n:null===n)?r["string"==typeof e?"string":"hash"]:r.map}function mt(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return dt(n)?n:void 0}et.prototype.clear=function(){this.__data__=Z?Z(null):{},this.size=0},et.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},et.prototype.get=function(t){var e=this.__data__;if(Z){var n=e[t];return"__lodash_hash_undefined__"===n?void 0:n}return I.call(e,t)?e[t]:void 0},et.prototype.has=function(t){var e=this.__data__;return Z?void 0!==e[t]:I.call(e,t)},et.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=Z&&void 0===e?"__lodash_hash_undefined__":e,this},nt.prototype.clear=function(){this.__data__=[],this.size=0},nt.prototype.delete=function(t){var e=this.__data__,n=st(e,t);return!(n<0)&&(n==e.length-1?e.pop():N.call(e,n,1),--this.size,!0)},nt.prototype.get=function(t){var e=this.__data__,n=st(e,t);return n<0?void 0:e[n][1]},nt.prototype.has=function(t){return st(this.__data__,t)>-1},nt.prototype.set=function(t,e){var n=this.__data__,o=st(n,t);return o<0?(++this.size,n.push([t,e])):n[o][1]=e,this},ot.prototype.clear=function(){this.size=0,this.__data__={hash:new et,map:new(z||nt),string:new et}},ot.prototype.delete=function(t){var e=gt(this,t).delete(t);return this.size-=e?1:0,e},ot.prototype.get=function(t){return gt(this,t).get(t)},ot.prototype.has=function(t){return gt(this,t).has(t)},ot.prototype.set=function(t,e){var n=gt(this,t),o=n.size;return n.set(t,e),this.size+=n.size==o?0:1,this},rt.prototype.add=rt.prototype.push=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},rt.prototype.has=function(t){return this.__data__.has(t)},it.prototype.clear=function(){this.__data__=new nt,this.size=0},it.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},it.prototype.get=function(t){return this.__data__.get(t)},it.prototype.has=function(t){return this.__data__.has(t)},it.prototype.set=function(t,e){var n=this.__data__;if(n instanceof nt){var o=n.__data__;if(!z||o.length<199)return o.push([t,e]),this.size=++n.size,this;n=this.__data__=new ot(o)}return n.set(t,e),this.size=n.size,this};var yt=U?function(t){return null==t?[]:(t=Object(t),function(t,e){for(var n=-1,o=null==t?0:t.length,r=0,i=[];++n<o;){var a=t[n];e(a,n,t)&&(i[r++]=a)}return i}(U(t),(function(e){return D.call(t,e)})))}:function(){return[]},vt=ct;function bt(t,e){return!!(e=null==e?9007199254740991:e)&&("number"==typeof t||s.test(t))&&t>-1&&t%1==0&&t<e}function _t(t){if(null!=t){try{return L.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function wt(t,e){return t===e||t!=t&&e!=e}(B&&"[object DataView]"!=vt(new B(new ArrayBuffer(1)))||z&&vt(new z)!=o||J&&"[object Promise]"!=vt(J.resolve())||W&&vt(new W)!=i||q&&"[object WeakMap]"!=vt(new q))&&(vt=function(t){var e=ct(t),n=e==r?t.constructor:void 0,a=n?_t(n):"";if(a)switch(a){case $:return"[object DataView]";case Y:return o;case K:return"[object Promise]";case H:return i;case Q:return"[object WeakMap]"}return e});var xt=ut(function(){return arguments}())?ut:function(t){return Lt(t)&&I.call(t,"callee")&&!D.call(t,"callee")},Ct=Array.isArray;var Mt=V||function(){return!1};function St(t){if(!Pt(t))return!1;var e=ct(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}function Et(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}function Pt(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function Lt(t){return null!=t&&"object"==typeof t}var It=y?function(t){return function(e){return t(e)}}(y):function(t){return Lt(t)&&Et(t.length)&&!!c[ct(t)]};function kt(t){return null!=(e=t)&&Et(e.length)&&!St(e)?at(t):pt(t);var e}t.exports=function(t,e){return lt(t,e)}}));var qn={Polygon:ft,LineString:pt,Point:dt,MultiPolygon:mt,MultiLineString:mt,MultiPoint:mt};function Zn(t,e){return e.modes=A,e.getFeatureIdsAt=function(e){return nt.click({point:e},null,t).map((function(t){return t.properties.id}))},e.getSelectedIds=function(){return t.store.getSelectedIds()},e.getSelected=function(){return{type:I,features:t.store.getSelectedIds().map((function(e){return t.store.get(e)})).map((function(t){return t.toGeoJSON()}))}},e.getSelectedPoints=function(){return{type:I,features:t.store.getSelectedCoordinates().map((function(t){return{type:S,properties:{},geometry:{type:L,coordinates:t.coordinates}}}))}},e.set=function(n){if(void 0===n.type||n.type!==I||!Array.isArray(n.features))throw new Error("Invalid FeatureCollection");var o=t.store.createRenderBatch(),r=t.store.getAllIds().slice(),i=e.add(n),a=new tt(i);return(r=r.filter((function(t){return!a.has(t)}))).length&&e.delete(r),o(),i},e.add=function(e){var n=JSON.parse(JSON.stringify(he(e))).features.map((function(e){if(e.id=e.id||ut(),null===e.geometry)throw new Error("Invalid geometry: null");if(void 0===t.store.get(e.id)||t.store.get(e.id).type!==e.geometry.type){var n=qn[e.geometry.type];if(void 0===n)throw new Error("Invalid geometry type: "+e.geometry.type+".");var o=new n(t,e);t.store.add(o)}else{var r=t.store.get(e.id);r.properties=e.properties,Wn(r.getCoordinates(),e.geometry.coordinates)||r.incomingCoords(e.geometry.coordinates)}return e.id}));return t.store.render(),n},e.get=function(e){var n=t.store.get(e);if(n)return n.toGeoJSON()},e.getAll=function(){return{type:I,features:t.store.getAll().map((function(t){return t.toGeoJSON()}))}},e.delete=function(n){return t.store.delete(n,{silent:!0}),e.getMode()!==A.DIRECT_SELECT||t.store.getSelectedIds().length?t.store.render():t.events.changeMode(A.SIMPLE_SELECT,void 0,{silent:!0}),Ht(),t._deleteFeatureCallback&&t._deleteFeatureCallback([n].flat()),e},e.deleteAll=function(){return t.store.delete(t.store.getAllIds(),{silent:!0}),e.getMode()===A.DIRECT_SELECT?t.events.changeMode(A.SIMPLE_SELECT,void 0,{silent:!0}):t.store.render(),Ht(),t._deleteFeatureCallback&&t._deleteFeatureCallback(["*"]),e},e.changeMode=function(n,o){return void 0===o&&(o={}),n===A.SIMPLE_SELECT&&e.getMode()===A.SIMPLE_SELECT?(r=o.featureIds||[],i=t.store.getSelectedIds(),r.length===i.length&&JSON.stringify(r.map((function(t){return t})).sort())===JSON.stringify(i.map((function(t){return t})).sort())||(t.store.setSelected(o.featureIds,{silent:!0}),t.store.render()),e):(n===A.DIRECT_SELECT&&e.getMode()===A.DIRECT_SELECT&&o.featureId===t.store.getSelectedIds()[0]||t.events.changeMode(n,o,{silent:!0}),e);var r,i},e.getMode=function(){return t.events.getMode()},e.trash=function(){return t.events.trash({silent:!0}),e},e.combineFeatures=function(){return t.events.combineFeatures({silent:!0}),e},e.uncombineFeatures=function(){return t.events.uncombineFeatures({silent:!0}),e},e.setFeatureProperty=function(n,o,r){return t.store.setFeatureProperty(n,o,r),e},e}!function(t,e){void 0===e&&(e={});var n=e.insertAt;if(t&&"undefined"!=typeof document){var o=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css","top"===n&&o.firstChild?o.insertBefore(r,o.firstChild):o.appendChild(r),r.styleSheet?r.styleSheet.cssText=t:r.appendChild(document.createTextNode(t))}}('\r\n/* Override default control style */\r\n.mapbox-gl-draw_ctrl-bottom-left,\r\n.mapbox-gl-draw_ctrl-top-left {\r\n  margin-left:0;\r\n  border-radius:0 4px 4px 0;\r\n}\r\n.mapbox-gl-draw_ctrl-top-right,\r\n.mapbox-gl-draw_ctrl-bottom-right {\r\n  margin-right:0;\r\n  border-radius:4px 0 0 4px;\r\n}\r\n.mapbox-gl-draw_ctrl-draw {\r\n  background-color:rgba(0,0,0,0.75);\r\n  border-color:rgba(0,0,0,0.9);\r\n}\r\n.mapbox-gl-draw_ctrl-draw > button {\r\n  border-color:rgba(0,0,0,0.9);\r\n  color:rgba(255,255,255,0.5);\r\n  width:30px;\r\n  height:30px;\r\n}\r\n.mapbox-gl-draw_ctrl-draw > button:hover {\r\n  background-color:rgba(0,0,0,0.85);\r\n  color:rgba(255,255,255,0.75);\r\n}\r\n.mapbox-gl-draw_ctrl-draw > button.active,\r\n.mapbox-gl-draw_ctrl-draw > button.active:hover {\r\n  background-color:rgba(0,0,0,0.95);\r\n  color:#fff;\r\n}\r\n.mapbox-gl-draw_ctrl-draw-btn {\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n}\r\n\r\n.mapbox-gl-draw_point {\r\n  background-image: url(\'data:image/svg+xml;utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="20" height="20">%3Cpath d="m10 2c-3.3 0-6 2.7-6 6s6 9 6 9 6-5.7 6-9-2.7-6-6-6zm0 2c2.1 0 3.8 1.7 3.8 3.8 0 1.5-1.8 3.9-2.9 5.2h-1.7c-1.1-1.4-2.9-3.8-2.9-5.2-.1-2.1 1.6-3.8 3.7-3.8z"/>%3C/svg>\');\r\n}\r\n.mapbox-gl-draw_polygon {\r\n  background-image: url(\'data:image/svg+xml;utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="20" height="20">%3Cpath d="m15 12.3v-4.6c.6-.3 1-1 1-1.7 0-1.1-.9-2-2-2-.7 0-1.4.4-1.7 1h-4.6c-.3-.6-1-1-1.7-1-1.1 0-2 .9-2 2 0 .7.4 1.4 1 1.7v4.6c-.6.3-1 1-1 1.7 0 1.1.9 2 2 2 .7 0 1.4-.4 1.7-1h4.6c.3.6 1 1 1.7 1 1.1 0 2-.9 2-2 0-.7-.4-1.4-1-1.7zm-8-.3v-4l1-1h4l1 1v4l-1 1h-4z"/>%3C/svg>\');\r\n}\r\n.mapbox-gl-draw_line {\r\n  background-image: url(\'data:image/svg+xml;utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="20" height="20">%3Cpath d="m13.5 3.5c-1.4 0-2.5 1.1-2.5 2.5 0 .3 0 .6.2.9l-3.8 3.8c-.3-.1-.6-.2-.9-.2-1.4 0-2.5 1.1-2.5 2.5s1.1 2.5 2.5 2.5 2.5-1.1 2.5-2.5c0-.3 0-.6-.2-.9l3.8-3.8c.3.1.6.2.9.2 1.4 0 2.5-1.1 2.5-2.5s-1.1-2.5-2.5-2.5z"/>%3C/svg>\');\r\n}\r\n.mapbox-gl-draw_trash {\r\n  background-image: url(\'data:image/svg+xml;utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="20" height="20">%3Cpath d="M10,3.4 c-0.8,0-1.5,0.5-1.8,1.2H5l-1,1v1h12v-1l-1-1h-3.2C11.5,3.9,10.8,3.4,10,3.4z M5,8v7c0,1,1,2,2,2h6c1,0,2-1,2-2V8h-2v5.5h-1.5V8h-3 v5.5H7V8H5z"/>%3C/svg>\');\r\n}\r\n.mapbox-gl-draw_uncombine {\r\n  background-image: url(\'data:image/svg+xml;utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="20" height="20">%3Cpath d="m12 2c-.3 0-.5.1-.7.3l-1 1c-.4.4-.4 1 0 1.4l1 1c.4.4 1 .4 1.4 0l1-1c.4-.4.4-1 0-1.4l-1-1c-.2-.2-.4-.3-.7-.3zm4 4c-.3 0-.5.1-.7.3l-1 1c-.4.4-.4 1 0 1.4l1 1c.4.4 1 .4 1.4 0l1-1c.4-.4.4-1 0-1.4l-1-1c-.2-.2-.4-.3-.7-.3zm-7 1c-1 0-1 1-.5 1.5.3.3 1 1 1 1l-1 1s-.5.5 0 1 1 0 1 0l1-1 1 1c.5.5 1.5.5 1.5-.5v-4zm-5 3c-.3 0-.5.1-.7.3l-1 1c-.4.4-.4 1 0 1.4l4.9 4.9c.4.4 1 .4 1.4 0l1-1c.4-.4.4-1 0-1.4l-4.9-4.9c-.1-.2-.4-.3-.7-.3z"/>%3C/svg>\');\r\n}\r\n.mapbox-gl-draw_combine {\r\n  background-image: url(\'data:image/svg+xml;utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="20" height="20">%3Cpath d="M12.1,2c-0.3,0-0.5,0.1-0.7,0.3l-1,1c-0.4,0.4-0.4,1,0,1.4l4.9,4.9c0.4,0.4,1,0.4,1.4,0l1-1 c0.4-0.4,0.4-1,0-1.4l-4.9-4.9C12.6,2.1,12.3,2,12.1,2z M8,8C7,8,7,9,7.5,9.5c0.3,0.3,1,1,1,1l-1,1c0,0-0.5,0.5,0,1s1,0,1,0l1-1l1,1 C11,13,12,13,12,12V8H8z M4,10c-0.3,0-0.5,0.1-0.7,0.3l-1,1c-0.4,0.4-0.4,1,0,1.4l1,1c0.4,0.4,1,0.4,1.4,0l1-1c0.4-0.4,0.4-1,0-1.4 l-1-1C4.5,10.1,4.3,10,4,10z M8,14c-0.3,0-0.5,0.1-0.7,0.3l-1,1c-0.4,0.4-0.4,1,0,1.4l1,1c0.4,0.4,1,0.4,1.4,0l1-1 c0.4-0.4,0.4-1,0-1.4l-1-1C8.5,14.1,8.3,14,8,14z"/>%3C/svg>\');\r\n}\r\n\r\n.mapboxgl-map.mouse-pointer .mapboxgl-canvas-container.mapboxgl-interactive {\r\n  cursor: pointer;\r\n}\r\n.mapboxgl-map.mouse-move .mapboxgl-canvas-container.mapboxgl-interactive {\r\n  cursor: move;\r\n}\r\n.mapboxgl-map.mouse-add .mapboxgl-canvas-container.mapboxgl-interactive {\r\n  cursor: crosshair;\r\n}\r\n.mapboxgl-map.mouse-move.mode-direct_select .mapboxgl-canvas-container.mapboxgl-interactive {\r\n  cursor: grab;\r\n  cursor: -moz-grab;\r\n  cursor: -webkit-grab;\r\n}\r\n.mapboxgl-map.mode-direct_select.feature-vertex.mouse-move .mapboxgl-canvas-container.mapboxgl-interactive {\r\n  cursor: move;\r\n}\r\n.mapboxgl-map.mode-direct_select.feature-midpoint.mouse-pointer .mapboxgl-canvas-container.mapboxgl-interactive {\r\n  cursor: cell;\r\n}\r\n.mapboxgl-map.mode-direct_select.feature-feature.mouse-move .mapboxgl-canvas-container.mapboxgl-interactive {\r\n  cursor: move;\r\n}\r\n.mapboxgl-map.mode-static.mouse-pointer  .mapboxgl-canvas-container.mapboxgl-interactive {\r\n  cursor: grab;\r\n  cursor: -moz-grab;\r\n  cursor: -webkit-grab;\r\n}\r\n\r\n.mapbox-gl-draw_boxselect {\r\n    pointer-events: none;\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 0;\r\n    height: 0;\r\n    background: rgba(0,0,0,.1);\r\n    border: 2px dotted #fff;\r\n    opacity: 0.5;\r\n}\r\n');var $n=function(t,e){var n={options:t=function(t){void 0===t&&(t={});var e=Bt(t);return t.controls||(e.controls={}),!1===t.displayControlsDefault?e.controls=Bt(zn,t.controls):e.controls=Bt(Bn,t.controls),(e=Bt(Gn,e)).styles=Jn(e.styles,"cold").concat(Jn(e.styles,"hot")),e}(t)};e=Zn(n,e),n.api=e;var o=Qt(n);return e.onAdd=o.onAdd,e.onRemove=o.onRemove,e.types=M,e.options=t,e};function Yn(t){void 0===t&&(t={}),t.hasOwnProperty("displayControlsDefault")||(t.displayControlsDefault=!1),$n(t,this)}return Yn.modes=Vn,Yn}));
//# sourceMappingURL=draw.min.js.map
