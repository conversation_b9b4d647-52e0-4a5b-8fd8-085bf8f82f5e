import request from '@/router/axios'

// 课程管理
export const queryCourseList = (data) => {
    return request({
        url: '/fleet/courseManager/selectPage',
        method: 'post',
        data,
    })
}

export const addCourse = (data) => {
    return request({
        url: '/fleet/courseManager/create',
        method: 'post',
        data,
    })
}

export const updateCourse = (data) => {
    return request({
        url: '/fleet/courseManager/update',
        method: 'post',
        data,
    })
}

export const updateCourseStatus = (data) => {
    return request({
        url: '/fleet/courseManager/updateStatus',
        method: 'post',
        data,
    })
}

export const queryCourseById = (data) => {
    return request({
        url: '/fleet/courseManager/selectById',
        method: 'post',
        data,
    })
}

export const delCourse = (data) => {
    return request({
        url: '/fleet/courseManager/delete',
        method: 'post',
        data,
    })
}

// 试卷管理
export const queryTestList = (data) => {
    return request({
        url: '/fleet/itemBankManager/selectPage',
        method: 'post',
        data,
    })
}

export const addTest = (data) => {
    return request({
        url: '/fleet/itemBankManager/create',
        method: 'post',
        data,
    })
}

export const updateTest = (data) => {
    return request({
        url: '/fleet/itemBankManager/update',
        method: 'post',
        data,
    })
}

export const updateTestStatus = (data) => {
    return request({
        url: '/fleet/itemBankManager/updateStatus',
        method: 'post',
        data,
    })
}

export const queryTestById = (data) => {
    return request({
        url: '/fleet/itemBankManager/selectById',
        method: 'post',
        data,
    })
}

export const delTest = (data) => {
    return request({
        url: '/fleet/itemBankManager/delete',
        method: 'post',
        data,
    })
}

// 培训管理
export const queryTrainList = (data) => {
    return request({
        url: '/fleet/trainingManager/selectPage',
        method: 'post',
        data,
    })
}

export const addTrain = (data) => {
    return request({
        url: '/fleet/trainingManager/create',
        method: 'post',
        data,
    })
}

export const updateTrain = (tempKey, data) => {
    return request({
        url: `/fleet/trainingManager/update/${tempKey}`,
        method: 'post',
        data,
    })
}

export const updateTrainStatus = (data) => {
    return request({
        url: '/fleet/trainingManager/updateStatus',
        method: 'post',
        data,
    })
}

export const addTrainingObject = (data) => {
    return request({
        url: '/fleet/trainingManager/addTrainingObject',
        method: 'post',
        data,
    })
}

export const queryTrainById = (data) => {
    return request({
        url: '/fleet/trainingManager/selectById',
        method: 'post',
        data,
    })
}

export const delTrain = (data) => {
    return request({
        url: '/fleet/trainingManager/delete',
        method: 'post',
        data,
    })
}

export const preUpdateTrain = (trainingId) => {
    return request({
        url: `/fleet/trainingManager/preUpdate/${trainingId}`,
        method: 'post',
    })
}

export const exitUpdateTrain = (trainingId, tempKey) => {
    return request({
        url: `/fleet/trainingManager/exitUpdate/${trainingId}/${tempKey}`,
        method: 'post',
    })
}

//车辆列表接口
export function queryDriverList(data) {
    return request({
        url: '/ztDriver/queryDriverList',
        method: 'post',
        data,
    })
}

// 查询顺丰自营司机名单
export function queryOwnerDriverList(data) {
    return request({
        url: '/ztDriver/queryOwnerDriverList',
        method: 'post',
        data,
    })
}

// 下发业务助手提醒
export function issueRemind(data) {
    return request({
        url: '/fleet/driverExam/issueRemind',
        method: 'post',
        data,
    })
}

export const exportDriverExamDetailURL = '/fleet/driverExam/exportDetail'

// 导出司机考试详情
export function exportDriverExamDetail(data) {
    return request({
        url: '/fleet/driverExam/exportDetail',
        method: 'post',
        data,
    })
}

// 任务完成时查看报告
export function queryExportReport(data) {
    return request({
        url: '/fleet/trainingManager/exportReport',
        method: 'post',
        data,
    })
}

// 培训看板
export function getTrainDashboardData(data) {
    return request({
        url: '/fleet/trainBoard/getTrainingBoardByOrgCode',
        method: 'post',
        data,
    })
}
