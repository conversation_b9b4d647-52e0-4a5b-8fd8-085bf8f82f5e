import request from '@/router/axios'

// 司机信息管理
export function queryDriverInfoPage(obj) {
    return request({
        url: '/soc/driver/page',
        method: 'get',
        params: obj,
    })
}

export function exportDriver(data) {
    return request({
        url: '/soc/driver/exportDriver',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer',
    })
}

// 下载司机导入模板
export function importDriverTemplate() {
    return request({
        url: '/soc/driver/template',
        method: 'post',
    })
}

export function driverRemove(id) {
    return request({
        url: `/soc/driver/remove/${id}`,
        method: 'delete',
    })
}
export function httpDetail(id) {
    return request({
        url: ` /soc/driver/${id}`,
        method: 'get',
    })
}
export function httpUpdate(data) {
    return request({
        url: `/soc/driver`,
        method: 'put',
        data,
    })
}
