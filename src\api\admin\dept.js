/*
 *    Copyright (c) 2018-2025, cloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloud
 */

import request from '@/router/axios'

export function fetchTree(query) {
  return request({
    url: '/admin/dept/tree',
    method: 'get',
    params: query
  })
}

export function fetchUserTree() {
    return request({
        url: 'admin/dept/userTree',
        method: 'get',
    })
}


//获取一级客户
export function getPrimaryData() {
  return request({
    url: '/admin/dept/getPrimaryData',
    method: 'get'
  });
};

export function addObj(obj) {
  return request({
    url: '/admin/dept/',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/admin/dept/getDeptById/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/admin/dept/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/admin/dept/',
    method: 'put',
    data: obj
  })
}

export function getRegionListByLevel(query) {
  return request({
    url: '/admin/region/get',
    method: 'get',
    params: query
  })
}

/// data: { dataCode, id? }
export function exportObj(data) {
  return request({
    url: '/admin/dept/export',
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 导出
export function exportDept(data) {
  return request({
      url: '/admin/dept/export',
      method: 'post',
      data,
      timeout: 1000000,
      responseType: 'arraybuffer'
  })
}


// 查询车管联系方式
export function getCarAdminContact(query) {
  return request({
    url: "/platform/defend/queryManagerList",
    method: "get",
    params: query,
  });
}

