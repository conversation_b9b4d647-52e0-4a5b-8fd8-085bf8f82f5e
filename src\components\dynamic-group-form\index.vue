<template>
    <div class="dynamic">
        <template>
            <span class="title" v-if="label">{{ label }}:&nbsp;&nbsp;</span>
            <el-button v-if="!readonly && addBtn" class="title" size="small" @click="$emit('save')">
                保存
            </el-button>
        </template>

        <div v-for="(item, index) in formItems" :key="item.id">
            <el-row>
                <el-col :span="formSpan">
                    <slot :index="index" :item="item"></slot>
                </el-col>
                <el-col :span="optSpan" v-if="!readonly" class="opt">
                    <el-button
                        :size="size"
                        icon="el-icon-plus"
                        circle
                        @click="onAddNextItem(index)"
                    ></el-button>
                </el-col>
                <el-col :span="optSpan" v-if="!readonly" class="opt">
                    <el-button
                        :size="size"
                        type="danger"
                        icon="el-icon-delete"
                        circle
                        @click="onDelNextItem(item.id)"
                    ></el-button>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script>
import isFunction from 'lodash-es/isFunction';
export default {
    name: "dynamic-goup-from",
    props: {
        label: {
            type: String,
            default: "",
        },
        addBtn: {
            type: Boolean,
            default: true,
        },
        value: {
            type: Array,
            default: () => [],
        },
        defaultValue: {
            type: Object | Function,
            default: () => {},
        },
        readonly: {
            type: Boolean,
            default: false,
        },
        size: {
            type: String,
            default: 'medium',
        },
        optSpan: {
            type: Number,
            default: 2
        },
        formSpan: {
            type: Number,
            default: 20
        }
    },
    computed: {
        formItems() {
            // 建立组件唯一标识
            return this.value.map((item, index) => ({
                id: `${Date.now()}${index}`,
                ...item,
            }));
        },
    },
    methods: {
        onAddNextItem(index) {
            const defaultValue = isFunction(this.defaultValue) ? this.defaultValue() : this.defaultValue;
            const addItem = { id: Date.now(), ...defaultValue };
            const newVal = [...this.formItems];
            newVal.splice(index + 1, 0, addItem);
            this.$emit("input", newVal);
        },
        onDelNextItem(id) {
            const isOnlyOne = this.formItems.length < 2;
            const newVal = isOnlyOne
                ? [{ id: Date.now(), ...this.defaultValue }]
                : this.formItems.filter((item) => item.id !== id);
            this.$emit("input", newVal);
        },
    },
};
</script>

<style lang="scss" scoped>
.dynamic {
    margin-left: 24px;
}

.title {
    margin-bottom: 20px;
}

.opt {
    text-align: center;
}
</style>>