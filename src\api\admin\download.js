import request from '@/router/axios'

/**
 * 新增下载任务
 * @param {Object} params - 请求参数
 * @returns {Promise} - 响应Promise
 */
export function addDownloadTask(params) {
  return request({
    url: '/erp/Download/export',
    method: 'post',
    data: params
  })
}

/**
 * 取消下载任务
 * @param {Object} params - 请求参数，包含id
 * @returns {Promise} - 响应Promise
 */
export function cancelDownloadTask(params) {
  return request({
    url: '/erp/Download/cancelTask',
    method: 'get',
    params
  })
}

/**
 * 下载文件
 * @param {Object} params - 请求参数，包含id
 * @returns {Promise} - 响应Promise
 */
export function getDownloadFile(params) {
  return request({
    url: '/erp/Download/getFile',
    method: 'get',
    params
  })
}

/**
 * 分页查询下载任务
 * @param {Object} params - 请求参数
 * @returns {Promise} - 响应Promise
 */
export function getDownloadPage(params) {
  return request({
    url: '/erp/Download/page',
    method: 'get',
    params
  })
}