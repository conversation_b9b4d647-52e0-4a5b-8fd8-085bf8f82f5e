<template>
  <div
    :class="[block ? 'basic-container--block' : '' , extraClass]"
    class="basic-container">
    <el-card>
      <slot/>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'BasicContainer',
  props: {
    block: {
      type: Boolean,
      default: false
    },
    extraClass: String,
  }
}
</script>

<style lang="scss">
.basic-container {
  border-radius: 10px;
  box-sizing: border-box;
  margin: 0px !important;
  background: #fff !important;
  overflow: auto;
  .el-card {
    width: 100%;
  }
  &:first-child {
    padding-top: 0;
  }
  &--block {
    height: 100%;
    .el-card {
      height: 100%;
    }
  }
}
</style>
