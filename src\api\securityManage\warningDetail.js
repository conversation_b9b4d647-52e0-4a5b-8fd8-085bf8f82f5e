import request from '@/router/axios'

export function fetchWarningDetailData(data) { // 报警明细
  return request({
    url: '/fleet/alarm/queryAlarmPage',
    method: 'post',
    data
  })
}
export function fetchPTypeDict(query) { // 报警大类
  return request({
    url: '/admin/dict/type/alarm_type',
    method: 'get',
    params: query
  })
}
export function fetchSubTypeDict(query, key) { // 报警小类
  return request({
    url: `/fleet/tcalarmrule/getSubType/${key}`,
    method: 'get',
    params: query
  })
}
export function fetchInterveneStatusDict(query) { // 护航状态
  return request({
    url: '/admin/dict/type/intervene_status_type',
    method: 'get',
    params: query
  })
}
export function exportAlarmMeddle(data) {
  return request({
    url: '/fleet/alarm/exportAlarm',
    method: 'post',
    data,
    timeout: 1000000,
    responseType: 'arraybuffer'
  })
}

//手动获取附件
export function queryAlarmMediaByRemote(obj) {
  return request({
    url: '/fleet/alarm/queryAlarmMediaByRemote',
    method: 'get',
    params: obj
  })
}

// GET /alarm/getRecordAudio 查询打电话录音附件
// 参数：recordUrl，传processLogs中的audio
// 成功示例：
// {
// "code": 0,
// "msg": null,
// "data": "http://obs-deepview-app.obs.cn-north-4.myhuaweicloud.com:80/soc-test/FOP_IPCC_RECORD_20230111/C0CD47571DB963294E7E5FC7DE753487.mp3?AccessKeyId=D1PU5T6WXTM1YDMGGC9J&Expires=1673524476&Signature=FJUubGmyT7jJl59oU4NY6jwJsJg%3D"
// }
// 失败示例：
// {
// "code": 1,
// "msg": "录音文件暂未回传，请稍后再试",
// "data": null
// }
export function alarm_getRecordAudio(params) {
  return request({
    url: '/fleet/alarm/getRecordAudio',
    method: 'get',
    params
  })
}

// GET /alarm/queryCompareVideo 请求对比视频
// 参数：
// alarmId：报警id
// startDate: 报警开始时间yyyy-MM-dd HH:mm:ss
// 返回示例：
// 成功
// {
// "code": 0,
// "msg": null,
// "data": {
// "workOrderId": "IN-20230111-I438041J24",
// "videoPath": null,
// "bucket": null,
// "status": 1
// }
// }
// 失败，结果为空：
// {
// "code": 1,
// "msg": "查询数据为空",
// "data": null
// }
export function alarm_queryCompareVideo(params) {
  return request({
    url: '/fleet/alarm/queryCompareVideo',
    method: 'get',
    params
  })
}
