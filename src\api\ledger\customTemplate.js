import request from '@/router/axios'

export function addFocusDriverPost(data) {
    return request({
        url: '/fleet/anjiPost/addPostNew',
        method: 'post',
        data
      })
}


export function editFocusDriverPost(data) {
    return request({
        url: '/fleet/anjiPost/editPostNew',
        method: 'post',
        data
      })
}

export function queryDriverPostId(params) {
    return request({
        url: '/fleet/ledger/queryByPostId',
        method: 'get',
        params
      })
}