import request from '@/router/axios'

export function getLedgerTemplateList(data) {
    return request({
        url: '/fleet/ledgertemplate/gettemplatelist',
        method: 'post',
        data,
    })
}

export function haveTemplate(data) {
    return request({
        url: '/fleet/ledgertemplate/havetemplate',
        method: 'post',
        data,
    })
}

export function ledgerTemplateCreate(params) {
    return request({
        url: '/fleet/ledgertemplate/create',
        method: 'post',
        data: params,
    })
}

// 获取月度工作平台模版（总部）
export function getLedgerTemplateMonthAll(params) {
    return request({
        url: '/fleet/ledgertemplate/monthall',
        method: 'post',
        data: params,
    })
}
// 月度工作平台模版（总部）分配模版
export function ledgerTemplateMonthAllAllocate(params) {
    return request({
        url: '/fleet/ledgertemplate/monthallchange',
        method: 'post',
        data: params,
    })
}

// 月度工作平台地区接口(获取地区模版)
export function getLedgerTemplateMonthArea(params) {
    return request({
        url: '/fleet/ledgertemplate/montharea',
        method: 'post',
        data: params,
    })
}

// 查询各地区上月持续未改善人数（地区台账上报页面-指定模板显示数量）
export function getAlarmPeopleCount(params) {
    return request({
        url: '/fleet/ledgerBoard/getAlarmPeopleCountByOrg',
        method: 'post',
        data: params,
    })
}

export function getLedgerList(params) {
    return request({
        url: '/fleet/ledger/getledgerlist',
        method: 'post',
        data: params,
    })
}

export function ledgerCreate(params) {
    return request({
        url: '/fleet/ledger/create',
        method: 'post',
        data: params,
    })
}

export function ledgerUp(params) {
    return request({
        url: '/fleet/ledger/upLedger',
        method: 'post',
        data: params,
    })
}

export function ledgerDelete(params) {
    return request({
        url: '/fleet/ledger/delLedger',
        method: 'post',
        data: params,
    })
}

export function ledgerReject(params) {
    return request({
        url: '/fleet/ledger/reject',
        method: 'post',
        data: params,
    })
}

export function templateSendOrBack(params) {
    return request({
        url: '/fleet/ledgertemplate/sendorback',
        method: 'post',
        data: params,
    })
}

export function templateDel(params) {
    return request({
        url: '/fleet/ledgertemplate/deltemplate',
        method: 'post',
        data: params,
    })
}

export function getFileUrlByFilename(query) {
    return request({
        url: '/fleet/ledgerfile/queryHWCloudFile',
        method: 'get',
        params: query,
    })
}

export function getLedgerCombineData(query) {
    return request({
        url: '/fleet/ledger/getMergeList',
        method: 'get',
        params: query,
    })
}

//组织机构懒加载
export function getNextOrgTreeList(query) {
    return request({
        url: '/fleet/ztcommon/getOrgTree',
        method: 'post',
        params: query,
    })
}

//组织框模糊搜索
export function getVagueOrgList(query) {
    return request({
        url: '/fleet/ztcommon/getVagueOrg',
        method: 'get',
        params: query,
    })
}
// 意见反馈保存
export function feedbackCreate(params) {
    return request({
        url: '/fleet/ledgerFeedback/add',
        method: 'post',
        data: params,
    })
}
// 意见反馈分页查询
export function getFeedbackList(params) {
    return request({
        url: '/fleet/ledgerFeedback/selectPage',
        method: 'post',
        data: params,
    })
}

// 查询安全管理KPI
export function getLedgerBoardKpi(params) {
    return request({
        url: '/fleet/ledgerBoard/getKPI',
        method: 'post',
        data: params,
    })
}

// 查询台账上报排行榜
export function getLedgerBoardRankList(params) {
    return request({
        url: '/fleet/ledgerBoard/getRankList',
        method: 'post',
        data: params,
    })
}

// 查询各报警持续未改善人员数量
export function getLedgerBoardAlarmPeopleList(params) {
    return request({
        url: '/fleet/ledgerBoard/getAlarmPeopleCountList',
        method: 'post',
        data: params,
    })
}

// 根据组织机构，月份查询台账上报环比
export function getLedgerBoardMonthChainList(params) {
    return request({
        url: '/fleet/ledgerBoard/getMonthChain',
        method: 'post',
        data: params,
    })
}

// 分页查询持续未改善人员明细
export function getLedgerBoardAlarmPeopleDetailList(params) {
    return request({
        url: '/fleet/ledgerBoard/getAlarmPeopleDetailPage',
        method: 'post',
        data: params,
    })
}