
import request from '@/router/axios'

export function fetchPageList(query) {
    return request({
        url: '/soc/customRiskModel/page',
        method: 'get',
        params: query
    })
}

export function getDetail(dataCode) {
    return request({
        url: '/soc/customRiskModel/detail',
        method: 'get',
        params: {
            dataCode
        }
    })
}

export function addModel(data) {
    return request({
        url: '/soc/customRiskModel/addModel ',
        method: 'post',
        data,
    })
}

export function updateModel(data) {
    return request({
        url: '/soc/customRiskModel/updateModel ',
        method: 'post',
        data,
    })
}

export function addRule(data) {
    return request({
        url: '/soc/customRiskModel/addRule',
        method: 'post',
        data,
    })
}

export function updateRule(data) {
    return request({
        url: '/soc/customRiskModel/updateRule',
        method: 'post',
        data,
    })
}

export function delRisk(dataCode) {
    return request({
        url: '/soc/customRiskModel/del?dataCode=' + dataCode,
        method: 'delete'
    })
}

