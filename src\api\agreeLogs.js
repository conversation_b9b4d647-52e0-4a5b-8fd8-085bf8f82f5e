import request from '@/router/axios'

export function queryLetterPage(data) {
    return request({
        url: '/fleet/safeletter/queryLetterPage',
        method: 'post',
        data,
    });
};

export function safeletter_export(data) {
    return request({
        url: '/fleet/safeletter/export',
        method: 'post',
        data,
        responseType: 'blob',
    });
}

export function queryHWCloudFile(params) {
    return request({
        url: '/fleet/safeletter/queryHWCloudFile',
        method: 'get',
        params,
    });
};


