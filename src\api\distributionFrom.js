import request from '@/router/axios'

// 分单规则分页
export function disRulePage(params) {
    return request({
        params,
        method: 'get',
        url: '/soc/disRule/disRulePage',
    })
}

// 分单规则分页
export function getProcessByRiskLevel(data) {
    return request({
        data,
        method: 'post',
        url: '/soc/disRule/getProcessByRiskLevel',
    })
}

// 分单规则分页
export function getList(params) {
    return request({
        params,
        method: 'get',
        url: '/soc/disRule/page',
    })
}

// 删除分担规则
export function removeDisRule(id) {
    return request({
        method: 'delete',
        url: `/soc/disRule/remove/${id}`,
    })
}

// 新增分单规则
export function saveDisRule(data) {
    return request({
        data,
        method: 'post',
        url: '/soc/disRule/save',
    })
}

// 编辑分单规则
export function updatDisRule(data) {
    return request({
        data,
        method: 'post',
        url: '/soc/disRule/update',
    })
}
