import request from '@/router/axios'
export function queryCarStatus(params) {
    return request({
        url: '/fleet/track/queryCarStatus',
        method: 'get',
        params,
    })
}

export function querySingleCarStatus(params) {
    return request({
        url: '/fleet/track/querySingleCarStatus',
        method: 'get',
        params,
    })
}

export function queryVagueCar(params) {
    return request({
        url: '/erp/vehicle/queryVagueCarInfo', // 接口地址更换/fleet/track/queryVagueCar
        method: 'get',
        params,
    })
}

// 发送语音
export function sendTTSMessage(data) {
    return request({
        url: '/fleet/ttsmsg/sendTTSMessage',
        method: 'post',
        data,
    })
}

// 发送语音批量
export function sendMsgRecord(data) {
    return request({
        url: `/fleet/MsgRecord`,
        method: 'post',
        data,
    })
}
