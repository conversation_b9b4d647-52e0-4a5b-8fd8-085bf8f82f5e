import request from '@/router/axios'

// 司机信息管理
export function queryDriverInfoPage(data) {
    return request({
        url: '/fleet/driver/selectPage',
        method: 'post',
        data
    })
}


// 司机信息导出
export function exportDriverInfo(data) {
    return request({
        url: '/fleet/driver/export',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}