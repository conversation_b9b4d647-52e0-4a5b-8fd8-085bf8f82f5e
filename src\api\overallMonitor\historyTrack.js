import request from '@/router/axios'

export function queryPointPosByGeo(query) {
    return request({
        url: '/fleet/track/queryGeoByXY',
        method: 'get',
        params: query,
    })
}

export function queryHisTrack(data) {
    return request({
        url: '/fleet/track/queryHisTrack',
        method: 'post',
        data,
    })
}

export function submitRiskMonitorCheck(query) {
    return request({
        url: '/alarmRecent/submitCheck',
        method: 'post',
        params: query,
    })
}

export function exportHisTrack(data) {
    return request({
        url: '/fleet/track/exportTrack',
        method: 'post',
        data,
        timeout: 1000000,
        // responseType: 'arraybuffer',
    })
}

export function searchCarNo(query) {
    return request({
        url: '/fleet/track/queryVagueCar',
        method: 'get',
        params: query,
    })
}

export function queryAlarmList(data) {
    return request({
        url: '/fleet/alarm/queryAlarmList',
        method: 'post',
        data,
    })
}
export function dictAlarmType(query) {
    return request({
        url: '/admin/dict/type/alarm_type',
        method: 'get',
        params: query,
    })
}

export function queryTrackSpeed(data) {
    return request({
        url: '/fleet/track/queryTrackSpeed',
        method: 'post',
        data,
    })
}
//司机登签信息查询
export function getDriverLogList(data) {
    return request({
        url: '/erp/driverLog/getDriverLogList',
        method: 'post',
        data,
    })
}

//在线明细查询
export function getOnlineRecord(data) {
    return request({
        url: '/fleet/track/onlineRecord',
        method: 'post',
        data,
    })
}
//车辆信息查询
export function getTrackVehicleInfo(query) {
    return request({
        url: '/erp/Vehicle/getTrackVehicleInfo',
        method: 'get',
        params: query,
    })
}

export function getDeviceChannle(imei) {
    return request({
        url: 'erp/Terminal/channel/' + imei,
        method: 'get',
    })
}
//在线明细查询
export function queryTrackStop(data) {
    return request({
        url: '/fleet/track/queryTrackStop',
        method: 'post',
        data,
    })
}
