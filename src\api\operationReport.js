import request from '@/router/axios'

export function fetchWeekMonthTotalityReportList(data) { // 运营报表列表
  let url = '/fleet/fleetWeekMonth/queryWeekMonthTotality';
  if (data.reportType === 'D') {
    url = '/fleet/fleetDaily/queryDailyTotality';
  }
  return request({
    url,
    method: 'post',
    data
  })
}
export function fetchMileWeekDayReportList(data) { // 查询客户维度七-30天内每天的总里程及报警数
  return request({
    url: '/fleet/fleetWeekMonth/queryMileWeekDay',
    method: 'post',
    data
  })
}
export function fetchRecent4WeekReportList(data) { // 查询近4周的指标
  return request({
    url: '/fleet/fleetWeekMonth/queryRecent4WeekDay',
    method: 'post',
    data
  })
}
export function fetchDefendCarReportList(data) { // 车辆护航情况分析(TOP15)排名
  let url = '/fleet/fleetWeekMonth/queryDefendCar';
  if (data.reportType === 'D') {
    url = '/fleet/fleetDaily/queryDefendCar';
  }
  return request({
    url,
    method: 'post',
    data
  })
}
export function fetchSafeCarTopReportList(data) { // 车辆安全驾驶排名前10
  let url = '/fleet/fleetWeekMonth/querySafeCarTop';
  if (data.reportType === 'D') {
    url = '/fleet/fleetDaily/querySafeCarTop';
  }
  return request({
    url,
    method: 'post',
    data
  })
}
export function fetchDriverSafeReportList(data) { // 查询司机风险行为分析
  return request({
    url: '/fleet/fleetWeekMonth/queryDriverSafe',
    method: 'post',
    data
  })
}
export function fetchAlarmHourReportList(data) { // 风险行为类型及时段分布
  let url = '/fleet/fleetWeekMonth/queryAlarmHour';
  if (data.reportType === 'D') {
    url = '/fleet/fleetDaily/queryAlarmHour';
  }
  return request({
    url,
    method: 'post',
    data
  })
}
export function updateReportDsc(data) { // 更新操作记录
  return request({
    url: '/fleet/fleetDaily/updateReportDsc',
    method: 'post',
    data
  })
}
export function fetchAllDeptList(query) { // 获取车队
  return request({
    url: '/fleet/security-manager/getDeptList',
    method: 'get',
    params: query
  })
}
export function fetchReportDscList(data) { // 查询报表反馈记录
  return request({
    url: '/fleet/fleetDaily/queryReportDsc',
    method: 'post',
    data
  })
}
