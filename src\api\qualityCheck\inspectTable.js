import request from '@/router/axios'

//提交质检结果
export function submitInspectResult(data) {
    return request({
        url: '/soc/inspection/order/result',
        method: 'post',
        data,
    })
}

// 详情查询
export function queryDetail(data) {
    return request({
        url: '/soc/inspection/order/detail',
        methods: 'get',
        params: data
    })
}

// 分页查询
export function queryPage(data) {
    return request({
      url: '/soc/inspection/order/page',
      method: 'get',
      params: data
    })
}

export function exportInspectList(data) {
    return request({
        url: '/soc/inspection/exportOrder',
        method: 'post',
        data,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}
