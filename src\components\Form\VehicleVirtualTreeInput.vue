<template>
  <VirtualTreeSelect lazy :load="loadFunction" @clear="clear" :value="carNo" @change="onChange" :inputClass="inputClass"
    v-loading="loading" :clearable="clearable" :placement="placement" :width="width" :filterMethod="filterMethod"
    :disabled="disabled" size="small" :placeholder="placeholder" :defaultNodeLabel="defaultNodeLabel" />
</template>

<script>
import { get } from 'lodash-es';
import { fetchTree } from "@/api/admin/dept";
import VirtualTreeSelect from "@/components/virtual-tree-select";
import { getStore, setStore } from '@/util/store'
import * as apis from '@/apis';
import { f } from '@/lib/VFormDesigner.umd';

const treeCacheKey = 'datacode_tree_cache';
export default {
  components: {
    VirtualTreeSelect,
  },
  props: {
    width: {
      type: [String, Number],
      default: 240,
    },
    inputClass: String,
    placement: String,
    disabled: {
      default: false,
      type: Boolean,
    },
    clearable: {
      type: <PERSON>olean,
      default: true,
    },
    childHiddenAllOption: {
      default: false,
      type: Boolean,
    },
    // isWithInit: {
    //   type: Boolean,
    //   default: false,
    // },
    modelValue: {
      type: [String, Number],
      default: "",
    },
    size: {
      type: String,
      default: "medium",
      validator: function (val) {
        return ["medium", "small", "mini"].includes(val);
      },
    },
    // defaultProps: {
    //   type: Object,
    //   default: () =>  ({
    //     label: "label",
    //     value: "value",
    //   }),
    // },
    placeholder: {
      type: String,
      default: () => "请选择车辆",
    },
    defaultNodeLabel: String,
  },
  model: {
    prop: "modelValue",
    event: "change",
  },
  data() {
    return {
      loading: false,
      // treeData: [],
      // dataCode: "",
      carNo: '',
    };
  },
  watch: {
    modelValue: {
      handler: function () {
        if (this.modelValue != this.carNo) {
          this.carNo = this.modelValue;
        }
      },
      immediate: true,
    },
    carNo() {
      this.$emit("change", this.carNo);

    },
  },
  created() {
    // this.init();
    console.log('VehicleVirtualTreeInput')
  },
  methods: {
    // init() {
    //   const treeDataCache = getStore({name: treeCacheKey});
    //   if (treeDataCache) {
    //     this.formatData(treeDataCache);
    //     return;
    //   }

    //   this.loading = true;
    //   fetchTree()
    //     .then((response) => {
    //       let res = response.data.data;
    //       this.formatData(res);
    //       setStore({
    //         name: treeCacheKey,
    //         content: [ ...this.treeData ],
    //         type: 'session'
    //       });
    //     })
    //     .finally(() => {
    //       this.loading = false;
    //     });
    // },
    // formatData(res) {
    //   const isNotHeadOrg = get(this.$store.state.user, 'userInfo.deptId') != 24;
    //   if (isNotHeadOrg && this.childHiddenAllOption && get(res, '0.dataCode') === "1000") {
    //     res = res[0].children;
    //     this.treeData = res;
    //   } else {
    //     this.treeData = res;
    //   }

    //   if (this.isWithInit) {
    //     this.dataCode = this.treeData[0].dataCode;
    //     this.$emit("change", this.dataCode);
    //     this.$emit("inited");
    //   }
    // },
    // filterMethod(query, node) {
    //   if (!query) {
    //     return true;
    //   }
    //   return node.label.includes(query);
    // },
    clear() {
      // global_tree_data = null;


    },
    onChange(value, node) {
      if (value.startsWith('dataCode:')) return;
      this.carNo = value;
      this.$emit("onGetNode", node);

    },
    async filterMethod(query, cb) {
      let data = [];
     
      if (!query) {
        console.log(global_tree_data, `global_tree_data`);
        global_tree_data.map(it => {
          it.hiddenExpandIcon = false;
          return it;
        });
        cb(global_tree_data);

        return;
      }
      try {
        const res = await apis.fleet.track.queryVagueCarInfo({ carNo: query });
        data = res.data.data.map(it => {
          it.value = it.carplate;
          it.label = it.carplate;
          it.hiddenExpandIcon = true;
          return it;
        });
      } finally {
       

        cb(data);
      }
    },
    async loadFunction(node, cb) {
      let children = [];
      try {
        if (node) {
          children = node.data.children;
          const res = await apis.fleet.track.queryDeptCar({ dataCode: node.data.dataCode });
          node.data.children = node.data.children.concat(res.data.data.map(it => {
            it.value = it.carplate;
            it.label = it.carplate;
            it.hiddenExpandIcon = true;
            return it;
          }));
          node.data.loaded = true;
          children = node.data.children;
          return;
        }
        // 初始化
        if (!global_tree_data) {
          this.loading = true;
         
          try {
            const res = await apis.admin.dept.tree();
            res.data.data.forEach(traverse);
            global_tree_data = res.data.data;
          } finally {
            this.loading = false;
          }
          function traverse(it) {
            it.label = it.name;
            it.value = 'dataCode:' + it.dataCode;
            (it.children || []).forEach(traverse);
          }
        }
        children = global_tree_data;
      } finally {
        cb(children);
      }
    },
  },
};

let global_tree_data = null;
</script>

<style lang="scss" scoped></style>
