const fs = require('fs')
const path = require('path')

const packageJson = require('../package.json')

// 生成版本号（格式：基础版本-时间戳）
const version = `${packageJson.version}-${new Date()
    .toISOString()
    .replace(/[:T.-]/g, '')
    .slice(0, 14)}`

// 写入到 .env 文件（供 Vue CLI 读取）
const envContent = `VUE_APP_VERSION=${version}\n`
fs.writeFileSync(path.resolve(__dirname, '../.env'), envContent)

console.log(`[版本号更新] ${version}`)
