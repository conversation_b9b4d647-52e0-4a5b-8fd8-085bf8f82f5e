import request from '@/router/axios'

export function querySuspeaccidentList(params) {
    return request({
        url: '/soc/suspeaccident/page',
        method: 'post',
        data: params
    })
}

export function exportSuspeaccident(params) {
    return request({
        url: '/soc/suspeaccident/export',
        method: 'post',
        data: params,
        timeout: 1000000,
        responseType: 'arraybuffer'
    })
}

export function auditSuspeaccident(params) {
    return request({
        url: '/soc/suspeaccident/audit',
        method: 'post',
        data: params
    })
}
