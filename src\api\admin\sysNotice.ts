import request from '@/router/axios'
/**
 * 新增公告。
 */
export function add(obj?: Object) {
  return request({
    url: '/admin/Notice',
    method: 'post',
    data: obj
  })
}
// 修改公告
export function update(obj?: Object) {
  return request({
    url: '/admin/Notice',
    method: 'put',
    data: obj
  })
}
/**
 分页查询-我发布的
 */
export function getList(query?: Object) {
  return request({
    url: '/admin/Notice/pageNotice',
    method: 'get',
    params: query
  })
}
/**
 分页查询-我收到的
 */
export function getMyList(query?: Object) {
  return request({
    url: '/admin/Notice/pageNotifier',
    method: 'get',
    params: query
  })
}


/**
 * 查询通告详情
 */
export function getDetail(id: number) {
  return request({
    url: '/admin/Notice/' + id,
    method: 'get'
  })
}
/**
 *  查询当前用户未读数量
 */
export function getNoticeCount() {
  return request({
    url: '/admin/Notice/noticeCount',
    method: 'get'
  })
}
// 更新公告已读状态
export function updateStatus(obj?: Object) {
  return request({
    url: '/admin/Notice/updateStatus',
    method: 'post',
    data: obj
  })
}

// 获取所有用户
export function getAllUser() {
  return request({
    url: '/admin/user/getAll',
    method: 'get'
  })
}




export function del(ids?: Object) {
  return request({
    url: '/admin/Notice',
    method: 'delete',
    data: ids
  })
}





