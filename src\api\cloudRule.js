import request from '@/router/axios'

export function queryCloudRuleList(params) {
    return request({
        url: '/soc/cloudRule/page',
        method: 'get',
        params
    })
}

export function addCloudRule(params) {
    return request({
        url: '/soc/cloudRule/save',
        method: 'post',
        data: params,
    })
}

export function editCloudRule(params) {
    return request({
        url: '/soc/cloudRule/update',
        method: 'post',
        data: params,
    })
}

export function delCloudRule(id) {
    return request({
        url: `/soc/cloudRule/del/${id}`,
        method: 'delete',
    })
}

