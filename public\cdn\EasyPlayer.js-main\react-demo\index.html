<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>React Player</title>
    <!-- React 和 ReactDOM CDN -->
    <script src="https://unpkg.com/react@18/umd/react.development.js" crossorigin></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js" crossorigin></script>
    <script src="https://cdn.jsdelivr.net/npm/babel-standalone@6.26.0/babel.min.js"></script>
    <script src="./js/EasyPlayer-pro.js"></script>
    <style>
        .player_container {
            width: 640px;
            height: 360px;
            background-color: black;
            margin: 10px auto;
        }
    </style>
</head>

<body>
    <div id="root"></div>
    <script type="text/babel" src="./player.jsx"></script>
</body>

</html>